version: '3'

services:
  mysql:
    image: mysql:5.7
    container_name: mysql57
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: boleai
      MYSQL_USER: boleai
      MYSQL_PASSWORD: boleaiboleai
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "--password=$$MYSQL_ROOT_PASSWORD" ]
      interval: 5s
      timeout: 3s
      retries: 10
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - app-net
  api:
    container_name: boleai-api
    image: alpine:latest
    working_dir: /app
    volumes:
      - ./backend:/app
    command: ["/app/boleai"]
    ports:
      - "8010:8010"
    depends_on:
      mysql:
        condition: service_healthy  # 关键：等待MySQL健康状态
    networks:
      - app-net
  nginx:
    container_name: nginx-proxy
    image: nginx:alpine
    ports:
      - "8080:8080"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./dist:/usr/share/nginx/html:ro
    depends_on:
      - api
    networks:
      - app-net
volumes:
  mysql-data:

networks:
  app-net:
    driver: bridge