package main

import (
	"fmt"
	"os"
	"strings"
	"text/template"
	"unicode"
)

func main() {
	if len(os.Args) < 3 || os.Args[1] != "new" {
		fmt.Println("Usage: service new <Name>")
		return
	}

	name := os.Args[2]
	capName := toExportedName(name) // 首字母大写
	interfaceName := capName + "Service"
	structName := toStructName(name) + "Service"
	methodName := "New" + interfaceName
	fileName := fmt.Sprintf("internal/service/%s.go", strings.ToLower(name))

	// 1️⃣ 生成独立 service 文件
	tpl := `package service

import (
	"bole-ai/internal/repository"
	goRedis "github.com/go-redis/redis/v8"
	"bole-ai/internal/model"
)

// {{.InterfaceName}} interface
type {{.InterfaceName}} interface {
	// TODO: add methods
}

// {{.StructName}} implements {{.InterfaceName}}
type {{.StructName}} struct {
	repo repository.{{.CapName}}Repository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ {{.InterfaceName}} = (*{{.StructName}})(nil)

// constructor
func new{{.InterfaceName}}(repo repository.{{.CapName}}Repository, rdb *goRedis.Client, conf *model.Conf) {{.InterfaceName}} {
	return &{{.StructName}}{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}
`

	writeFile(fileName, tpl, map[string]string{
		"InterfaceName": interfaceName,
		"StructName":    structName,
		"CapName":       capName,
	})

	// 2️⃣ 追加到 service.go 的接口和 struct 方法
	serviceFile := "internal/service/service.go"
	appendServiceInterfaceAndMethod(serviceFile, methodName, interfaceName, structName)
	fmt.Println("✅ Done! Generated", fileName, "and updated service.go")
}

// 首字母大写
func capitalize(s string) string {
	if s == "" {
		return s
	}
	runes := []rune(s)
	runes[0] = unicode.ToUpper(runes[0])
	return string(runes)
}

// 写文件
func writeFile(path, tpl string, data map[string]string) {
	if _, err := os.Stat(path); err == nil {
		fmt.Println("File already exists, skip:", path)
		return
	}
	f, err := os.Create(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	t := template.Must(template.New("tpl").Parse(tpl))
	t.Execute(f, data)
	fmt.Println("Generated:", path)
}

// 追加接口方法和 struct 方法
func appendServiceInterfaceAndMethod(serviceFile, methodName, interfaceName, structName string) {
	content, err := os.ReadFile(serviceFile)
	if err != nil {
		panic(err)
	}

	lines := strings.Split(string(content), "\n")
	var out []string
	insertedInterface := false
	var inFunc bool
	var braceCount int
	var funcStarted bool
	inserted := false

	for _, line := range lines {
		out = append(out, line)

		// 1️⃣ 追加接口方法
		if strings.Contains(line, "type Service interface {") && !insertedInterface {
			exists := false
			for _, l := range lines {
				if strings.Contains(l, methodName+"()") {
					exists = true
					break
				}
			}
			if !exists {
				out = append(out, "\t"+methodName+"() "+interfaceName)
				insertedInterface = true
			}
		}

		// 2️⃣ 检查 NewService 函数
		if strings.HasPrefix(strings.TrimSpace(line), "func NewService(") {
			inFunc = true
			funcStarted = false
			braceCount = 0
			continue
		}

		if inFunc {
			// 等到真正的 { 出现，函数体才开始
			if !funcStarted && strings.Contains(line, "{") {
				funcStarted = true
				braceCount += strings.Count(line, "{")
				braceCount -= strings.Count(line, "}")
				continue
			}

			if funcStarted {
				braceCount += strings.Count(line, "{")
				braceCount -= strings.Count(line, "}")

				if braceCount == 0 && !inserted {
					// NewService 完全闭合，追加方法
					out = append(out, fmt.Sprintf("\nfunc (srv *service) %s() %s {\n\treturn new%s(srv.repo, srv.rdb, srv.conf)\n}", methodName, interfaceName, interfaceName))
					inFunc = false
					inserted = true
				}
			}
		}
	}

	err = os.WriteFile(serviceFile, []byte(strings.Join(out, "\n")), 0644)
	if err != nil {
		panic(err)
	}
}

func toExportedName(name string) string {
	// 支持多个单词，首字母大写
	parts := strings.FieldsFunc(name, func(r rune) bool {
		return r == '_' || r == '-' || r == ' '
	})
	for i := range parts {
		if len(parts[i]) > 0 {
			parts[i] = strings.ToUpper(parts[i][:1]) + parts[i][1:]
		}
	}
	return strings.Join(parts, "")
}

func toStructName(entity string) string {
	// 将首字母小写，其余单词首字母大写
	runes := []rune(toExportedName(entity)) // 使用前面驼峰首字母大写函数
	if len(runes) == 0 {
		return ""
	}
	runes[0] = unicode.ToLower(runes[0])
	return string(runes)
}
