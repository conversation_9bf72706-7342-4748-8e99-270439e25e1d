package main

import (
	"fmt"
	"os"
	"strings"
	"text/template"
	"unicode"
)

func main() {
	if len(os.Args) < 3 || os.Args[1] != "new" {
		fmt.Println("Usage: repo new <Name>")
		return
	}

	name := os.Args[2]
	interfaceName := fmt.Sprintf("%sRepository", toExportedName(name))
	structName := toStructName(name) + "Repository"
	methodName := "New" + interfaceName
	fileName := fmt.Sprintf("internal/repository/%s.go", strings.ToLower(name))

	// 1️⃣ 生成单文件：接口 + 实现
	tpl := `package repository

import "bole-ai/internal/ent"

// {{.InterfaceName}} interface
type {{.InterfaceName}} interface {
	// TODO: add methods
}

// {{.StructName}} implements {{.InterfaceName}}
type {{.StructName}} struct {
	client *ent.Client
}

// constructor
func new{{.InterfaceName}}(client *ent.Client) {{.InterfaceName}} {
	return &{{.StructName}}{client: client}
}

// compile-time check: ensure struct implements interface
var _ {{.InterfaceName}} = (*{{.StructName}})(nil)
`

	writeFile(fileName, tpl, map[string]string{
		"InterfaceName": interfaceName,
		"StructName":    structName,
	})

	// 2️⃣ 追加到 repository.go
	repoFile := "internal/repository/repository.go"
	appendRepoInterfaceAndMethod(repoFile, methodName, interfaceName)
	fmt.Println("✅ Done! Generated", fileName, "and updated repository.go")
}

func writeFile(path, tpl string, data map[string]string) {
	if _, err := os.Stat(path); err == nil {
		fmt.Println("File already exists, skip:", path)
		return
	}
	f, err := os.Create(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	t := template.Must(template.New("tpl").Parse(tpl))
	t.Execute(f, data)
	fmt.Println("Generated:", path)
}

func appendRepoInterfaceAndMethod(repoFile, methodName, interfaceName string) {
	content, err := os.ReadFile(repoFile)
	if err != nil {
		panic(err)
	}

	lines := strings.Split(string(content), "\n")
	var out []string
	insertedInterface := false
	insertedMethod := false
	var inNewRepoFunc bool
	var braceCount int

	for _, line := range lines {
		out = append(out, line)

		// 追加接口方法
		if strings.Contains(line, "type Repository interface {") && !insertedInterface {
			exists := false
			for _, l := range lines {
				if strings.Contains(l, methodName+"()") {
					exists = true
					break
				}
			}
			if !exists {
				out = append(out, "\t"+methodName+"() "+interfaceName)
				insertedInterface = true
			}
		}

		// 2️⃣ 检查 NewRepository 函数
		trim := strings.TrimSpace(line)
		if strings.HasPrefix(trim, "func NewRepository(") {
			inNewRepoFunc = true
			braceCount = 0
		}

		if inNewRepoFunc {
			braceCount += strings.Count(line, "{")
			braceCount -= strings.Count(line, "}")
			if braceCount == 0 {
				// 函数结束，追加方法
				if !insertedMethod {
					out = append(out, fmt.Sprintf("\nfunc (repo *repository) %s() %s {\n\treturn new%s(repo.client)\n}", methodName, interfaceName, interfaceName))
					insertedMethod = true
				}
				inNewRepoFunc = false
			}
		}
	}

	err = os.WriteFile(repoFile, []byte(strings.Join(out, "\n")), 0644)
	if err != nil {
		panic(err)
	}
}

func toExportedName(name string) string {
	// 支持多个单词，首字母大写
	parts := strings.FieldsFunc(name, func(r rune) bool {
		return r == '_' || r == '-' || r == ' '
	})
	for i := range parts {
		if len(parts[i]) > 0 {
			parts[i] = strings.ToUpper(parts[i][:1]) + parts[i][1:]
		}
	}
	return strings.Join(parts, "")
}

func toStructName(entity string) string {
	// 将首字母小写，其余单词首字母大写
	runes := []rune(toExportedName(entity)) // 使用前面驼峰首字母大写函数
	if len(runes) == 0 {
		return ""
	}
	runes[0] = unicode.ToLower(runes[0])
	return string(runes)
}
