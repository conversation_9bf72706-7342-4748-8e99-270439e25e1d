package main

import (
	"bole-ai/internal/cache"
	"bole-ai/internal/config"
	"bole-ai/internal/database"
	"bole-ai/internal/httptransport"
	"bole-ai/internal/httptransport/token"
	"bole-ai/internal/repository"
	"bole-ai/internal/service"
	"context"
	"errors"
	"log"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"time"
)

func main() {
	file, err := os.OpenFile("app.log", os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		log.Fatal(err)
	}
	defer file.Close()

	slog.SetDefault(slog.New(slog.NewJSONHandler(file, &slog.HandlerOptions{
		Level: slog.LevelInfo,
	})))

	conf := config.NewViperConfig("./")
	slog.Info("config:", "conf", conf)
	client, err := database.NewMySQLClient(conf.Mysql)
	if err != nil {
		log.Fatal(err)
	}

	tokenMaker, err := token.NewJWTMaker(conf.Http.Jwt.Signature)
	if err != nil {
		log.Fatalf("cannot create token maker: %v", err)
	}

	repo := repository.NewRepository(client)

	rdb, err := cache.NewRedisClient(conf.Redis)
	if err != nil {
		log.Fatalf("NewRedisClient error: %v", err)
	}
	srv := service.NewService(conf, repo, rdb)

	h := httptransport.NewHandler(conf, tokenMaker, srv)

	hSrv := &http.Server{
		Addr:    conf.Http.Addr,
		Handler: h,
	}

	go func() {
		// 服务连接
		if err := hSrv.ListenAndServe(); err != nil && !errors.Is(err, http.ErrServerClosed) {
			log.Fatalf("listen: %s", err)
		}
	}()

	slog.Info("the http serve listen", "on", conf.Http.Addr)
	// 等待中断信号以优雅地关闭服务器（设置 5 秒的超时时间）
	quit := make(chan os.Signal)
	signal.Notify(quit, os.Interrupt)
	<-quit

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := hSrv.Shutdown(ctx); err != nil {
		log.Fatal("Server Shutdown:", err)
	}
}
