package response

import (
	"bole-ai/internal/model"
	"time"
)

type JwtToken struct {
	Token     string    `json:"token"`
	ExpiredAt time.Time `json:"expired_at"`
}

type UserLogin struct {
	Username string   `json:"username"`
	JwtToken JwtToken `json:"jwt_token"`
}

type User struct {
	Id        int    `json:"id"`
	Username  string `json:"username"`
	TestCode  string `json:"test_code"`
	BatchName string `json:"batch_name"`
	Email     string `json:"email"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
	Status    int    `json:"status"`
}

type ListUser struct {
	Pagination model.Pagination `json:"pagination"`
	List       []User           `json:"list"`
}
