package response

import (
	"bole-ai/internal/model"
)

type Conversation struct {
	Id             int                   `json:"id"`
	Name           string                `json:"name"`
	CreatedAt      string                `json:"created_at,omitempty"`
	UpdatedAt      string                `json:"updated_at,omitempty"`
	ConversationId string                `json:"conversation_id,omitempty"`
	AiAgent        AiAgent               `json:"ai_agent,omitempty"`
	Contents       []ConversationContent `json:"contents,omitempty"`
}

type ConversationContent struct {
	Type      int8   `json:"type"`
	Content   string `json:"content"`
	CreatedAt string `json:"created_at"`
}

type ListConversation struct {
	Pagination model.Pagination `json:"pagination"`
	List       []Conversation   `json:"list"`
}
