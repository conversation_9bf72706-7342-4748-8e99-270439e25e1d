package response

import "bole-ai/internal/model"

type InternalMessage struct {
	Id        int    `json:"id"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	Status    int8   `json:"status"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

type ListInternalMessage struct {
	Pagination model.Pagination  `json:"pagination"`
	List       []InternalMessage `json:"list"`
}
