package response

import (
	"bole-ai/internal/ent/schema"
	"bole-ai/internal/model"
)

type AiAgent struct {
	Id          int                   `json:"id"`
	Name        string                `json:"name,omitempty"`
	Icon        string                `json:"icon,omitempty"`
	Target      string                `json:"target"`
	Method      string                `json:"method"`
	Secret      string                `json:"secret"`
	Guide       string                `json:"guide"`
	Description string                `json:"description,omitempty"`
	Param       []schema.AiAgentParam `json:"param"`
	Status      int8                  `json:"status"`
}

type ListAiAgent struct {
	Pagination model.Pagination `json:"pagination"`
	List       []AiAgent        `json:"list"`
}
