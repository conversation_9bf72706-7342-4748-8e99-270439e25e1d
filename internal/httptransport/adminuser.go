package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	response2 "bole-ai/internal/httptransport/response"
	"bole-ai/internal/httptransport/token"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) adminLogin(ctx *gin.Context) {
	var req request.AdminUserLogin
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}

	res, err := h.srv.NewAdminUserService().Login(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	accessToken, accessPayload, err := h.tokenMaker.CreateToken(
		res.Username,
		h.conf.Http.Jwt.Duration,
	)
	ctx.JSON(http.StatusOK, response(response2.UserLogin{
		Username: res.Username,
		JwtToken: response2.JwtToken{
			Token:     accessToken,
			ExpiredAt: accessPayload.ExpiredAt,
		},
	}, ""))
}

func (h *handle) adminLogout(ctx *gin.Context) {
	_, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) adminUser(ctx *gin.Context) {
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}
	p := v.(*token.Payload)
	adminuser, err := h.srv.NewAdminUserService().GetByUsername(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, gin.H{
		"message": "",
		"data": gin.H{
			"id":       adminuser.ID,
			"username": p.UserID,
		},
	})
}
