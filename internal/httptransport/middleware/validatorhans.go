package middleware

import (
	bv "bole-ai/internal/validator"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
)

// TranslateError 把验证错误翻译成中文 map[string]string
func TranslateError(err error) map[string]string {
	res := map[string]string{}

	if errs, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range errs {
			res[fieldErr.Field()] = fieldErr.Translate(bv.Trans)
		}
	} else {
		res["error"] = err.Error()
	}

	return res
}

// BindAndValidate 统一处理绑定和校验，自动翻译错误
func BindAndValidate(c *gin.Context, obj any) bool {
	if err := c.ShouldBind(obj); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"msg":   "参数错误",
			"error": TranslateError(err),
		})
		return false
	}
	return true
}
