package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/token"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
)

func (h *handle) listUserInternalMessage(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")
	statusStr := ctx.Query("status")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)
	status := cast.ToInt(statusStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	list, err := h.srv.NewUserInternalMessageService().List(ctx, page, perPage, user.ID, status)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) showUserInternalMessage(ctx *gin.Context) {
	id := cast.ToInt(ctx.Param("id"))
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	show, err := h.srv.NewUserInternalMessageService().Show(ctx, user.ID, id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.DataNotFound))
		return
	}
	ctx.JSON(http.StatusOK, response(show, ""))
}
