package request

type AddScreenplay struct {
	UserId    int    `json:"-"`
	ProjectId int    `json:"project_id" binding:"required"`
	Title     string `json:"title" binding:"required,min=2"`
	Content   string `json:"content"`
	Type      int    `json:"type" binding:"required,oneof=1 2 3"`
}

type UpdateScreenplay struct {
	UserId    int    `json:"-"`
	ProjectId int    `json:"project_id" binding:"required"`
	Title     string `json:"title" binding:"required,min=2"`
	Content   string `json:"content"`
}
