package request

type UserRegister struct {
	Username        string `json:"username" binding:"required,alpha,min=3,max=50"`
	Password        string `json:"password" binding:"required,min=6,max=50"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
	Email           string `json:"email" binding:"required,email"`
	TestCode        string `json:"test_code" binding:"required"`
	TestCodeId      int    `json:"-"`
	BatchId         int    `json:"-"`
}

type UserLogin struct {
	Username string `json:"username" binding:"required,min=3,max=20"`
	Password string `json:"password" binding:"required,min=6,max=50"`
}

type ForgetPasswordRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type ResetPasswordRequest struct {
	Email           string `json:"email" binding:"required,email"`
	Password        string `json:"password" binding:"required,min=6,max=50"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
	Code            string `json:"code" binding:"required"`
}

type SetPasswordRequest struct {
	Username        string `json:"-"`
	OldPassword     string `json:"old_password" binding:"required,min=6,max=50"`
	Password        string `json:"password" binding:"required,min=6,max=50"`
	ConfirmPassword string `json:"confirm_password" binding:"required,eqfield=Password"`
}
