package request

import (
	"bole-ai/internal/model"
)

type ChatMessage struct {
	Query          string         `json:"query"`
	ScreenplayId   int            `json:"screenplay_id"`
	ConversationId string         `json:"conversation_id"`
	R              model.ChatRole `json:"r" binding:"oneof=0 1 2 3"`
	G              model.ChatG    `json:"g" binding:"oneof=0 1 2"`
}

type ChatMessageV2 struct {
	AiAgentId      int            `json:"ai_agent_id" binding:"required"`
	Query          string         `json:"query" binding:"required"`
	FileContent    string         `json:"file_content"`
	ConversationId string         `json:"conversation_id"`
	Param          map[string]any `json:"param"`
}
