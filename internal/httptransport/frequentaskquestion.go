package httptransport

import (
	"bole-ai/internal/httptransport/request"
	"github.com/gin-gonic/gin"
	"net/http"
)

func (h *handle) addFrequentAskQuestion(ctx *gin.Context) {
	var req request.AddFrequentAskQuestion
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	_, err := h.srv.NewFrequentlyAskedQuestionsService().Add(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) getLatestFrequentAskQuestion(ctx *gin.Context) {
	f, err := h.srv.NewFrequentlyAskedQuestionsService().GetLatest(ctx)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(f, ""))
}
