package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/token"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
)

func (h *handle) listScreenplay(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")
	projectIdStr := ctx.Query("project_id")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)
	projectId := cast.ToInt(projectIdStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	list, err := h.srv.NewScreenplayService().List(ctx, page, perPage, projectId, user.ID)
	if err != nil {
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) addScreenplay(ctx *gin.Context) {
	var req request.AddScreenplay
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	req.UserId = user.ID
	s, err := h.srv.NewScreenplayService().Add(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(s, ""))
}

func (h *handle) updateScreenplay(ctx *gin.Context) {
	id := ctx.Param("id")
	var req request.UpdateScreenplay
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	req.UserId = user.ID
	err = h.srv.NewScreenplayService().Update(ctx, cast.ToInt(id), req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) showScreenplay(ctx *gin.Context) {
	id := ctx.Param("id")
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	show, err := h.srv.NewScreenplayService().Show(ctx, cast.ToInt(id), user.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(show, ""))
}
