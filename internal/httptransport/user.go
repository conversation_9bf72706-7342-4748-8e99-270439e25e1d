/**
 * @Author: 1cool
 * @Author: 1cool.vip
 * @Date: 2023/5/12 8:10
 * @Desc:
 */

package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	response2 "bole-ai/internal/httptransport/response"
	"bole-ai/internal/httptransport/token"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
	"strings"
	"time"
)

func (h *handle) user(ctx *gin.Context) {
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}
	p := v.(*token.Payload)
	ctx.JSON(http.StatusOK, gin.H{
		"message": "",
		"data": gin.H{
			"username": p.UserID,
		},
	})
}

func (h *handle) register(ctx *gin.Context) {
	var req request.UserRegister
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	code, err := h.srv.NewTestCodeService().GetByCode(ctx, strings.TrimSpace(req.TestCode))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	if code.Status != 1 {
		ctx.JSON(http.StatusInternalServerError, response(nil, "无效的code"))
		return
	}
	if code.ExpiredAt.Before(time.Now()) {
		ctx.JSON(http.StatusInternalServerError, response(nil, "测试码已过期"))
		return
	}
	req.TestCodeId = code.ID
	req.BatchId = code.Edges.Batch.ID
	_, err = h.srv.NewUserService().Register(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) login(ctx *gin.Context) {
	var req request.UserLogin
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	user, err := h.srv.NewUserService().Login(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	accessToken, accessPayload, err := h.tokenMaker.CreateToken(
		user.Username,
		h.conf.Http.Jwt.Duration,
	)
	ctx.JSON(http.StatusOK, response(response2.UserLogin{
		Username: user.Username,
		JwtToken: response2.JwtToken{
			Token:     accessToken,
			ExpiredAt: accessPayload.ExpiredAt,
		},
	}, ""))
}

func (h *handle) logout(ctx *gin.Context) {
	_, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取用户信息失败",
		})
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) userList(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")
	email := ctx.Query("email")
	status := ctx.Query("status")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	list, err := h.srv.NewUserService().List(ctx, page, perPage, cast.ToInt(status), email)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) setStatus(ctx *gin.Context) {
	id := cast.ToInt(ctx.Param("id"))
	err := h.srv.NewUserService().SetStatus(ctx, id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) ForgetPassword(ctx *gin.Context) {
	var req request.ForgetPasswordRequest
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	err := h.srv.NewUserService().ForgetPassword(ctx, req.Email)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) ResetPassword(ctx *gin.Context) {
	var req request.ResetPasswordRequest
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	err := h.srv.NewUserService().ResetPassword(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) SetPassword(ctx *gin.Context) {
	var req request.SetPasswordRequest
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	req.Username = p.UserID
	err := h.srv.NewUserService().SetPassword(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}
