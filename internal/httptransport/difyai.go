package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/token"
	"bole-ai/internal/model"
	"bufio"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"io"
	"log/slog"
	"net/http"
	"strings"
)

func (h *handle) chatMessage(ctx *gin.Context) {
	var in request.ChatMessage
	if err := ctx.ShouldBindJSON(&in); err != nil {
		ctx.JSON(http.StatusBadRequest, response(nil, err.Error()))
		return
	}
	if in.Query == "" {
		ctx.JSON(http.StatusBadRequest, response(nil, "Query param is empty"))
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	var (
		secret string
		inputs = `{}`
	)
	isGemini := in.G == model.ChatGemini

	switch in.R {
	case model.ChatRolePlanner:
		if isGemini {
			secret = h.conf.Chat.Planner.Gemini
		} else {
			secret = h.conf.Chat.Planner.GLM
		}
	case model.ChatRolePlaywright:
		if isGemini {
			secret = h.conf.Chat.Playwright.Gemini
		} else {
			secret = h.conf.Chat.Playwright.GLM
		}
		screenplay, err := h.srv.NewScreenplayService().Show(ctx, in.ScreenplayId, user.ID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, response(nil, internal.ChatAIError))
			return
		}
		content := ExtractPureText(screenplay.Content)
		if screenplay.Type != 1 {
			ctx.JSON(http.StatusBadRequest, response(nil, "not is storyline"))
			return
		}
		if content == "" {
			ctx.JSON(http.StatusBadRequest, response(nil, "storyline is empty"))
			return
		}
		inputs = fmt.Sprintf(`{"user_storyline":"%s"}`, content)
	case model.ChatRoleEditor:
		if isGemini {
			secret = h.conf.Chat.Editor.Gemini
		} else {
			secret = h.conf.Chat.Editor.GLM
		}
		screenplay, err := h.srv.NewScreenplayService().Show(ctx, in.ScreenplayId, user.ID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, response(nil, internal.ChatAIError))
			return
		}
		content := ExtractPureText(screenplay.Content)
		if screenplay.Type != 2 {
			ctx.JSON(http.StatusBadRequest, response(nil, "not is user_script"))
			return
		}
		inputs = fmt.Sprintf(`{"user_script":"%s"}`, content)
	default:
		if isGemini {
			secret = h.conf.Chat.Planner.Gemini
		} else {
			secret = h.conf.Chat.Planner.GLM
		}
	}
	if secret == "" {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.ChatAIError))
		return
	}
	// 设置 SSE 响应头
	ctx.Writer.Header().Set("Content-Type", "text/event-stream")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")
	ctx.Writer.Header().Set("Access-Control-Allow-Origin", "*") // 如需跨域

	flusher, ok := ctx.Writer.(http.Flusher)
	if !ok {
		ctx.String(http.StatusInternalServerError, "Streaming not supported")
		return
	}

	payload := fmt.Sprintf(`{
			"inputs": %s,
			"query": "%s",
			"response_mode": "streaming",
			"user": "%s",
			"conversation_id": "%s"
		}`, inputs, in.Query, p.UserID, in.ConversationId)

	client := &http.Client{
		Timeout: 0, // SSE 是长连接，不能设置超时
	}
	req, err := http.NewRequest(http.MethodPost, "https://dify.ai-role.cn/v1/chat-messages", strings.NewReader(payload))
	if err != nil {
		ctx.String(http.StatusInternalServerError, "Failed to create request")
		return
	}
	req.Header.Add("Content-Type", "application/json")
	// 设置请求头，声明支持 SSE
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Add("Authorization", "Bearer "+secret)

	resp, err := client.Do(req)
	if err != nil {
		ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
			return
		}
		slog.Error("chatMessageError", "err", err, "response", string(body), "payload", payload, "secret", secret, "in", in)
		ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
		return
	}
	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		// 写入原始 SSE 数据
		fmt.Fprintf(ctx.Writer, "%s\n", line)
		// 写入空行作为事件分隔（SSE 协议要求）
		if line == "" {
			fmt.Fprint(ctx.Writer, "\n")
		}
		flusher.Flush()
	}
	if err := scanner.Err(); err != nil {
		slog.Error("chatMessageError", "payload", payload)
		fmt.Fprintf(ctx.Writer, "event: error\ndata: %v\n\n", err)
		flusher.Flush()
	}
}

func (h *handle) chatMessageV2(ctx *gin.Context) {
	var in request.ChatMessageV2
	if err := ctx.ShouldBindJSON(&in); err != nil {
		ctx.JSON(http.StatusBadRequest, response(nil, err.Error()))
		return
	}
	in.Query = fmt.Sprintf("%s%s", in.FileContent, in.Query)
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	aiAgent, err := h.srv.NewAiAgentService().GetById(ctx, in.AiAgentId)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, response(nil, err.Error()))
		return
	}
	if aiAgent.Target == "" || aiAgent.Secret == "" {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.AiAgentError))
		return
	}
	if aiAgent.Status == 2 {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.AiAgentOffline))
		return
	}

	var conversation *ent.Conversation
	if in.ConversationId != "" {
		conversation, err = h.srv.NewConversationService().GetByConversationId(ctx, in.ConversationId, user.ID)
		if err != nil {
			ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
			return
		}
	}

	// 设置 SSE 响应头
	ctx.Writer.Header().Set("Content-Type", "text/event-stream")
	ctx.Writer.Header().Set("Cache-Control", "no-cache")
	ctx.Writer.Header().Set("Connection", "keep-alive")
	ctx.Writer.Header().Set("Access-Control-Allow-Origin", "*") // 如需跨域

	flusher, ok := ctx.Writer.(http.Flusher)
	if !ok {
		ctx.String(http.StatusInternalServerError, "Streaming not supported")
		return
	}

	inputs := make(map[string]any)
	for field, value := range in.Param {
		inputs[field] = value
	}

	payload := model.Payload{
		Inputs:         inputs,
		Query:          in.Query,
		ResponseMode:   "streaming",
		User:           p.UserID,
		ConversationId: in.ConversationId,
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		ctx.String(http.StatusInternalServerError, err.Error())
		return
	}
	client := &http.Client{
		Timeout: 0, // SSE 是长连接，不能设置超时
	}

	req, err := http.NewRequest(http.MethodPost, aiAgent.Target, strings.NewReader(string(jsonPayload)))
	if err != nil {
		ctx.String(http.StatusInternalServerError, "Failed to create request")
		return
	}
	req.Header.Add("Content-Type", "application/json")
	// 设置请求头，声明支持 SSE
	req.Header.Set("Accept", "text/event-stream")
	req.Header.Add("Authorization", "Bearer "+aiAgent.Secret)

	resp, err := client.Do(req)
	if err != nil {
		slog.Error("chatMessageV2", "payload", payload, "err", err)
		ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
		return
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
			return
		}
		slog.Error("chatMessageError", "err", err, "response", string(body), "payload", payload, in)
		ctx.String(http.StatusInternalServerError, "Failed to connect to SSE source")
		return
	}
	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		line := scanner.Text()
		go func(line string) {
			if strings.HasPrefix(line, "data: ") && strings.Contains(line, "workflow_started") {
				data := strings.TrimPrefix(line, "data: ")
				var c model.AiAgentConversation
				err = json.Unmarshal([]byte(data), &c)
				if err != nil {
					slog.Error("ParseChatMessageError", "err", err, "data", data)
					return
				}
				if conversation == nil {
					runes := []rune(in.Query)
					name := in.Query
					if len(runes) > 10 {
						name = string(runes[:10])
					}
					conversation, err = h.srv.NewConversationService().Add(ctx, request.AddConversation{
						Name:           name,
						UserId:         user.ID,
						AiAgentId:      aiAgent.ID,
						ConversationId: c.ConversationID,
					})
					if err != nil {
						slog.Error("AddChatMessageConversationError", "err", err, "data", c.Data)
						return
					}
				}
			}
			if strings.HasPrefix(line, "data: ") && strings.Contains(line, "workflow_finished") {
				data := strings.TrimPrefix(line, "data: ")
				var c model.AiAgentConversation
				err = json.Unmarshal([]byte(data), &c)
				if err != nil {
					slog.Error("ParseChatMessageError", "err", err, "data", data)
					return
				}
				_, err = h.srv.NewConversationContentService().Add(ctx, request.AddConversationContent{
					ConversationId: conversation.ID,
					Content:        in.Query,
					Type:           1,
				})
				if err != nil {
					slog.Error("AddChatMessageUserContentError", "err", err, "data", c.Data)
					return
				}
				_, err = h.srv.NewConversationContentService().Add(ctx, request.AddConversationContent{
					ConversationId: conversation.ID,
					Content:        c.Data.Outputs.Answer,
					Type:           2,
				})
				if err != nil {
					slog.Error("AddChatMessageAgentContentError", "err", err, "data", c.Data)
					return
				}
			}
		}(line)

		// 写入原始 SSE 数据
		fmt.Fprintf(ctx.Writer, "%s\n", line)
		// 写入空行作为事件分隔（SSE 协议要求）
		if line == "" {
			fmt.Fprint(ctx.Writer, "\n")
		}
		flusher.Flush()
	}
	if err := scanner.Err(); err != nil {
		slog.Error("chatMessageError", "payload", payload)
		fmt.Fprintf(ctx.Writer, "event: error\ndata: %v\n\n", err)
		flusher.Flush()
	}
}
