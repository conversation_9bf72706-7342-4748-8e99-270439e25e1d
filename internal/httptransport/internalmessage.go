package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
)

func (h *handle) listInternalMessage(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	list, err := h.srv.NewInternalMessageService().List(ctx, page, perPage)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) addInternalMessage(ctx *gin.Context) {
	var req request.AddInternalMessage
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	err := h.srv.NewInternalMessageService().Add(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}
