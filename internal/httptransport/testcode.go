package httptransport

import (
	"bole-ai/internal"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"log/slog"
	"net/http"
	"strings"
	"time"
)

func (h *handle) listCode(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	batchId := cast.ToInt(ctx.Query("batch_id"))
	list, err := h.srv.NewTestCodeService().List(ctx, page, perPage, batchId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) testCode(ctx *gin.Context) {
	slog.Info("testCode", "header", ctx.Request.Header)
	userAgent := ctx.Request.Header.Get("User-Agent")
	if strings.Contains(userAgent, "iPhone") {
		userAgent += fmt.Sprintf("%d", time.Now().UnixMilli())
	}
	ttt := `
    <html>
    <head><title>珀乐AI短剧编剧助手</title></head>
    <body style="margin:0; display:flex; align-items:center; justify-content:center; height:100vh; background-color:#f0f0f0;">
        <div style="text-align:center; font-size:48px; font-weight:bold; color:#333;">
			%s
        </div>
    </body>
    </html>`
	if !strings.Contains(userAgent, "MicroMessenger") {
		ctx.Data(http.StatusOK, "text/html; charset=utf-8", []byte(fmt.Sprintf(ttt, "请使用微信扫码")))
		return
	}
	device := MD5(userAgent)
	code, err := h.srv.NewTestCodeService().GetOneTestCode(ctx, device)
	if err != nil {
		ctx.Data(http.StatusOK, "text/html; charset=utf-8", []byte(fmt.Sprintf(ttt, err.Error())))
		return
	}
	ttt = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>产品邀请码 - 科技未来</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
            position: relative;
        }

        /* 动态背景粒子效果 */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 255, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0; }
            50% { transform: translateY(-100px) rotate(180deg); opacity: 1; }
        }

        /* 主容器 */
        .container {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 24px;
            padding: 3rem 2rem;
            max-width: 480px;
            width: 90%;
            text-align: center;
            position: relative;
            z-index: 10;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
        }

        /* 发光边框效果 */
        .container::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, #00ffff, #ff00ff, #ffff00, #00ffff);
            border-radius: 24px;
            z-index: -1;
            animation: glow 3s linear infinite;
            opacity: 0.3;
        }

        @keyframes glow {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* 标题样式 */
        .title {
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #00ffff, #ff00ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-bottom: 2.5rem;
            line-height: 1.6;
        }

        /* 网站地址卡片 */
        .website-card {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }

        .website-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shine 2s infinite;
        }

        @keyframes shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .website-label {
            color: #00ffff;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .website-url {
            color: #ffffff;
            font-size: 1.3rem;
            font-weight: 500;
            word-break: break-all;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .website-url:hover {
            color: #00ffff;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
        }

        /* 邀请码卡片 */
        .invite-card {
            background: rgba(255, 0, 255, 0.1);
            border: 1px solid rgba(255, 0, 255, 0.3);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .invite-label {
            color: #ff00ff;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .invite-code {
            color: #ffffff;
            font-size: 2rem;
            font-weight: 700;
            font-family: 'Courier New', monospace;
            letter-spacing: 3px;
            margin-bottom: 1rem;
            text-shadow: 0 0 20px rgba(255, 0, 255, 0.6);
        }

        /* 复制按钮 */
        .copy-btn {
            background: linear-gradient(135deg, #ff00ff, #00ffff);
            border: none;
            border-radius: 12px;
            color: white;
            padding: 0.8rem 1.5rem;
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .copy-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 0, 255, 0.4);
        }

        .copy-btn:active {
            transform: translateY(0);
        }

        /* 底部信息 */
        .footer {
            color: rgba(255, 255, 255, 0.6);
            font-size: 0.85rem;
            line-height: 1.5;
        }

        .footer .highlight {
            color: #00ffff;
            font-weight: 600;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .container {
                padding: 2rem 1.5rem;
                margin: 1rem;
            }

            .title {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .website-url {
                font-size: 1.1rem;
            }

            .invite-code {
                font-size: 1.5rem;
                letter-spacing: 2px;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 1.8rem;
            }

            .invite-code {
                font-size: 1.3rem;
                letter-spacing: 1px;
            }

            .website-card, .invite-card {
                padding: 1rem;
            }
        }

        /* 成功提示 */
        .success-toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 255, 0, 0.9);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 1000;
        }

        .success-toast.show {
            transform: translateX(0);
        }
    </style>
</head>
<body>
    <!-- 动态粒子背景 -->
    <div class="particles" id="particles"></div>

    <!-- 主容器 -->
    <div class="container">
        <h1 class="title">科技未来</h1>
        <p class="subtitle">欢迎加入我们的创新平台<br>体验前沿科技的无限可能</p>

        <!-- 网站地址卡片 -->
        <div class="website-card">
            <div class="website-label">官方网站</div>
            <div class="website-url" onclick="openWebsite()">https://agent.poletech.com.cn/</div>
        </div>

        <!-- 邀请码卡片 -->
        <div class="invite-card">
            <div class="invite-label">专属邀请码</div>
            <div class="invite-code" id="inviteCode"> ` + code.Code + `</div>
            <button class="copy-btn" onclick="copyInviteCode()">复制邀请码</button>
        </div>

        <!-- 底部信息 -->
        <div class="footer">
            使用邀请码注册可享受 <span class="highlight">专属权益</span><br>
            立即体验未来科技的魅力
        </div>
    </div>

    <!-- 成功提示 -->
    <div class="success-toast" id="successToast">
        邀请码已复制到剪贴板！
    </div>

    <script>
        // 创建动态粒子效果
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 复制邀请码功能
        function copyInviteCode() {
            const inviteCode = document.getElementById('inviteCode').textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(inviteCode).then(() => {
                    showSuccessToast();
                });
            } else {
                // 兼容旧浏览器
                const textArea = document.createElement('textarea');
                textArea.value = inviteCode;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showSuccessToast();
            }
        }

        // 显示成功提示
        function showSuccessToast() {
            const toast = document.getElementById('successToast');
            toast.classList.add('show');
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // 打开网站
        function openWebsite() {
            window.open('https://agent.poletech.com.cn/', '_blank');
        }

        // 页面加载完成后创建粒子效果
        document.addEventListener('DOMContentLoaded', () => {
            createParticles();
        });

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'c') {
                e.preventDefault();
                copyInviteCode();
            }
        });
    </script>
</body>
</html>`
	ctx.Data(http.StatusOK, "text/html; charset=utf-8", []byte(ttt))
}
