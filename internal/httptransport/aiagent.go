package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
	"strings"
)

func (h *handle) addAiAgent(ctx *gin.Context) {
	var req request.AddAiAgent
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	for _, param := range req.Inputs {
		if !Contains([]string{
			"string",
			"int",
			"float",
		}, param.Type) {
			ctx.JSON(http.StatusBadRequest, response(nil, internal.AiAgentParamTypeError))
			return
		}
	}
	_, err := h.srv.NewAiAgentService().Add(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) listAiAgent(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	status := int8(0)
	if strings.Contains(ctx.FullPath(), "api") {
		status = 1
	}
	list, err := h.srv.NewAiAgentService().List(ctx, page, perPage, status)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) updateAiAgent(ctx *gin.Context) {
	id := ctx.Param("id")
	var req request.AddAiAgent
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	for _, param := range req.Inputs {
		if !Contains([]string{
			"string",
			"int",
			"float",
		}, param.Type) {
			ctx.JSON(http.StatusBadRequest, response(nil, internal.AiAgentParamTypeError))
			return
		}
	}
	agent, err := h.srv.NewAiAgentService().Update(ctx, cast.ToInt(id), req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(agent, ""))
}

func (h *handle) setAiAgentStatus(ctx *gin.Context) {
	id := cast.ToInt(ctx.Param("id"))
	err := h.srv.NewAiAgentService().SetStatus(ctx, id)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}
