package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/middleware"
	"bole-ai/internal/httptransport/token"
	"bole-ai/internal/model"
	"bole-ai/internal/service"
	bv "bole-ai/internal/validator"
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"net/http"
	"reflect"
	"strings"
)

type handle struct {
	conf       *model.Conf
	tokenMaker token.Maker
	srv        service.Service
}

func NewHandler(conf *model.Conf, tokenMaker token.Maker, srv service.Service) http.Handler {
	h := &handle{
		conf:       conf,
		tokenMaker: tokenMaker,
		srv:        srv,
	}

	r := gin.New()
	if !conf.Http.Debug {
		gin.SetMode(gin.ReleaseMode)
	}
	bv.InitValidator()
	backend := r.Group("/backend/v1")
	{
		backend.POST("login", h.adminLogin)
		bauth := backend.Group("")
		bauth.Use(middleware.JWTAuthMiddleware(h.tokenMaker))
		{
			bauth.GET("users/info", h.adminUser)
			bauth.GET("users", h.userList)
			bauth.POST("logout", h.adminLogout)
			bauth.PUT("users/:id/status", h.setStatus)
			bauth.POST("batch", h.addBatch)
			bauth.GET("batch", h.listBatch)
			bauth.GET("codes", h.listCode)
			bauth.GET("/users/export", h.exportUser)
			bauth.POST("/internal-messages", h.addInternalMessage)
			bauth.GET("/internal-messages", h.listInternalMessage)
			bauth.POST("/ai-agents", h.addAiAgent)
			bauth.GET("/ai-agents", h.listAiAgent)
			bauth.PUT("/ai-agents/:id", h.updateAiAgent)
			bauth.PUT("/ai-agents/:id/status", h.setAiAgentStatus)
			bauth.POST("/frequent-ask-questions", h.addFrequentAskQuestion)
			bauth.GET("/frequent-ask-questions/latest", h.getLatestFrequentAskQuestion)
		}
	}

	v1 := r.Group("/api/v1")
	v2 := r.Group("/api/v2")
	{
		v1.POST("register", h.register)
		v1.POST("login", h.login)
		v1.GET("code", h.testCode)
		v1.POST("users/forgetpwd", h.ForgetPassword)
		v1.POST("users/resetpwd", h.ResetPassword)

		auth := v1.Group("")
		auth.Use(middleware.JWTAuthMiddleware(h.tokenMaker))
		{
			auth.GET("users/info", h.user)
			auth.POST("logout", h.logout)

			auth.GET("projects", h.listProject)
			auth.PUT("projects/:id", h.updateProject)
			auth.POST("projects", h.addProject)

			auth.GET("screenplay", h.listScreenplay)
			auth.PUT("screenplay/:id", h.updateScreenplay)
			auth.GET("screenplay/:id", h.showScreenplay)
			auth.POST("screenplay", h.addScreenplay)
			auth.POST("chat-messages", h.chatMessage)
			auth.GET("export/:id", h.export)
			auth.POST("users/setpwd", h.SetPassword)

			auth.GET("users/internal-messages", h.listUserInternalMessage)
			auth.GET("users/internal-messages/:id", h.showUserInternalMessage)

			auth.GET("conversations", h.listConversation)
			auth.GET("conversations/:id", h.showConversation)
			auth.DELETE("conversations/:id", h.deleteConversation)

			auth.GET("/ai-agents", h.listAiAgent)
			auth.GET("/frequent-ask-questions/latest", h.getLatestFrequentAskQuestion)
		}

		authv2 := v2.Group("")
		authv2.Use(middleware.JWTAuthMiddleware(h.tokenMaker))
		{
			authv2.POST("chat-messages", h.chatMessageV2)
		}
	}

	return r
}

// response 统一响应数据格式，
// 解决empty slice return null 问题
// 只负责响应结构统一，成功/失败/分页等结构由业务定义
func response(data any, message string) gin.H {
	if data == nil {
		data = struct{}{}
	} else if reflect.TypeOf(data).Kind() == reflect.Slice &&
		reflect.ValueOf(data).Len() == 0 {
		data = struct{}{}
	}

	if message == "" {
		message = internal.MessageOk
	} else {
		message = strings.ReplaceAll(message, "ent: ", "")
	}

	return gin.H{
		"message": message,
		"data":    data,
	}
}

func BindAndValidateJSON(c *gin.Context, obj any, responseFunc func(data any, msg string) gin.H) bool {
	if err := c.ShouldBindJSON(obj); err != nil {
		c.JSON(http.StatusBadRequest, responseFunc(nil, flattenError(err)))
		return false
	}
	return true
}

// flattenError 将 validator 错误翻译为字符串
func flattenError(err error) string {
	if errs, ok := err.(validator.ValidationErrors); ok {
		for _, e := range errs {
			return e.Translate(bv.Trans) // 只返回第一个错误（也可以返回全部）
		}
	}
	return err.Error()
}
