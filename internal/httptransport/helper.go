package httptransport

import (
	"archive/zip"
	"crypto/md5"
	"encoding/hex"
	"math/rand"
	"regexp"
	"strings"
	"unicode"
)

func GetPayload() {

}

// shuffleRuneSlice 函数用于打乱 rune 切片中的元素
func shuffleRuneSlice(runes []rune) {
	n := len(runes)
	for i := n - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		runes[i], runes[j] = runes[j], runes[i]
	}
}

func FieldsFunc(in string) []string {
	isPunctuation := func(c rune) bool {
		return unicode.IsPunct(c)
	}
	lines := strings.FieldsFunc(in, isPunctuation)
	var out []string
	for _, line := range lines {
		out = append(out, line)
	}
	return out
}

func MD5(in string) string {
	hasher := md5.New()
	hasher.Write([]byte(in))
	return hex.EncodeToString(hasher.Sum(nil))
}

func ExtractPureText(input string) string {
	// 匹配中文、英文、数字字符
	re := regexp.MustCompile(`[\p{Han}a-zA-Z0-9]+`)
	matches := re.FindAllString(input, -1)

	return strings.Join(matches, "")
}

func writeZipFile(zipWriter *zip.Writer, name string, content string) {
	w, _ := zipWriter.Create(name)
	w.Write([]byte(content))
}

func buildDocumentXML(text string) string {
	lines := strings.Split(text, "\n")
	var sb strings.Builder
	sb.WriteString(headerXML)

	for _, line := range lines {
		sb.WriteString(`<w:p><w:pPr><w:ind w:left="720"/></w:pPr>`) // 缩进
		sb.WriteString(`<w:r><w:t xml:space="preserve">` + xmlEscape(line) + `</w:t></w:r>`)
		sb.WriteString(`</w:p>`)
	}

	sb.WriteString(footerXML)
	return sb.String()
}

func xmlEscape(s string) string {
	s = strings.ReplaceAll(s, "&", "&amp;")
	s = strings.ReplaceAll(s, "<", "&lt;")
	s = strings.ReplaceAll(s, ">", "&gt;")
	s = strings.ReplaceAll(s, `"`, "&quot;")
	s = strings.ReplaceAll(s, `'`, "&apos;")
	return s
}

var contentTypesXML = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
  <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
  <Default Extension="xml" ContentType="application/xml"/>
  <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>`

var relsXML = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
  <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>`

var docRelsXML = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships"/>`

var headerXML = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
<w:body>`

var footerXML = `</w:body></w:document>`

func Contains[T comparable](slice []T, item T) bool {
	for _, v := range slice {
		if v == item {
			return true
		}
	}
	return false
}
