package httptransport

import (
	"bole-ai/internal"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/token"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
)

func (h *handle) listProject(ctx *gin.Context) {
	pageStr := ctx.Query("page")
	perPageStr := ctx.Query("per_page")

	page := cast.ToInt(pageStr)
	perPage := cast.ToInt(perPageStr)

	if page < 0 || perPage < 0 {
		ctx.JSON(http.StatusBadRequest, response(nil, internal.PaginationParamError))
		return
	}
	if perPage == 0 {
		perPage = 10
	}
	if page == 0 {
		page = 1
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	list, err := h.srv.NewProjectService().List(ctx, page, perPage, user.ID)
	if err != nil {
		return
	}
	ctx.JSON(http.StatusOK, response(list, ""))
}

func (h *handle) addProject(ctx *gin.Context) {
	var req request.AddProject
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	req.UserId = int64(user.ID)
	_, err = h.srv.NewProjectService().Add(ctx, req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}

func (h *handle) updateProject(ctx *gin.Context) {
	id := ctx.Param("id")
	var req request.AddProject
	if !BindAndValidateJSON(ctx, &req, response) {
		return
	}
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}

	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	req.UserId = int64(user.ID)
	err = h.srv.NewProjectService().Update(ctx, cast.ToInt(id), req)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, err.Error()))
		return
	}
	ctx.JSON(http.StatusOK, response(nil, ""))
}
