package httptransport

import (
	"archive/zip"
	"bole-ai/internal"
	"bole-ai/internal/httptransport/token"
	"bytes"
	"encoding/csv"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"net/http"
	"time"
)

func (h *handle) export(ctx *gin.Context) {
	v, ok := ctx.Get(internal.AuthorizationPayloadKey)
	if !ok {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	p := v.(*token.Payload)
	user, err := h.srv.NewUserService().GetByUserName(ctx, p.UserID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.UserNotFundError))
		return
	}
	id := ctx.Param("id")
	screenplay, err := h.srv.NewScreenplayService().Show(ctx, cast.ToInt(id), user.ID)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.DataNotFound))
		return
	}
	// 1. 创建 Word 文档

	var buf bytes.Buffer
	zipWriter := zip.NewWriter(&buf)
	writeZipFile(zipWriter, "[Content_Types].xml", contentTypesXML)
	writeZipFile(zipWriter, "_rels/.rels", relsXML)
	writeZipFile(zipWriter, "word/_rels/document.xml.rels", docRelsXML)
	writeZipFile(zipWriter, "word/document.xml", buildDocumentXML(screenplay.Content))

	zipWriter.Close()
	// 3. 设置响应头，返回文件流
	filename := "export_" + time.Now().Format("20060102150405") + ".docx"
	ctx.Header("Content-Disposition", "attachment; filename=\""+filename+"\"")
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
	ctx.Header("Content-Length", cast.ToString(len(buf.Bytes())))
	ctx.Header("Cache-Control", "no-store")
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.wordprocessingml.document", buf.Bytes())
}

func (h *handle) exportUser(ctx *gin.Context) {
	email := ctx.Query("email")
	status := ctx.Query("status")
	list, err := h.srv.NewUserService().Export(ctx, cast.ToInt(status), email)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, response(nil, internal.DataNotFound))
		return
	}
	ctx.Writer.Header().Set("Content-Type", "text/csv; charset=utf-8")
	ctx.Writer.Header().Set("Content-Disposition", "attachment;filename=users.csv")

	// 写入 UTF-8 BOM，确保 Excel 能正确识别中文
	ctx.Writer.Write([]byte{0xEF, 0xBB, 0xBF})

	writer := csv.NewWriter(ctx.Writer)
	// 写表头
	writer.Write([]string{"ID", "邮箱", "注册时间", "来源", "状态"})

	// 写内容
	for _, user := range list {
		statusDesc := "正常"
		if user.Status == 2 {
			statusDesc = "禁用"
		}
		writer.Write([]string{
			cast.ToString(user.Id),
			user.Email,
			user.CreatedAt,
			user.BatchName,
			statusDesc,
		})
	}
	writer.Flush()
}
