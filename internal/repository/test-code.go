package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/testcode"
	"context"
	"errors"
	"github.com/bwmarrin/snowflake"
	"time"
)

type TestCodeRepository interface {
	Supplement(context.Context) error
	GetByCode(ctx context.Context, code string) (*ent.TestCode, error)
	GetOneTestCode(ctx context.Context, device string) (*ent.TestCode, error)
	List(ctx context.Context, offset, limit, batchId int) (int, []*ent.TestCode, error)
}

type testCodeRepository struct {
	client *ent.Client
}

var _ TestCodeRepository = (*testCodeRepository)(nil)

func newTestCodeRepository(client *ent.Client) TestCodeRepository {
	return &testCodeRepository{
		client: client,
	}
}

func (repo *testCodeRepository) Supplement(ctx context.Context) error {
	count, err := repo.client.TestCode.Query().
		Where(testcode.StatusEQ(1)).
		Count(ctx)
	if err != nil {
		return err
	}
	if count >= 500 {
		return nil
	}
	total := 500 - count
	var bulk []*ent.TestCodeCreate
	tx, err := repo.client.Tx(ctx)
	for i := 0; i < total; i++ {
		node, err := snowflake.NewNode(int64(i))
		if err != nil {
			return err
		}
		id, err := GenerateUniqueID(node)
		if err != nil {
			return err
		}
		bulk = append(bulk, tx.TestCode.Create().
			SetBatchID(1).
			SetCode(id).
			SetExpiredAt(time.Now().AddDate(100, 0, 0)),
		)
	}
	_, err = tx.TestCode.CreateBulk(bulk...).Save(ctx)
	if err != nil {
		return rollback(tx, err)
	}
	err = tx.Commit()
	if err != nil {
		return rollback(tx, err)
	}
	return nil
}

func (repo *testCodeRepository) GetByCode(ctx context.Context, code string) (*ent.TestCode, error) {
	return repo.client.TestCode.Query().
		WithBatch().
		Where(testcode.CodeEQ(code)).
		First(ctx)
}

func (repo *testCodeRepository) GetOneTestCode(ctx context.Context, device string) (*ent.TestCode, error) {
	code, err := repo.client.TestCode.Query().
		Where(testcode.DeviceEQ(device)).
		First(ctx)
	if err != nil {
		if !ent.IsNotFound(err) {
			return nil, err
		}
		code, err = repo.client.TestCode.Query().
			Where(testcode.StatusEQ(1)).
			Where(testcode.ExpiredAtGTE(time.Now())).
			First(ctx)
	}
	if code != nil {
		if code.Status == 1 {
			return repo.client.TestCode.UpdateOne(code).
				SetDevice(device).
				SetStatus(2).
				Save(ctx)
		}
		if code.Status == 2 {
			return code, nil
		}
		if code.Status == 3 {
			return nil, errors.New("已领取过测试码")
		}
	}

	return code, nil
}

func (repo *testCodeRepository) List(ctx context.Context, offset, limit, batchId int) (int, []*ent.TestCode, error) {
	total, err := repo.client.TestCode.Query().Where(testcode.BatchID(batchId)).Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.TestCode.Query().
		Where(testcode.BatchID(batchId)).
		WithUser().
		Offset(offset).
		Limit(limit).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}
