package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"github.com/spf13/cast"
)

// UserInternalMessageRepository interface
type UserInternalMessageRepository interface {
	List(ctx context.Context, offset, limit, userId, status int) (int, []*ent.UserInternalMessage, error)
	Count(ctx context.Context, userId, status int) (int, error)
	Show(ctx context.Context, userId, id int) (*ent.UserInternalMessage, error)
}

// userInternalMessageRepository implements UserInternalMessageRepository
type userInternalMessageRepository struct {
	client *ent.Client
}

// constructor
func newUserInternalMessageRepository(client *ent.Client) UserInternalMessageRepository {
	return &userInternalMessageRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ UserInternalMessageRepository = (*userInternalMessageRepository)(nil)

func (repo *userInternalMessageRepository) List(ctx context.Context, offset, limit, userId, status int) (int, []*ent.UserInternalMessage, error) {
	q := repo.client.UserInternalMessage.Query().Where(userinternalmessage.UserID(userId))
	if status > 0 {
		q.Where(userinternalmessage.Status(cast.ToInt8(status)))
	}
	total, err := q.Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := q.Offset(offset).
		WithMessage(func(query *ent.InternalMessageQuery) {
			query.Select(internalmessage.FieldID, internalmessage.FieldTitle)
		}).
		Limit(limit).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *userInternalMessageRepository) Count(ctx context.Context, userId, status int) (int, error) {
	q := repo.client.UserInternalMessage.Query().Where(userinternalmessage.UserID(userId))
	if status > 0 {
		q.Where(userinternalmessage.Status(cast.ToInt8(status)))
	}
	return q.Count(ctx)
}

func (repo *userInternalMessageRepository) Show(ctx context.Context, userId, id int) (*ent.UserInternalMessage, error) {
	m, err := repo.client.UserInternalMessage.Query().
		Where(userinternalmessage.UserID(userId)).
		Where(userinternalmessage.ID(id)).
		WithMessage().
		First(ctx)
	if err != nil {
		return nil, err
	}
	_, err = repo.client.UserInternalMessage.UpdateOne(m).SetStatus(2).Save(ctx)
	if err != nil {
		return nil, err
	}
	return m, nil
}
