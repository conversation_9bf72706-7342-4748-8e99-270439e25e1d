package repository

import (
	"bole-ai/internal/ent"
	"crypto/rand"
	"fmt"
	"github.com/bwmarrin/snowflake"
	"golang.org/x/crypto/bcrypt"
	"math/big"
	"strings"
)

const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func randomString(length int) (string, error) {
	result := make([]byte, length)
	charsetLen := big.NewInt(int64(len(charset)))
	for i := 0; i < length; i++ {
		num, err := rand.Int(rand.Reader, charsetLen)
		if err != nil {
			return "", err
		}
		result[i] = charset[num.Int64()]
	}
	return string(result), nil
}

// GenerateUniqueID 10位唯一ID，雪花ID转62进制 + 随机补足10位
func GenerateUniqueID(node *snowflake.Node) (string, error) {
	id := node.Generate()
	base62 := toBase62(id.Int64())

	if len(base62) >= 10 {
		return base62[:10], nil
	}

	// 不够10位补随机字符
	suffixLen := 10 - len(base62)
	suffix, err := randomString(suffixLen)
	if err != nil {
		return "", err
	}
	return base62 + suffix, nil
}

// 把int64转成62进制字符串
func toBase62(num int64) string {
	if num == 0 {
		return "0"
	}
	var sb strings.Builder
	for num > 0 {
		remainder := num % 62
		sb.WriteByte(charset[remainder])
		num = num / 62
	}
	// 反转字符串
	res := sb.String()
	// reverse
	runes := []rune(res)
	for i, j := 0, len(runes)-1; i < j; i, j = i+1, j-1 {
		runes[i], runes[j] = runes[j], runes[i]
	}
	return string(runes)
}

func rollback(tx *ent.Tx, err error) error {
	if rerr := tx.Rollback(); rerr != nil {
		err = fmt.Errorf("%w: %v", err, rerr)
	}
	return err
}

func encryptPWD(pwd string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
	return string(hash), err
}
