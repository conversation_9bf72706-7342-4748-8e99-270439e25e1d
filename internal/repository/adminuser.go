package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/adminuser"
	"context"
)

type adminUserRepository struct {
	client *ent.Client
}

// AdminUserRepository is repository's functions used to service
// It statement the repository's abilities.
// the function name use Camel-Case define should be like this.
// the action is prefix the domain is middle and the other is suffix.
type AdminUserRepository interface {
	GetByUsername(ctx context.Context, username string) (*ent.AdminUser, error)

	// InjectInterface
}

func newAdminUserRepository(client *ent.Client) AdminUserRepository {
	return &adminUserRepository{
		client: client,
	}
}

func (repo *adminUserRepository) GetByUsername(ctx context.Context, username string) (*ent.AdminUser, error) {
	_ = repo.InitAdminUser(ctx)
	return repo.client.AdminUser.Query().
		Where(adminuser.Username(username)).
		Select().
		First(ctx)
}

func (repo *adminUserRepository) InitAdminUser(ctx context.Context) error {
	count, err := repo.client.AdminUser.Query().Where(adminuser.Username("admin")).Count(ctx)
	if err != nil {
		return err
	}
	if count > 0 {
		return nil
	}
	p, _ := encryptPWD("aca07426d537d0b07f5ca1764c2f61e0") // BoleAI.1
	_, err = repo.client.AdminUser.Create().
		SetUsername("admin").
		SetPassword(p).
		Save(ctx)
	return err
}

// InjectInterfaceImpl
