package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/frequentaskquestion"
	"context"
	"github.com/gin-gonic/gin"
)

// FrequentlyAskedQuestionsRepository interface
type FrequentlyAskedQuestionsRepository interface {
	GetLatest(ctx *gin.Context) (*ent.FrequentAskQuestion, error)
	Add(ctx context.Context, in *ent.FrequentAskQuestion) (*ent.FrequentAskQuestion, error)
}

// frequentlyAskedQuestionsRepository implements FrequentlyAskedQuestionsRepository
type frequentlyAskedQuestionsRepository struct {
	client *ent.Client
}

// constructor
func newFrequentlyAskedQuestionsRepository(client *ent.Client) FrequentlyAskedQuestionsRepository {
	return &frequentlyAskedQuestionsRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ FrequentlyAskedQuestionsRepository = (*frequentlyAskedQuestionsRepository)(nil)

func (repo *frequentlyAskedQuestionsRepository) GetLatest(ctx *gin.Context) (*ent.FrequentAskQuestion, error) {
	f, err := repo.client.FrequentAskQuestion.Query().
		Order(ent.Desc(frequentaskquestion.FieldID)).
		First(ctx)
	if ent.IsNotFound(err) {
		return &ent.FrequentAskQuestion{}, nil
	}
	return f, err
}

func (repo *frequentlyAskedQuestionsRepository) Add(ctx context.Context, in *ent.FrequentAskQuestion) (*ent.FrequentAskQuestion, error) {
	return repo.client.FrequentAskQuestion.Create().
		SetContent(in.Content).
		Save(ctx)
}
