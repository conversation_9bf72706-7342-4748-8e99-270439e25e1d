package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/user"
	"context"
	"errors"
	"fmt"
)

// InternalMessageRepository interface
type InternalMessageRepository interface {
	Add(ctx context.Context, in *ent.InternalMessage, userIds []int) (*ent.InternalMessage, error)
	Update(ctx context.Context, id int, in *ent.InternalMessage) (*ent.InternalMessage, error)
	List(ctx context.Context, offset, limit int) (int, []*ent.InternalMessage, error)
}

// internalMessageRepository implements InternalMessageRepository
type internalMessageRepository struct {
	client *ent.Client
}

// constructor
func newInternalMessageRepository(client *ent.Client) InternalMessageRepository {
	return &internalMessageRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ InternalMessageRepository = (*internalMessageRepository)(nil)

func (repo *internalMessageRepository) List(ctx context.Context, offset, limit int) (int, []*ent.InternalMessage, error) {
	total, err := repo.client.InternalMessage.Query().
		Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.InternalMessage.Query().
		WithUsers(func(query *ent.UserInternalMessageQuery) {
			query.WithUser(func(query *ent.UserQuery) {
				query.Select(user.FieldID, user.FieldUsername)
			})
		}).
		Offset(offset).
		Limit(limit).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *internalMessageRepository) Add(ctx context.Context, in *ent.InternalMessage, userIds []int) (*ent.InternalMessage, error) {
	q := repo.client.User.Query()
	if len(userIds) == 0 {
		q.Select(user.FieldID)
	} else {
		q.Where(user.IDIn(userIds...)).Select(user.FieldID)
	}
	users, err := q.All(ctx)
	if err != nil {
		return nil, err
	}
	if len(users) == 0 {
		return nil, errors.New("user is empty")
	}
	tx, err := repo.client.Tx(ctx)
	if err != nil {
		return nil, fmt.Errorf("starting transaction: %w", err)
	}

	message, err := tx.InternalMessage.Create().
		SetTitle(in.Title).
		SetContent(in.Content).
		Save(ctx)
	if err != nil {
		err = rollback(tx, err)
		if err != nil {
			return nil, err
		}
	}

	builders := make([]*ent.UserInternalMessageCreate, len(users))
	for i, u := range users {
		builders[i] = tx.UserInternalMessage.
			Create().
			SetInternalMessageID(message.ID).
			SetUserID(u.ID)
	}
	if _, err = tx.UserInternalMessage.
		CreateBulk(builders...).
		Save(ctx); err != nil {
		return nil, rollback(tx, err)
	}
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("committing transaction: %w", err)
	}
	return message, nil
}

func (repo *internalMessageRepository) Update(ctx context.Context, id int, in *ent.InternalMessage) (*ent.InternalMessage, error) {
	return repo.client.InternalMessage.UpdateOneID(id).
		SetTitle(in.Title).
		SetContent(in.Content).
		Save(ctx)
}
