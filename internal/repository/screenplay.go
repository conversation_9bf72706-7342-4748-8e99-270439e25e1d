package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/screenplay"
	"context"
)

type ScreenplayRepository interface {
	List(ctx context.Context, offset, limit, projectId, userId int) (int, []*ent.Screenplay, error)
	Show(ctx context.Context, id, userId int) (*ent.Screenplay, error)
	Delete(ctx context.Context, id int) (int, error)
	Add(ctx context.Context, in *ent.Screenplay) (*ent.Screenplay, error)
	Update(ctx context.Context, id int, in *ent.Screenplay) (int, error)
}

type screenplayRepository struct {
	client *ent.Client
}

var _ ScreenplayRepository = (*screenplayRepository)(nil)

func newScreenplayRepository(client *ent.Client) ScreenplayRepository {
	return &screenplayRepository{
		client: client,
	}
}

func (repo *screenplayRepository) List(ctx context.Context, offset, limit, projectId, userId int) (int, []*ent.Screenplay, error) {
	total, err := repo.client.Screenplay.Query().
		Where(screenplay.UserID(int64(userId))).
		Where(screenplay.ProjectID(int64(projectId))).
		Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.Screenplay.Query().
		Where(screenplay.UserID(int64(userId))).
		Where(screenplay.ProjectID(int64(projectId))).
		Offset(offset).
		Limit(limit).
		Select(screenplay.FieldID, screenplay.FieldProjectID, screenplay.FieldTitle, screenplay.FieldType).
		Order(ent.Desc(screenplay.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *screenplayRepository) Show(ctx context.Context, id, userId int) (*ent.Screenplay, error) {
	return repo.client.Screenplay.Query().
		Where(screenplay.ID(id)).
		Where(screenplay.UserID(int64(userId))).
		Select(
			screenplay.FieldID,
			screenplay.FieldProjectID,
			screenplay.FieldTitle,
			screenplay.FieldType,
			screenplay.FieldContent,
			screenplay.FieldUpdatedAt,
		).
		First(ctx)
}

func (repo *screenplayRepository) Delete(ctx context.Context, id int) (int, error) {
	return repo.client.Screenplay.Delete().
		Where(screenplay.ID(id)).
		Exec(ctx)
}

func (repo *screenplayRepository) Add(ctx context.Context, in *ent.Screenplay) (*ent.Screenplay, error) {
	p, err := repo.client.Screenplay.Create().
		SetUserID(in.UserID).
		SetTitle(in.Title).
		SetContent(in.Content).
		SetProjectID(in.ProjectID).
		SetType(in.Type).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return p, nil
}

func (repo *screenplayRepository) Update(ctx context.Context, id int, in *ent.Screenplay) (int, error) {
	return repo.client.Screenplay.Update().
		Where(screenplay.ID(id)).
		SetTitle(in.Title).
		SetContent(in.Content).
		Save(ctx)
}
