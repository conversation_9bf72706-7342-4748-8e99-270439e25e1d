package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/project"
	"context"
)

// AiAgentRepository interface
type AiAgentRepository interface {
	Add(ctx context.Context, in *ent.AiAgent) (*ent.AiAgent, error)
	List(ctx context.Context, offset, limit int, status int8) (int, []*ent.AiAgent, error)
	Update(ctx context.Context, id int, in *ent.AiAgent) (*ent.AiAgent, error)
	SetStatus(ctx context.Context, id int) error
	GetById(ctx context.Context, id int) (*ent.AiAgent, error)
}

// aiAgentRepository implements AiAgentRepository
type aiAgentRepository struct {
	client *ent.Client
}

// constructor
func newAiAgentRepository(client *ent.Client) AiAgentRepository {
	return &aiAgentRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ AiAgentRepository = (*aiAgentRepository)(nil)

func (repo *aiAgentRepository) Add(ctx context.Context, in *ent.AiAgent) (*ent.AiAgent, error) {
	return repo.client.AiAgent.Create().
		SetName(in.Name).
		SetIcon(in.Icon).
		SetDescription(in.Description).
		SetInputs(in.Inputs).
		SetTarget(in.Target).
		SetSecret(in.Secret).
		SetMethod(in.Method).
		SetGuide(in.Guide).
		Save(ctx)
}

func (repo *aiAgentRepository) List(ctx context.Context, offset, limit int, status int8) (int, []*ent.AiAgent, error) {
	q := repo.client.AiAgent.Query()
	if status > 0 {
		q.Where(aiagent.Status(status))
	}
	total, err := q.Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := q.Offset(offset).
		Limit(limit).
		Order(ent.Desc(project.FieldCreatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *aiAgentRepository) Update(ctx context.Context, id int, in *ent.AiAgent) (*ent.AiAgent, error) {
	c := repo.client.AiAgent.UpdateOneID(id)
	if in.Target != "" {
		c.SetTarget(in.Target)
	}
	if in.Icon != "" {
		c.SetIcon(in.Icon)
	}
	if in.Description != "" {
		c.SetDescription(in.Description)
	}
	if in.Inputs != nil {
		c.SetInputs(in.Inputs)
	}
	if in.Name != "" {
		c.SetName(in.Name)
	}
	if in.Method != "" {
		c.SetMethod(in.Method)
	}
	if in.Secret != "" {
		c.SetSecret(in.Secret)
	}
	if in.Guide != "" {
		c.SetGuide(in.Guide)
	}
	return c.Save(ctx)
}

func (repo *aiAgentRepository) SetStatus(ctx context.Context, id int) error {
	u, err := repo.client.AiAgent.Query().
		Where(aiagent.ID(id)).
		First(ctx)
	if err != nil {
		return err
	}
	status := int8(1)
	if u.Status == 1 {
		status = 2
	} else if u.Status == 2 {
		status = 1
	}
	_, err = repo.client.AiAgent.UpdateOne(u).
		SetStatus(status).Save(ctx)
	return err
}

func (repo *aiAgentRepository) GetById(ctx context.Context, id int) (*ent.AiAgent, error) {
	return repo.client.AiAgent.Query().
		Where(aiagent.ID(id)).
		First(ctx)
}
