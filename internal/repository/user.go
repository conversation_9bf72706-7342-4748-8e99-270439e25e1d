package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
)

type UserRepository interface {
	List(ctx context.Context, offset, limit, status int, email string) (int, []*ent.User, error)
	Export(ctx context.Context, status int, email string) ([]*ent.User, error)
	Register(context.Context, *ent.User) (*ent.User, error)
	GetByUserName(ctx context.Context, username string) (*ent.User, error)
	GetByEmail(ctx context.Context, email string) (*ent.User, error)
	SetStatus(ctx context.Context, id int) error
	SetPassword(ctx context.Context, id int, pwd string) error
}

type userRepository struct {
	client *ent.Client
}

var _ UserRepository = (*userRepository)(nil)

func newUserRepository(client *ent.Client) UserRepository {
	return &userRepository{
		client: client,
	}
}

func (repo *userRepository) List(ctx context.Context, offset, limit, status int, email string) (int, []*ent.User, error) {
	client := repo.client.User.Query()
	if email != "" {
		client.Where(user.Email(email))
	}
	if status != 0 {
		client.Where(user.Status(int8(status)))
	}

	total, err := client.Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := client.Offset(offset).
		WithCode().
		WithBatch().
		Limit(limit).
		Order(ent.Desc(user.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *userRepository) Export(ctx context.Context, status int, email string) ([]*ent.User, error) {
	client := repo.client.User.Query()
	if email != "" {
		client.Where(user.Email(email))
	}
	if status != 0 {
		client.Where(user.Status(int8(status)))
	}
	list, err := client.
		WithCode().
		WithBatch().
		Order(ent.Desc(user.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return nil, err
	}
	return list, nil
}

func (repo *userRepository) Register(ctx context.Context, u *ent.User) (*ent.User, error) {
	tx, err := repo.client.Tx(ctx)
	if err != nil {
		return nil, err
	}
	ur, err := tx.User.Create().
		SetUsername(u.Username).
		SetEmail(u.Email).
		SetTestCodeID(u.TestCodeID).
		SetBatchID(u.BatchID).
		SetPassword(u.Password).
		Save(ctx)
	if err != nil {
		return nil, rollback(tx, err)
	}
	_, err = tx.TestCode.Update().Where(testcode.ID(int(u.TestCodeID))).SetStatus(3).Save(ctx)
	if err != nil {
		return nil, rollback(tx, err)
	}
	err = tx.Commit()
	if err != nil {
		return nil, rollback(tx, err)
	}
	return ur, nil
}

func (repo *userRepository) GetByUserName(ctx context.Context, username string) (*ent.User, error) {
	return repo.client.User.Query().
		Where(user.UsernameEQ(username)).
		First(ctx)
}

func (repo *userRepository) GetByEmail(ctx context.Context, email string) (*ent.User, error) {
	return repo.client.User.Query().
		Where(user.Email(email)).
		First(ctx)
}

func (repo *userRepository) SetStatus(ctx context.Context, id int) error {
	u, err := repo.client.User.Query().
		Where(user.ID(id)).
		First(ctx)
	if err != nil {
		return err
	}
	status := 1
	if u.Status == 1 {
		status = 2
	} else if u.Status == 2 {
		status = 1
	}
	_, err = repo.client.User.UpdateOne(u).
		SetStatus(int8(status)).Save(ctx)
	return err
}

func (repo *userRepository) SetPassword(ctx context.Context, id int, pwd string) error {
	_, err := repo.client.User.UpdateOneID(id).
		SetPassword(pwd).
		Save(ctx)
	return err
}
