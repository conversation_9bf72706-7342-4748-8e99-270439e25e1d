package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/project"
	"context"
)

// ConversationRepository interface
type ConversationRepository interface {
	Add(ctx context.Context, in *ent.Conversation) (*ent.Conversation, error)
	GetByConversationId(ctx context.Context, conversationId string, userId int) (*ent.Conversation, error)
	GetById(ctx context.Context, id, userId int) (*ent.Conversation, error)
	List(ctx context.Context, offset, limit, userId int) (int, []*ent.Conversation, error)
	Delete(ctx context.Context, conversationID string, userId int) (int, error)
}

// conversationRepository implements ConversationRepository
type conversationRepository struct {
	client *ent.Client
}

// constructor
func newConversationRepository(client *ent.Client) ConversationRepository {
	return &conversationRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ ConversationRepository = (*conversationRepository)(nil)

func (repo *conversationRepository) Add(ctx context.Context, in *ent.Conversation) (*ent.Conversation, error) {
	return repo.client.Conversation.Create().
		SetAiAgentID(in.AiAgentID).
		SetUserID(in.UserID).
		SetName(in.Name).
		SetAiAgentConversationID(in.AiAgentConversationID).
		Save(ctx)
}

func (repo *conversationRepository) GetByConversationId(ctx context.Context, conversationId string, userId int) (*ent.Conversation, error) {
	return repo.client.Conversation.Query().
		Where(conversation.UserID(userId)).
		Where(conversation.AiAgentConversationID(conversationId)).
		WithAiAgent().
		WithContents().
		First(ctx)
}

func (repo *conversationRepository) GetById(ctx context.Context, id, userId int) (*ent.Conversation, error) {
	return repo.client.Conversation.Query().
		Where(conversation.UserID(userId)).
		Where(conversation.ID(id)).
		WithContents().
		First(ctx)
}

func (repo *conversationRepository) List(ctx context.Context, offset, limit, userId int) (int, []*ent.Conversation, error) {
	total, err := repo.client.Conversation.Query().Where(conversation.UserID(userId)).Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.Conversation.Query().
		Where(conversation.UserID(userId)).
		Offset(offset).
		Limit(limit).
		Select(conversation.FieldID, conversation.FieldAiAgentConversationID, conversation.FieldName, conversation.FieldUpdatedAt).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *conversationRepository) Delete(ctx context.Context, conversationID string, userId int) (int, error) {
	return repo.client.Conversation.Delete().
		Where(conversation.UserID(userId)).
		Where(conversation.AiAgentConversationID(conversationID)).
		Exec(ctx)
}
