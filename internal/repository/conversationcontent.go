package repository

import (
	"bole-ai/internal/ent"
	"context"
)

// ConversationContentRepository interface
type ConversationContentRepository interface {
	Add(ctx context.Context, in *ent.ConversationContent) (*ent.ConversationContent, error)
}

// conversationContentRepository implements ConversationContentRepository
type conversationContentRepository struct {
	client *ent.Client
}

// constructor
func newConversationContentRepository(client *ent.Client) ConversationContentRepository {
	return &conversationContentRepository{client: client}
}

// compile-time check: ensure struct implements interface
var _ ConversationContentRepository = (*conversationContentRepository)(nil)

func (repo *conversationContentRepository) Add(ctx context.Context, in *ent.ConversationContent) (*ent.ConversationContent, error) {
	return repo.client.ConversationContent.Create().
		SetConversationID(in.ConversationID).
		SetContent(in.Content).
		SetType(in.Type).
		Save(ctx)
}
