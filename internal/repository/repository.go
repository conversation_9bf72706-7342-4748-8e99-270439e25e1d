package repository

import (
	"bole-ai/internal/ent"
)

type Repository interface {
	NewFrequentlyAskedQuestionsRepository() FrequentlyAskedQuestionsRepository
	NewConversationContentRepository() ConversationContentRepository
	NewConversationRepository() ConversationRepository
	NewAiAgentRepository() AiAgentRepository
	NewUserInternalMessageRepository() UserInternalMessageRepository
	NewInternalMessageRepository() InternalMessageRepository
	NewUserRepository() UserRepository
	NewTestCodeRepository() TestCodeRepository
	NewScreenplayRepository() ScreenplayRepository
	NewProjectRepository() ProjectRepository
	NewAdminUserRepository() AdminUserRepository
	NewBatchRepository() BatchRepository
}

type repository struct {
	client *ent.Client
}

var _ Repository = (*repository)(nil)

func NewRepository(client *ent.Client) Repository {
	return &repository{
		client: client,
	}
}

func (repo *repository) NewFrequentlyAskedQuestionsRepository() FrequentlyAskedQuestionsRepository {
	return newFrequentlyAskedQuestionsRepository(repo.client)
}

func (repo *repository) NewConversationContentRepository() ConversationContentRepository {
	return newConversationContentRepository(repo.client)
}

func (repo *repository) NewConversationRepository() ConversationRepository {
	return newConversationRepository(repo.client)
}

func (repo *repository) NewAiAgentRepository() AiAgentRepository {
	return newAiAgentRepository(repo.client)
}

func (repo *repository) NewUserInternalMessageRepository() UserInternalMessageRepository {
	return newUserInternalMessageRepository(repo.client)
}

func (repo *repository) NewInternalMessageRepository() InternalMessageRepository {
	return newInternalMessageRepository(repo.client)
}

func (repo *repository) NewUserRepository() UserRepository {
	return newUserRepository(repo.client)
}

func (repo *repository) NewTestCodeRepository() TestCodeRepository {
	return newTestCodeRepository(repo.client)
}

func (repo *repository) NewScreenplayRepository() ScreenplayRepository {
	return newScreenplayRepository(repo.client)
}

func (repo *repository) NewProjectRepository() ProjectRepository {
	return newProjectRepository(repo.client)
}

func (repo *repository) NewAdminUserRepository() AdminUserRepository {
	return newAdminUserRepository(repo.client)
}

func (repo *repository) NewBatchRepository() BatchRepository {
	return newBatchRepository(repo.client)
}
