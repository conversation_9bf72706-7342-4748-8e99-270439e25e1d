package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/project"
	"context"
)

type ProjectRepository interface {
	List(ctx context.Context, offset, limit, userId int) (int, []*ent.Project, error)
	Add(ctx context.Context, project *ent.Project) (*ent.Project, error)
	Update(ctx context.Context, id int, in *ent.Project) (*ent.Project, error)
}

type projectRepository struct {
	client *ent.Client
}

func (repo *projectRepository) List(ctx context.Context, offset, limit, userId int) (int, []*ent.Project, error) {
	total, err := repo.client.Project.Query().Where(project.UserID(int64(userId))).Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.Project.Query().
		Where(project.UserID(int64(userId))).
		Offset(offset).
		Limit(limit).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *projectRepository) Add(ctx context.Context, in *ent.Project) (*ent.Project, error) {
	return repo.client.Project.Create().
		SetUserID(in.UserID).
		SetName(in.Name).
		Save(ctx)
}

func (repo *projectRepository) Update(ctx context.Context, id int, in *ent.Project) (*ent.Project, error) {
	return repo.client.Project.UpdateOneID(id).
		SetName(in.Name).
		Save(ctx)
}

var _ ProjectRepository = (*projectRepository)(nil)

func newProjectRepository(client *ent.Client) ProjectRepository {
	return &projectRepository{
		client: client,
	}
}
