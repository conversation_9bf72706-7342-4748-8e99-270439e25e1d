package repository

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/project"
	"context"
	"github.com/bwmarrin/snowflake"
	"github.com/spf13/cast"
)

type BatchRepository interface {
	Add(ctx context.Context, project *ent.Batch) error
	List(ctx context.Context, offset, limit int) (int, []*ent.Batch, error)
}
type batchRepository struct {
	client *ent.Client
}

func (repo *batchRepository) List(ctx context.Context, offset, limit int) (int, []*ent.Batch, error) {
	total, err := repo.client.Batch.Query().Count(ctx)
	if err != nil {
		return 0, nil, err
	}

	list, err := repo.client.Batch.Query().
		WithUsers().
		Offset(offset).
		Limit(limit).
		Order(ent.Desc(project.FieldUpdatedAt)).
		All(ctx)

	if err != nil {
		return 0, nil, err
	}
	return total, list, nil
}

func (repo *batchRepository) Add(ctx context.Context, in *ent.Batch) error {
	b, err := repo.client.Batch.Query().Order(ent.Desc(batch.FieldID)).First(ctx)
	if err != nil && !ent.IsNotFound(err) {
		return err
	}
	s := int64(0)
	total := cast.ToInt64(in.Num)
	if b != nil {
		s = cast.ToInt64(b.ID)
		total = total + cast.ToInt64(b.ID)
	}
	tx, err := repo.client.Debug().Tx(ctx)
	save, err := tx.Batch.Create().
		SetName(in.Name).
		SetNum(in.Num).
		SetExpiredAt(in.ExpiredAt).
		Save(ctx)
	if err != nil {
		return rollback(tx, err)
	}
	var bulk []*ent.TestCodeCreate
	for i := s; i < total; i++ {
		node, err := snowflake.NewNode(i)
		if err != nil {
			return err
		}
		id, err := GenerateUniqueID(node)
		if err != nil {
			return err
		}
		bulk = append(bulk,
			tx.TestCode.Create().
				SetCode(id).
				SetExpiredAt(in.ExpiredAt).
				SetBatchID(save.ID),
		)
	}
	_, err = tx.TestCode.CreateBulk(bulk...).Save(ctx)
	if err != nil {
		return rollback(tx, err)
	}
	err = tx.Commit()
	if err != nil {
		return rollback(tx, err)
	}
	return nil
}

func newBatchRepository(client *ent.Client) BatchRepository {
	return &batchRepository{
		client: client,
	}
}
