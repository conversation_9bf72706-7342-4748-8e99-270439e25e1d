package validator

import (
	"github.com/gin-gonic/gin/binding"
	"github.com/go-playground/locales/zh"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	zh_translations "github.com/go-playground/validator/v10/translations/zh"
	"log"
)

var (
	Validate *validator.Validate
	Trans    ut.Translator
)

func InitValidator() {
	zhCn := zh.New()
	uni := ut.New(zhCn, zhCn)
	trans, found := uni.GetTranslator("zh")
	if !found {
		log.Fatal("翻译器初始化失败")
	}

	v, ok := binding.Validator.Engine().(*validator.Validate)
	if !ok {
		log.Fatal("Validator 引擎初始化失败")
	}

	if err := zh_translations.RegisterDefaultTranslations(v, trans); err != nil {
		log.Fatal("注册翻译器失败: ", err)
	}

	Validate = v
	Trans = trans
}
