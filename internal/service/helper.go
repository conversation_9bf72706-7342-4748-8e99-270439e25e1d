package service

import (
	"crypto/rand"
	"golang.org/x/crypto/bcrypt"
	"math/big"
	"strings"
	"unicode"
)

func FieldsFunc(in string) []string {
	isPunctuation := func(c rune) bool {
		return unicode.IsPunct(c)
	}
	lines := strings.FieldsFunc(in, isPunctuation)
	var out []string
	for _, line := range lines {
		out = append(out, line)
	}
	return out
}

// comparePwd validate the password is same.
func comparePwd(pwd1 string, pwd2 string) bool {
	// Returns true on success, pwd1 is for the database.
	err := bcrypt.CompareHashAndPassword([]byte(pwd1), []byte(pwd2))
	if err != nil {
		return false
	} else {
		return true
	}
}

// encryptPWD encrypt the pwd
func encryptPWD(pwd string) (string, error) {
	hash, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
	return string(hash), err
}

func GenerateCode(length int) string {
	digits := "0123456789"
	code := ""
	for i := 0; i < length; i++ {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(digits))))
		code += string(digits[n.Int64()])
	}
	return code
}
