package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	goRedis "github.com/go-redis/redis/v8"
)

// AiAgentService interface
type AiAgentService interface {
	Add(ctx context.Context, req request.AddAiAgent) (*ent.AiAgent, error)
	List(ctx context.Context, page, perPage int, status int8) (response.ListAiAgent, error)
	SetStatus(ctx context.Context, id int) error
	Update(ctx context.Context, id int, req request.AddAiAgent) (*ent.AiAgent, error)
	GetById(ctx context.Context, id int) (*ent.AiAgent, error)
}

// aiAgentService implements AiAgentService
type aiAgentService struct {
	repo repository.AiAgentRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ AiAgentService = (*aiAgentService)(nil)

// constructor
func newAiAgentService(repo repository.AiAgentRepository, rdb *goRedis.Client, conf *model.Conf) AiAgentService {
	return &aiAgentService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *aiAgentService) Add(ctx context.Context, req request.AddAiAgent) (*ent.AiAgent, error) {
	return srv.repo.Add(ctx, &ent.AiAgent{
		Name:        req.Name,
		Icon:        req.Icon,
		Description: req.Description,
		Inputs:      req.Inputs,
		Target:      req.Target,
		Method:      req.Method,
		Secret:      req.Secret,
	})
}

func (srv *aiAgentService) List(ctx context.Context, page, perPage int, status int8) (response.ListAiAgent, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, status)
	if err != nil {
		return response.ListAiAgent{}, err
	}
	var out = make([]response.AiAgent, 0)
	isApi := status == 1 // 用户端请求
	for _, item := range list {
		agent := response.AiAgent{
			Id:          item.ID,
			Name:        item.Name,
			Icon:        item.Icon,
			Description: item.Description,
			Param:       item.Inputs,
			Status:      item.Status,
			Guide:       item.Guide,
		}
		if !isApi {
			agent.Target = item.Target
			agent.Method = item.Method
			agent.Secret = item.Secret
		}
		out = append(out, agent)
	}
	return response.ListAiAgent{
		Pagination: model.Pagination{
			Total:   total,
			PerPage: perPage,
			Page:    page,
		},
		List: out,
	}, nil
}

func (srv *aiAgentService) SetStatus(ctx context.Context, id int) error {
	return srv.repo.SetStatus(ctx, id)
}

func (srv *aiAgentService) Update(ctx context.Context, id int, req request.AddAiAgent) (*ent.AiAgent, error) {
	return srv.repo.Update(ctx, id, &ent.AiAgent{
		Name:        req.Name,
		Icon:        req.Icon,
		Description: req.Description,
		Inputs:      req.Inputs,
		Target:      req.Target,
		Method:      req.Method,
		Secret:      req.Secret,
	})
}

func (srv *aiAgentService) GetById(ctx context.Context, id int) (*ent.AiAgent, error) {
	return srv.repo.GetById(ctx, id)
}
