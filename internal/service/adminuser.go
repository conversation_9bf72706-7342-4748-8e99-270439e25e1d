package service

import (
	"bole-ai/internal"
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/repository"
	"context"
	"errors"
)

type adminUserService struct {
	repo repository.AdminUserRepository
}

// AdminUserService statement service's abilities.
// the function's name use Camel-Case define should be like this:
// the filename prefix and the action suffix.
type AdminUserService interface {
	GetByUsername(ctx context.Context, username string) (*ent.AdminUser, error)
	Login(ctx context.Context, req request.AdminUserLogin) (*ent.AdminUser, error)
	// InjectInterface
}

func newAdminUserService(repo repository.AdminUserRepository) AdminUserService {
	return &adminUserService{
		repo: repo,
	}
}

func (srv *adminUserService) GetByUsername(ctx context.Context, username string) (*ent.AdminUser, error) {
	return srv.repo.GetByUsername(ctx, username)
}

func (srv *adminUserService) Login(ctx context.Context, req request.AdminUserLogin) (*ent.AdminUser, error) {
	au, err := srv.repo.GetByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}
	if !comparePwd(au.Password, req.Password) {
		return nil, errors.New(internal.LoginError)
	}
	return au, nil
}

// InjectInterfaceImpl
