package service

import (
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	goRedis "github.com/go-redis/redis/v8"
	"time"
)

// UserInternalMessageService interface
type UserInternalMessageService interface {
	List(ctx context.Context, page, perPage, userId, status int) (response.ListInternalMessage, error)
	Count(ctx context.Context, userId, status int) (int, error)
	Show(ctx context.Context, userId, id int) (*response.InternalMessage, error)
}

// userInternalMessageService implements UserInternalMessageService
type userInternalMessageService struct {
	repo repository.UserInternalMessageRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ UserInternalMessageService = (*userInternalMessageService)(nil)

// constructor
func newUserInternalMessageService(repo repository.UserInternalMessageRepository, rdb *goRedis.Client, conf *model.Conf) UserInternalMessageService {
	return &userInternalMessageService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *userInternalMessageService) List(ctx context.Context, page, perPage, userId, status int) (response.ListInternalMessage, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, userId, status)
	if err != nil {
		return response.ListInternalMessage{}, err
	}
	var out = make([]response.InternalMessage, 0)
	for _, item := range list {
		out = append(out, response.InternalMessage{
			Id:        item.ID,
			Title:     item.Edges.Message.Title,
			Content:   item.Edges.Message.Content,
			CreatedAt: item.CreatedAt.Format(time.DateTime),
			UpdatedAt: item.UpdatedAt.Format(time.DateTime),
			Status:    item.Status,
		})
	}
	return response.ListInternalMessage{
		Pagination: model.Pagination{
			Total:   total,
			PerPage: perPage,
			Page:    page,
		},
		List: out,
	}, nil
}

func (srv *userInternalMessageService) Count(ctx context.Context, userId, status int) (int, error) {
	return srv.repo.Count(ctx, userId, status)
}

func (srv *userInternalMessageService) Show(ctx context.Context, userId, id int) (*response.InternalMessage, error) {
	m, err := srv.repo.Show(ctx, userId, id)
	if err != nil {
		return nil, err
	}
	return &response.InternalMessage{
		Id:        m.ID,
		Title:     m.Edges.Message.Title,
		Content:   m.Edges.Message.Content,
		CreatedAt: m.CreatedAt.Format(time.DateTime),
		UpdatedAt: m.UpdatedAt.Format(time.DateTime),
		Status:    m.Status,
	}, nil
}
