package service

import (
	"bole-ai/internal"
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	"errors"
	"fmt"
	goRedis "github.com/go-redis/redis/v8"
	"gopkg.in/gomail.v2"
	"time"
)

type UserService interface {
	List(ctx context.Context, page, perPage, status int, email string) (response.ListUser, error)
	Export(ctx context.Context, status int, email string) ([]response.User, error)
	Register(context.Context, request.UserRegister) (*ent.User, error)
	Login(context.Context, request.UserLogin) (*ent.User, error)
	GetByUserName(ctx context.Context, name string) (*ent.User, error)
	SetStatus(ctx context.Context, id int) error
	ForgetPassword(ctx context.Context, email string) error
	SendMail(to string, subject string, body string) error
	ResetPassword(ctx context.Context, req request.ResetPasswordRequest) error
	SetPassword(ctx context.Context, req request.SetPasswordRequest) error
}

type userService struct {
	repo repository.UserRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

func (srv *userService) List(ctx context.Context, page, perPage, status int, email string) (response.ListUser, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, status, email)
	if err != nil {
		return response.ListUser{}, err
	}
	var out = make([]response.User, 0)
	for _, add := range list {
		out = append(out, response.User{
			Id:        add.ID,
			Username:  add.Username,
			BatchName: add.Edges.Batch.Name,
			CreatedAt: add.CreatedAt.Format(time.DateTime),
			UpdatedAt: add.UpdatedAt.Format(time.DateTime),
			TestCode:  add.Edges.Code.Code,
			Email:     add.Email,
			Status:    int(add.Status),
		})
	}
	return response.ListUser{
		Pagination: model.Pagination{
			Total:   total,
			PerPage: perPage,
			Page:    page,
		},
		List: out,
	}, nil
}

func (srv *userService) Export(ctx context.Context, status int, email string) ([]response.User, error) {
	list, err := srv.repo.Export(ctx, status, email)
	if err != nil {
		return nil, err
	}
	var out = make([]response.User, 0)
	for _, add := range list {
		out = append(out, response.User{
			Id:        add.ID,
			Username:  add.Username,
			BatchName: add.Edges.Batch.Name,
			CreatedAt: add.CreatedAt.Format(time.DateTime),
			UpdatedAt: add.UpdatedAt.Format(time.DateTime),
			TestCode:  add.Edges.Code.Code,
			Email:     add.Email,
			Status:    int(add.Status),
		})
	}
	return out, nil
}

func (srv *userService) GetByUserName(ctx context.Context, name string) (*ent.User, error) {
	return srv.repo.GetByUserName(ctx, name)
}

var _ UserService = (*userService)(nil)

func newUserService(repo repository.UserRepository, rdb *goRedis.Client, conf *model.Conf) UserService {
	return &userService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *userService) Register(ctx context.Context, req request.UserRegister) (*ent.User, error) {
	user, err := srv.repo.GetByUserName(ctx, req.Username)
	if err != nil {
		if !ent.IsNotFound(err) {
			return user, nil
		}
	}
	if user != nil && user.ID != 0 {
		return nil, errors.New(internal.UserExistError)
	}
	pwd, err := encryptPWD(req.Password)
	if err != nil {
		return nil, err
	}
	return srv.repo.Register(ctx, &ent.User{
		Username:   req.Username,
		Password:   pwd,
		Email:      req.Email,
		TestCodeID: req.TestCodeId,
		BatchID:    req.BatchId,
	})
}

func (srv *userService) Login(ctx context.Context, req request.UserLogin) (*ent.User, error) {
	user, err := srv.repo.GetByUserName(ctx, req.Username)
	if err != nil {
		return nil, err
	}
	if user.Status == 2 {
		return nil, errors.New(internal.UserExistError)
	}
	if !comparePwd(user.Password, req.Password) {
		return nil, errors.New(internal.LoginError)
	}
	return user, nil
}

func (srv *userService) SetStatus(ctx context.Context, id int) error {
	return srv.repo.SetStatus(ctx, id)
}

func (srv *userService) SetPassword(ctx context.Context, req request.SetPasswordRequest) error {
	user, err := srv.repo.GetByUserName(ctx, req.Username)
	if err != nil {
		return err
	}
	if !comparePwd(user.Password, req.OldPassword) {
		return errors.New(internal.PwdError)
	}
	if comparePwd(user.Password, req.Password) {
		return errors.New(internal.SamePwdError)
	}
	pwd, err := encryptPWD(req.Password)
	if err != nil {
		return err
	}
	return srv.repo.SetPassword(ctx, user.ID, pwd)
}

func (srv *userService) ForgetPassword(ctx context.Context, email string) error {
	code := GenerateCode(6) // 6 位验证码
	key := fmt.Sprintf(internal.ResetPWDKey, email)
	exists, err := srv.rdb.Exists(ctx, key).Result()
	if err != nil {
		return err
	}
	if exists == 1 {
		ttl, err := srv.rdb.TTL(ctx, key).Result()
		if err != nil {
			return err
		}
		return fmt.Errorf("please wait %d seconds before requesting a new code", ttl/time.Second)
	}
	err = srv.rdb.Set(ctx, key, code, 5*time.Minute).Err()
	if err != nil {
		return err
	}
	htmlBody := fmt.Sprintf(`
	<!DOCTYPE html>
	<html>
	<head>
		<meta charset="UTF-8">
		<title>%s</title>
		<style>
			body { font-family: Arial, sans-serif; line-height: 1.6; }
			.container { max-width: 600px; margin: 0 auto; padding: 20px; }
			.header { background-color: #1890ff; color: white; padding: 10px; text-align: center; }
			.content { padding: 20px; background-color: #f9f9f9; }
			.code { font-size: 24px; font-weight: bold; color: #1890ff; text-align: center; margin: 20px 0; }
			.footer { margin-top: 20px; font-size: 12px; color: #999; text-align: center; }
		</style>
	</head>
	<body>
		<div class="container">
			<div class="header">
				<h2>密码重置验证码</h2>
			</div>
			<div class="content">
				<p>尊敬的客户：</p>
				<p>您正在申请重置密码，验证码如下：</p>
				<div class="code">%s</div>
				<p>验证码有效期为5分钟，请尽快使用。</p>
				<p>如非本人操作，请忽略此邮件。</p>
			</div>
			<div class="footer">
				<p>© %d %s 版权所有</p>
			</div>
		</div>
	</body>
	</html>
	`, "密码重置验证码", code, time.Now().Year(), srv.conf.Smtp.Company)
	err = srv.SendMail(email, "忘记密码", htmlBody)
	return err
}

func (srv *userService) SendMail(to string, subject string, body string) error {
	m := gomail.NewMessage()
	m.SetHeader("From", srv.conf.Smtp.From)
	m.SetHeader("To", to)
	m.SetHeader("Subject", subject)
	m.SetBody("text/html", body)

	d := gomail.NewDialer(srv.conf.Smtp.Host, srv.conf.Smtp.Port, srv.conf.Smtp.From, srv.conf.Smtp.Password)
	return d.DialAndSend(m)
}

func (srv *userService) ResetPassword(ctx context.Context, req request.ResetPasswordRequest) error {
	key := fmt.Sprintf(internal.ResetPWDKey, req.Email)
	code, err := srv.rdb.Get(ctx, key).Result()
	if err != nil {
		return errors.New(internal.InvalidCode)
	}
	if code != req.Code {
		return errors.New(internal.InvalidCode)
	}
	user, err := srv.repo.GetByEmail(ctx, req.Email)
	if err != nil {
		return err
	}
	pwd, err := encryptPWD(req.Password)
	if err != nil {
		return err
	}
	return srv.repo.SetPassword(ctx, user.ID, pwd)
}
