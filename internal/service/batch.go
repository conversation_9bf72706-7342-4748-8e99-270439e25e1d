package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	"time"
)

type BatchService interface {
	Add(ctx context.Context, req request.AddBatch) error
	List(ctx context.Context, page, perPage int) (response.ListBatch, error)
}

type batchService struct {
	repo repository.BatchRepository
}

func (srv *batchService) Add(ctx context.Context, req request.AddBatch) error {
	expiredAt := time.Now().AddDate(100, 0, 0)
	if req.ExpiredAt != 0 {
		expiredAt = time.UnixMilli(req.ExpiredAt)
	}
	return srv.repo.Add(ctx, &ent.Batch{
		Name:      req.Name,
		Num:       int64(req.Num),
		ExpiredAt: expiredAt,
	})
}

func (srv *batchService) List(ctx context.Context, page, perPage int) (response.ListBatch, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage)
	if err != nil {
		return response.ListBatch{}, err
	}

	var out = make([]response.Batch, 0)
	for _, item := range list {
		out = append(out, response.Batch{
			Id:        int64(item.ID),
			Name:      item.Name,
			Num:       item.Num,
			UsedNum:   int64(len(item.Edges.Users)),
			CreatedAt: item.CreatedAt.Format(time.DateTime),
			ExpiredAt: item.ExpiredAt.Format(time.DateTime),
		})
	}
	return response.ListBatch{
		Pagination: model.Pagination{
			Total:   total,
			Page:    page,
			PerPage: perPage,
		},
		List: out,
	}, nil
}

func newBatchService(repo repository.BatchRepository) BatchService {
	return &batchService{
		repo: repo,
	}
}
