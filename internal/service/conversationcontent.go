package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	goRedis "github.com/go-redis/redis/v8"
)

// ConversationContentService interface
type ConversationContentService interface {
	Add(ctx context.Context, req request.AddConversationContent) (*ent.ConversationContent, error)
}

// conversationContentService implements ConversationContentService
type conversationContentService struct {
	repo repository.ConversationContentRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ ConversationContentService = (*conversationContentService)(nil)

// constructor
func newConversationContentService(repo repository.ConversationContentRepository, rdb *goRedis.Client, conf *model.Conf) ConversationContentService {
	return &conversationContentService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *conversationContentService) Add(ctx context.Context, req request.AddConversationContent) (*ent.ConversationContent, error) {
	return srv.repo.Add(ctx, &ent.ConversationContent{
		ConversationID: req.ConversationId,
		Content:        req.Content,
		Type:           req.Type,
	})
}
