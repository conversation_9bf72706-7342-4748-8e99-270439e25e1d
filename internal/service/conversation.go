package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	goRedis "github.com/go-redis/redis/v8"
	"time"
)

// ConversationService interface
type ConversationService interface {
	Add(ctx context.Context, req request.AddConversation) (*ent.Conversation, error)
	GetByConversationId(ctx context.Context, conversationId string, userId int) (*ent.Conversation, error)
	List(ctx context.Context, page, perPage, userId int) (response.ListConversation, error)
	Show(ctx context.Context, id string, userId int) (*response.Conversation, error)
	Delete(ctx context.Context, conversationID string, userId int) (int, error)
}

// conversationService implements ConversationService
type conversationService struct {
	repo repository.ConversationRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ ConversationService = (*conversationService)(nil)

// constructor
func newConversationService(repo repository.ConversationRepository, rdb *goRedis.Client, conf *model.Conf) ConversationService {
	return &conversationService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *conversationService) Add(ctx context.Context, req request.AddConversation) (*ent.Conversation, error) {
	return srv.repo.Add(ctx, &ent.Conversation{
		Name:                  req.Name,
		AiAgentID:             req.AiAgentId,
		UserID:                req.UserId,
		AiAgentConversationID: req.ConversationId,
	})
}

func (srv *conversationService) GetByConversationId(ctx context.Context, conversationId string, userId int) (*ent.Conversation, error) {
	return srv.repo.GetByConversationId(ctx, conversationId, userId)
}

func (srv *conversationService) List(ctx context.Context, page, perPage, userId int) (response.ListConversation, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, userId)
	if err != nil {
		return response.ListConversation{}, err
	}

	var out = make([]response.Conversation, 0)
	for _, item := range list {
		out = append(out, response.Conversation{
			Id:             item.ID,
			ConversationId: item.AiAgentConversationID,
			Name:           item.Name,
			UpdatedAt:      item.UpdatedAt.Format(time.DateTime),
		})
	}
	return response.ListConversation{
		Pagination: model.Pagination{
			Total:   total,
			Page:    page,
			PerPage: perPage,
		},
		List: out,
	}, nil
}

func (srv *conversationService) Show(ctx context.Context, id string, userId int) (*response.Conversation, error) {
	c, err := srv.repo.GetByConversationId(ctx, id, userId)
	if err != nil {
		return nil, err
	}
	var contents = make([]response.ConversationContent, 0)

	for _, content := range c.Edges.Contents {
		contents = append(contents, response.ConversationContent{
			Type:      content.Type,
			Content:   content.Content,
			CreatedAt: content.CreatedAt.Format(time.DateTime),
		})
	}
	return &response.Conversation{
		Id:             c.ID,
		Name:           c.Name,
		ConversationId: c.AiAgentConversationID,
		CreatedAt:      c.CreatedAt.Format(time.DateTime),
		AiAgent: response.AiAgent{
			Id:    c.Edges.AiAgent.ID,
			Name:  c.Edges.AiAgent.Name,
			Param: c.Edges.AiAgent.Inputs,
			Guide: c.Edges.AiAgent.Guide,
		},
		Contents: contents,
	}, nil
}
func (srv *conversationService) Delete(ctx context.Context, conversationID string, userId int) (int, error) {
	return srv.repo.Delete(ctx, conversationID, userId)
}
