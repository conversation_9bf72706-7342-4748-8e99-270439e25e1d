package service

import (
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	goRedis "github.com/go-redis/redis/v8"
)

type service struct {
	conf *model.Conf
	repo repository.Repository
	rdb  *goRedis.Client
}

type Service interface {
	NewFrequentlyAskedQuestionsService() FrequentlyAskedQuestionsService
	NewConversationContentService() ConversationContentService
	NewAiAgentService() AiAgentService
	NewConversationService() ConversationService
	NewUserInternalMessageService() UserInternalMessageService
	NewInternalMessageService() InternalMessageService
	NewUserService() UserService
	NewTestCodeService() TestCodeService
	NewScreenplayService() ScreenplayService
	NewProjectService() ProjectService
	NewAdminUserService() AdminUserService
	NewBatchService() BatchService
}

func NewService(
	conf *model.Conf,
	repo repository.Repository,
	rdb *goRedis.Client,
) Service {
	return &service{
		conf: conf,
		repo: repo,
		rdb:  rdb,
	}
}

func (srv *service) NewFrequentlyAskedQuestionsService() FrequentlyAskedQuestionsService {
	return newFrequentlyAskedQuestionsService(srv.repo.NewFrequentlyAskedQuestionsRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewConversationContentService() ConversationContentService {
	return newConversationContentService(srv.repo.NewConversationContentRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewAiAgentService() AiAgentService {
	return newAiAgentService(srv.repo.NewAiAgentRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewConversationService() ConversationService {
	return newConversationService(srv.repo.NewConversationRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewUserInternalMessageService() UserInternalMessageService {
	return newUserInternalMessageService(srv.repo.NewUserInternalMessageRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewInternalMessageService() InternalMessageService {
	return newInternalMessageService(srv.repo.NewInternalMessageRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewUserService() UserService {
	return newUserService(srv.repo.NewUserRepository(), srv.rdb, srv.conf)
}

func (srv *service) NewTestCodeService() TestCodeService {
	return newTestCodeService(srv.repo.NewTestCodeRepository())
}

func (srv *service) NewScreenplayService() ScreenplayService {
	return newScreenplayService(srv.repo.NewScreenplayRepository())
}

func (srv *service) NewProjectService() ProjectService {
	return newProjectService(srv.repo.NewProjectRepository())
}

func (srv *service) NewAdminUserService() AdminUserService {
	return newAdminUserService(srv.repo.NewAdminUserRepository())
}

func (srv *service) NewBatchService() BatchService {
	return newBatchService(srv.repo.NewBatchRepository())
}
