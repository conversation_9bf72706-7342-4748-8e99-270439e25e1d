package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	"time"
)

type TestCodeService interface {
	Supplement(context.Context) error
	GetByCode(ctx context.Context, code string) (*ent.TestCode, error)
	GetOneTestCode(ctx context.Context, device string) (*ent.TestCode, error)
	List(ctx context.Context, page, perPage, batchId int) (response.ListTestCode, error)
}

type testCodeService struct {
	repo repository.TestCodeRepository
}

var _ TestCodeService = (*testCodeService)(nil)

func newTestCodeService(repo repository.TestCodeRepository) TestCodeService {
	return &testCodeService{
		repo: repo,
	}
}

func (srv *testCodeService) Supplement(ctx context.Context) error {
	return srv.repo.Supplement(ctx)
}

func (srv *testCodeService) GetByCode(ctx context.Context, code string) (*ent.TestCode, error) {
	return srv.repo.GetByCode(ctx, code)
}

func (srv *testCodeService) GetOneTestCode(ctx context.Context, device string) (*ent.TestCode, error) {
	return srv.repo.GetOneTestCode(ctx, device)
}

func (srv *testCodeService) List(ctx context.Context, page, perPage, batchId int) (response.ListTestCode, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, batchId)
	if err != nil {
		return response.ListTestCode{}, err
	}

	var out = make([]response.TestCode, 0)
	for _, item := range list {
		email := ""
		usedAt := ""
		if item.Edges.User != nil {
			email = item.Edges.User.Email
		}
		if item.Status == 3 {
			usedAt = item.UpdatedAt.Format(time.DateTime)
		}
		out = append(out, response.TestCode{
			Id:        item.ID,
			Code:      item.Code,
			Status:    int(item.Status),
			Email:     email,
			UsedAt:    usedAt,
			ExpiredAt: item.ExpiredAt.Format(time.DateTime),
		})
	}
	return response.ListTestCode{
		Pagination: model.Pagination{
			Total:   total,
			Page:    page,
			PerPage: perPage,
		},
		List: out,
	}, nil
}
