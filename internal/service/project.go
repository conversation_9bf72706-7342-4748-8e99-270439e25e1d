package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
)

type ProjectService interface {
	List(ctx context.Context, page, perPage, userId int) (response.ListProject, error)
	Add(ctx context.Context, req request.AddProject) (response.AddProject, error)
	Update(ctx context.Context, id int, req request.AddProject) error
}

type projectService struct {
	repo repository.ProjectRepository
}

var _ ProjectService = (*projectService)(nil)

func newProjectService(repo repository.ProjectRepository) ProjectService {
	return &projectService{
		repo: repo,
	}
}

func (srv *projectService) List(ctx context.Context, page, perPage, userId int) (response.ListProject, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, userId)
	if err != nil {
		return response.ListProject{}, err
	}

	var out = make([]response.AddProject, 0)
	for _, item := range list {
		out = append(out, response.AddProject{
			Id:   int64(item.ID),
			Name: item.Name,
		})
	}
	return response.ListProject{
		Pagination: model.Pagination{
			Total:   total,
			Page:    page,
			PerPage: perPage,
		},
		List: out,
	}, nil
}

func (srv *projectService) Add(ctx context.Context, req request.AddProject) (response.AddProject, error) {
	add, err := srv.repo.Add(ctx, &ent.Project{
		Name:   req.Name,
		UserID: req.UserId,
	})
	if err != nil {
		return response.AddProject{}, err
	}
	return response.AddProject{
		Id:   int64(add.ID),
		Name: add.Name,
	}, err
}

func (srv *projectService) Update(ctx context.Context, id int, req request.AddProject) error {
	_, err := srv.repo.Update(ctx, id, &ent.Project{
		Name: req.Name,
	})
	return err
}
