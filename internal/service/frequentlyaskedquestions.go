package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	"github.com/gin-gonic/gin"
	goRedis "github.com/go-redis/redis/v8"
)

// FrequentlyAskedQuestionsService interface
type FrequentlyAskedQuestionsService interface {
	GetLatest(ctx *gin.Context) (*ent.FrequentAskQuestion, error)
	Add(ctx context.Context, req request.AddFrequentAskQuestion) (*ent.FrequentAskQuestion, error)
}

// frequentlyAskedQuestionsService implements FrequentlyAskedQuestionsService
type frequentlyAskedQuestionsService struct {
	repo repository.FrequentlyAskedQuestionsRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ FrequentlyAskedQuestionsService = (*frequentlyAskedQuestionsService)(nil)

// constructor
func newFrequentlyAskedQuestionsService(repo repository.FrequentlyAskedQuestionsRepository, rdb *goRedis.Client, conf *model.Conf) FrequentlyAskedQuestionsService {
	return &frequentlyAskedQuestionsService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *frequentlyAskedQuestionsService) GetLatest(ctx *gin.Context) (*ent.FrequentAskQuestion, error) {
	return srv.repo.GetLatest(ctx)
}

func (srv *frequentlyAskedQuestionsService) Add(ctx context.Context, req request.AddFrequentAskQuestion) (*ent.FrequentAskQuestion, error) {
	return srv.repo.Add(ctx, &ent.FrequentAskQuestion{
		Content: req.Content,
	})
}
