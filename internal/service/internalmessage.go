package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
	goRedis "github.com/go-redis/redis/v8"
	"time"
)

// InternalMessageService interface
type InternalMessageService interface {
	List(ctx context.Context, page, perPage int) (response.ListInternalMessage, error)
	Add(ctx context.Context, req request.AddInternalMessage) error
}

// internalMessageService implements InternalMessageService
type internalMessageService struct {
	repo repository.InternalMessageRepository
	rdb  *goRedis.Client
	conf *model.Conf
}

// compile-time check
var _ InternalMessageService = (*internalMessageService)(nil)

// constructor
func newInternalMessageService(repo repository.InternalMessageRepository, rdb *goRedis.Client, conf *model.Conf) InternalMessageService {
	return &internalMessageService{
		repo: repo,
		rdb:  rdb,
		conf: conf,
	}
}

func (srv *internalMessageService) List(ctx context.Context, page, perPage int) (response.ListInternalMessage, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage)
	if err != nil {
		return response.ListInternalMessage{}, err
	}
	var out = make([]response.InternalMessage, 0)
	for _, item := range list {
		out = append(out, response.InternalMessage{
			Id:        item.ID,
			Title:     item.Title,
			Content:   item.Content,
			CreatedAt: item.CreatedAt.Format(time.DateTime),
			UpdatedAt: item.UpdatedAt.Format(time.DateTime),
		})
	}
	return response.ListInternalMessage{
		Pagination: model.Pagination{
			Total:   total,
			PerPage: perPage,
			Page:    page,
		},
		List: out,
	}, nil
}

func (srv *internalMessageService) Add(ctx context.Context, req request.AddInternalMessage) error {
	_, err := srv.repo.Add(ctx, &ent.InternalMessage{
		Title:   req.Title,
		Content: req.Content,
	}, req.UserIds)
	return err
}
