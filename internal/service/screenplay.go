package service

import (
	"bole-ai/internal/ent"
	"bole-ai/internal/httptransport/request"
	"bole-ai/internal/httptransport/response"
	"bole-ai/internal/model"
	"bole-ai/internal/repository"
	"context"
)

type ScreenplayService interface {
	List(ctx context.Context, page, perPage, projectId, userId int) (response.ListScreenplay, error)
	Add(ctx context.Context, req request.AddScreenplay) (response.AddScreenplay, error)
	Update(ctx context.Context, id int, req request.UpdateScreenplay) error
	Delete(ctx context.Context, id int) (int, error)
	Show(ctx context.Context, id, userId int) (*ent.Screenplay, error)
}

type screenplayService struct {
	repo repository.ScreenplayRepository
}

var _ ScreenplayService = (*screenplayService)(nil)

func newScreenplayService(repo repository.ScreenplayRepository) ScreenplayService {
	return &screenplayService{
		repo: repo,
	}
}

func (srv *screenplayService) Add(ctx context.Context, req request.AddScreenplay) (response.AddScreenplay, error) {
	add, err := srv.repo.Add(ctx, &ent.Screenplay{
		Title:     req.Title,
		Content:   req.Content,
		UserID:    int64(req.UserId),
		ProjectID: int64(req.ProjectId),
		Type:      int8(req.Type),
	})
	if err != nil {
		return response.AddScreenplay{}, err
	}
	return response.AddScreenplay{
		Id:        add.ID,
		ProjectId: int(add.ProjectID),
		Title:     add.Title,
		Content:   add.Content,
		Type:      add.Type,
	}, err
}

func (srv *screenplayService) Update(ctx context.Context, id int, req request.UpdateScreenplay) error {
	_, err := srv.repo.Update(ctx, id, &ent.Screenplay{
		Title:   req.Title,
		Content: req.Content,
	})
	return err
}

func (srv *screenplayService) List(ctx context.Context, page, perPage, projectId, userId int) (response.ListScreenplay, error) {
	total, list, err := srv.repo.List(ctx, (page-1)*perPage, perPage, projectId, userId)
	if err != nil {
		return response.ListScreenplay{}, err
	}
	var out = make([]response.AddScreenplay, 0)
	for _, add := range list {
		out = append(out, response.AddScreenplay{
			Id:        add.ID,
			Title:     add.Title,
			Content:   add.Content,
			ProjectId: projectId,
			Type:      add.Type,
		})
	}
	return response.ListScreenplay{
		Pagination: model.Pagination{
			Total:   total,
			PerPage: perPage,
			Page:    page,
		},
		List: out,
	}, nil
}

func (srv *screenplayService) Delete(ctx context.Context, id int) (int, error) {
	//TODO implement me
	panic("implement me")
}

func (srv *screenplayService) Show(ctx context.Context, id, userId int) (*ent.Screenplay, error) {
	return srv.repo.Show(ctx, id, userId)
}
