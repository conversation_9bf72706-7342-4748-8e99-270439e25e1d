// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 批次表
type Batch struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 批次名称
	Name string `json:"name,omitempty"`
	// 测试码生成数量
	Num int64 `json:"num,omitempty"`
	// 过期时间
	ExpiredAt time.Time `json:"expired_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the BatchQuery when eager-loading is set.
	Edges        BatchEdges `json:"edges"`
	selectValues sql.SelectValues
}

// BatchEdges holds the relations/edges for other nodes in the graph.
type BatchEdges struct {
	// Codes holds the value of the codes edge.
	Codes []*TestCode `json:"codes,omitempty"`
	// Users holds the value of the users edge.
	Users []*User `json:"users,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// CodesOrErr returns the Codes value or an error if the edge
// was not loaded in eager-loading.
func (e BatchEdges) CodesOrErr() ([]*TestCode, error) {
	if e.loadedTypes[0] {
		return e.Codes, nil
	}
	return nil, &NotLoadedError{edge: "codes"}
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading.
func (e BatchEdges) UsersOrErr() ([]*User, error) {
	if e.loadedTypes[1] {
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Batch) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case batch.FieldID, batch.FieldNum:
			values[i] = new(sql.NullInt64)
		case batch.FieldName:
			values[i] = new(sql.NullString)
		case batch.FieldCreatedAt, batch.FieldUpdatedAt, batch.FieldExpiredAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Batch fields.
func (b *Batch) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case batch.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			b.ID = int(value.Int64)
		case batch.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				b.CreatedAt = value.Time
			}
		case batch.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				b.UpdatedAt = value.Time
			}
		case batch.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				b.Name = value.String
			}
		case batch.FieldNum:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field num", values[i])
			} else if value.Valid {
				b.Num = value.Int64
			}
		case batch.FieldExpiredAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expired_at", values[i])
			} else if value.Valid {
				b.ExpiredAt = value.Time
			}
		default:
			b.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Batch.
// This includes values selected through modifiers, order, etc.
func (b *Batch) Value(name string) (ent.Value, error) {
	return b.selectValues.Get(name)
}

// QueryCodes queries the "codes" edge of the Batch entity.
func (b *Batch) QueryCodes() *TestCodeQuery {
	return NewBatchClient(b.config).QueryCodes(b)
}

// QueryUsers queries the "users" edge of the Batch entity.
func (b *Batch) QueryUsers() *UserQuery {
	return NewBatchClient(b.config).QueryUsers(b)
}

// Update returns a builder for updating this Batch.
// Note that you need to call Batch.Unwrap() before calling this method if this Batch
// was returned from a transaction, and the transaction was committed or rolled back.
func (b *Batch) Update() *BatchUpdateOne {
	return NewBatchClient(b.config).UpdateOne(b)
}

// Unwrap unwraps the Batch entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (b *Batch) Unwrap() *Batch {
	_tx, ok := b.config.driver.(*txDriver)
	if !ok {
		panic("ent: Batch is not a transactional entity")
	}
	b.config.driver = _tx.drv
	return b
}

// String implements the fmt.Stringer.
func (b *Batch) String() string {
	var builder strings.Builder
	builder.WriteString("Batch(")
	builder.WriteString(fmt.Sprintf("id=%v, ", b.ID))
	builder.WriteString("created_at=")
	builder.WriteString(b.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(b.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(b.Name)
	builder.WriteString(", ")
	builder.WriteString("num=")
	builder.WriteString(fmt.Sprintf("%v", b.Num))
	builder.WriteString(", ")
	builder.WriteString("expired_at=")
	builder.WriteString(b.ExpiredAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// Batches is a parsable slice of Batch.
type Batches []*Batch
