// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/predicate"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AiAgentQuery is the builder for querying AiAgent entities.
type AiAgentQuery struct {
	config
	ctx               *QueryContext
	order             []aiagent.OrderOption
	inters            []Interceptor
	predicates        []predicate.AiAgent
	withConversations *ConversationQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the AiAgentQuery builder.
func (aaq *AiAgentQuery) Where(ps ...predicate.AiAgent) *AiAgentQuery {
	aaq.predicates = append(aaq.predicates, ps...)
	return aaq
}

// Limit the number of records to be returned by this query.
func (aaq *AiAgentQuery) Limit(limit int) *AiAgentQuery {
	aaq.ctx.Limit = &limit
	return aaq
}

// Offset to start from.
func (aaq *AiAgentQuery) Offset(offset int) *AiAgentQuery {
	aaq.ctx.Offset = &offset
	return aaq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (aaq *AiAgentQuery) Unique(unique bool) *AiAgentQuery {
	aaq.ctx.Unique = &unique
	return aaq
}

// Order specifies how the records should be ordered.
func (aaq *AiAgentQuery) Order(o ...aiagent.OrderOption) *AiAgentQuery {
	aaq.order = append(aaq.order, o...)
	return aaq
}

// QueryConversations chains the current query on the "conversations" edge.
func (aaq *AiAgentQuery) QueryConversations() *ConversationQuery {
	query := (&ConversationClient{config: aaq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := aaq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := aaq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(aiagent.Table, aiagent.FieldID, selector),
			sqlgraph.To(conversation.Table, conversation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, aiagent.ConversationsTable, aiagent.ConversationsColumn),
		)
		fromU = sqlgraph.SetNeighbors(aaq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first AiAgent entity from the query.
// Returns a *NotFoundError when no AiAgent was found.
func (aaq *AiAgentQuery) First(ctx context.Context) (*AiAgent, error) {
	nodes, err := aaq.Limit(1).All(setContextOp(ctx, aaq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{aiagent.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (aaq *AiAgentQuery) FirstX(ctx context.Context) *AiAgent {
	node, err := aaq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first AiAgent ID from the query.
// Returns a *NotFoundError when no AiAgent ID was found.
func (aaq *AiAgentQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = aaq.Limit(1).IDs(setContextOp(ctx, aaq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{aiagent.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (aaq *AiAgentQuery) FirstIDX(ctx context.Context) int {
	id, err := aaq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single AiAgent entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one AiAgent entity is found.
// Returns a *NotFoundError when no AiAgent entities are found.
func (aaq *AiAgentQuery) Only(ctx context.Context) (*AiAgent, error) {
	nodes, err := aaq.Limit(2).All(setContextOp(ctx, aaq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{aiagent.Label}
	default:
		return nil, &NotSingularError{aiagent.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (aaq *AiAgentQuery) OnlyX(ctx context.Context) *AiAgent {
	node, err := aaq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only AiAgent ID in the query.
// Returns a *NotSingularError when more than one AiAgent ID is found.
// Returns a *NotFoundError when no entities are found.
func (aaq *AiAgentQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = aaq.Limit(2).IDs(setContextOp(ctx, aaq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{aiagent.Label}
	default:
		err = &NotSingularError{aiagent.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (aaq *AiAgentQuery) OnlyIDX(ctx context.Context) int {
	id, err := aaq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of AiAgents.
func (aaq *AiAgentQuery) All(ctx context.Context) ([]*AiAgent, error) {
	ctx = setContextOp(ctx, aaq.ctx, ent.OpQueryAll)
	if err := aaq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*AiAgent, *AiAgentQuery]()
	return withInterceptors[[]*AiAgent](ctx, aaq, qr, aaq.inters)
}

// AllX is like All, but panics if an error occurs.
func (aaq *AiAgentQuery) AllX(ctx context.Context) []*AiAgent {
	nodes, err := aaq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of AiAgent IDs.
func (aaq *AiAgentQuery) IDs(ctx context.Context) (ids []int, err error) {
	if aaq.ctx.Unique == nil && aaq.path != nil {
		aaq.Unique(true)
	}
	ctx = setContextOp(ctx, aaq.ctx, ent.OpQueryIDs)
	if err = aaq.Select(aiagent.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (aaq *AiAgentQuery) IDsX(ctx context.Context) []int {
	ids, err := aaq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (aaq *AiAgentQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, aaq.ctx, ent.OpQueryCount)
	if err := aaq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, aaq, querierCount[*AiAgentQuery](), aaq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (aaq *AiAgentQuery) CountX(ctx context.Context) int {
	count, err := aaq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (aaq *AiAgentQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, aaq.ctx, ent.OpQueryExist)
	switch _, err := aaq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (aaq *AiAgentQuery) ExistX(ctx context.Context) bool {
	exist, err := aaq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the AiAgentQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (aaq *AiAgentQuery) Clone() *AiAgentQuery {
	if aaq == nil {
		return nil
	}
	return &AiAgentQuery{
		config:            aaq.config,
		ctx:               aaq.ctx.Clone(),
		order:             append([]aiagent.OrderOption{}, aaq.order...),
		inters:            append([]Interceptor{}, aaq.inters...),
		predicates:        append([]predicate.AiAgent{}, aaq.predicates...),
		withConversations: aaq.withConversations.Clone(),
		// clone intermediate query.
		sql:  aaq.sql.Clone(),
		path: aaq.path,
	}
}

// WithConversations tells the query-builder to eager-load the nodes that are connected to
// the "conversations" edge. The optional arguments are used to configure the query builder of the edge.
func (aaq *AiAgentQuery) WithConversations(opts ...func(*ConversationQuery)) *AiAgentQuery {
	query := (&ConversationClient{config: aaq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	aaq.withConversations = query
	return aaq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.AiAgent.Query().
//		GroupBy(aiagent.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (aaq *AiAgentQuery) GroupBy(field string, fields ...string) *AiAgentGroupBy {
	aaq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &AiAgentGroupBy{build: aaq}
	grbuild.flds = &aaq.ctx.Fields
	grbuild.label = aiagent.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.AiAgent.Query().
//		Select(aiagent.FieldCreatedAt).
//		Scan(ctx, &v)
func (aaq *AiAgentQuery) Select(fields ...string) *AiAgentSelect {
	aaq.ctx.Fields = append(aaq.ctx.Fields, fields...)
	sbuild := &AiAgentSelect{AiAgentQuery: aaq}
	sbuild.label = aiagent.Label
	sbuild.flds, sbuild.scan = &aaq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a AiAgentSelect configured with the given aggregations.
func (aaq *AiAgentQuery) Aggregate(fns ...AggregateFunc) *AiAgentSelect {
	return aaq.Select().Aggregate(fns...)
}

func (aaq *AiAgentQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range aaq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, aaq); err != nil {
				return err
			}
		}
	}
	for _, f := range aaq.ctx.Fields {
		if !aiagent.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if aaq.path != nil {
		prev, err := aaq.path(ctx)
		if err != nil {
			return err
		}
		aaq.sql = prev
	}
	return nil
}

func (aaq *AiAgentQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*AiAgent, error) {
	var (
		nodes       = []*AiAgent{}
		_spec       = aaq.querySpec()
		loadedTypes = [1]bool{
			aaq.withConversations != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*AiAgent).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &AiAgent{config: aaq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, aaq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := aaq.withConversations; query != nil {
		if err := aaq.loadConversations(ctx, query, nodes,
			func(n *AiAgent) { n.Edges.Conversations = []*Conversation{} },
			func(n *AiAgent, e *Conversation) { n.Edges.Conversations = append(n.Edges.Conversations, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (aaq *AiAgentQuery) loadConversations(ctx context.Context, query *ConversationQuery, nodes []*AiAgent, init func(*AiAgent), assign func(*AiAgent, *Conversation)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*AiAgent)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(conversation.FieldAiAgentID)
	}
	query.Where(predicate.Conversation(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(aiagent.ConversationsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.AiAgentID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "ai_agent_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (aaq *AiAgentQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := aaq.querySpec()
	_spec.Node.Columns = aaq.ctx.Fields
	if len(aaq.ctx.Fields) > 0 {
		_spec.Unique = aaq.ctx.Unique != nil && *aaq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, aaq.driver, _spec)
}

func (aaq *AiAgentQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(aiagent.Table, aiagent.Columns, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt))
	_spec.From = aaq.sql
	if unique := aaq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if aaq.path != nil {
		_spec.Unique = true
	}
	if fields := aaq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagent.FieldID)
		for i := range fields {
			if fields[i] != aiagent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := aaq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := aaq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := aaq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := aaq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (aaq *AiAgentQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(aaq.driver.Dialect())
	t1 := builder.Table(aiagent.Table)
	columns := aaq.ctx.Fields
	if len(columns) == 0 {
		columns = aiagent.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if aaq.sql != nil {
		selector = aaq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if aaq.ctx.Unique != nil && *aaq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range aaq.predicates {
		p(selector)
	}
	for _, p := range aaq.order {
		p(selector)
	}
	if offset := aaq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := aaq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// AiAgentGroupBy is the group-by builder for AiAgent entities.
type AiAgentGroupBy struct {
	selector
	build *AiAgentQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (aagb *AiAgentGroupBy) Aggregate(fns ...AggregateFunc) *AiAgentGroupBy {
	aagb.fns = append(aagb.fns, fns...)
	return aagb
}

// Scan applies the selector query and scans the result into the given value.
func (aagb *AiAgentGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aagb.build.ctx, ent.OpQueryGroupBy)
	if err := aagb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentQuery, *AiAgentGroupBy](ctx, aagb.build, aagb, aagb.build.inters, v)
}

func (aagb *AiAgentGroupBy) sqlScan(ctx context.Context, root *AiAgentQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(aagb.fns))
	for _, fn := range aagb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*aagb.flds)+len(aagb.fns))
		for _, f := range *aagb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*aagb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aagb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// AiAgentSelect is the builder for selecting fields of AiAgent entities.
type AiAgentSelect struct {
	*AiAgentQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (aas *AiAgentSelect) Aggregate(fns ...AggregateFunc) *AiAgentSelect {
	aas.fns = append(aas.fns, fns...)
	return aas
}

// Scan applies the selector query and scans the result into the given value.
func (aas *AiAgentSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, aas.ctx, ent.OpQuerySelect)
	if err := aas.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*AiAgentQuery, *AiAgentSelect](ctx, aas.AiAgentQuery, aas, aas.inters, v)
}

func (aas *AiAgentSelect) sqlScan(ctx context.Context, root *AiAgentQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(aas.fns))
	for _, fn := range aas.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*aas.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := aas.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
