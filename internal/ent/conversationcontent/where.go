// Code generated by ent, DO NOT EDIT.

package conversationcontent

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldUpdatedAt, v))
}

// ConversationID applies equality check predicate on the "conversation_id" field. It's identical to ConversationIDEQ.
func ConversationID(v int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldConversationID, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldContent, v))
}

// Type applies equality check predicate on the "type" field. It's identical to TypeEQ.
func Type(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldType, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLTE(FieldUpdatedAt, v))
}

// ConversationIDEQ applies the EQ predicate on the "conversation_id" field.
func ConversationIDEQ(v int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldConversationID, v))
}

// ConversationIDNEQ applies the NEQ predicate on the "conversation_id" field.
func ConversationIDNEQ(v int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldConversationID, v))
}

// ConversationIDIn applies the In predicate on the "conversation_id" field.
func ConversationIDIn(vs ...int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldConversationID, vs...))
}

// ConversationIDNotIn applies the NotIn predicate on the "conversation_id" field.
func ConversationIDNotIn(vs ...int) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldConversationID, vs...))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldContainsFold(FieldContent, v))
}

// TypeEQ applies the EQ predicate on the "type" field.
func TypeEQ(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldEQ(FieldType, v))
}

// TypeNEQ applies the NEQ predicate on the "type" field.
func TypeNEQ(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNEQ(FieldType, v))
}

// TypeIn applies the In predicate on the "type" field.
func TypeIn(vs ...int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldIn(FieldType, vs...))
}

// TypeNotIn applies the NotIn predicate on the "type" field.
func TypeNotIn(vs ...int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldNotIn(FieldType, vs...))
}

// TypeGT applies the GT predicate on the "type" field.
func TypeGT(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGT(FieldType, v))
}

// TypeGTE applies the GTE predicate on the "type" field.
func TypeGTE(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldGTE(FieldType, v))
}

// TypeLT applies the LT predicate on the "type" field.
func TypeLT(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLT(FieldType, v))
}

// TypeLTE applies the LTE predicate on the "type" field.
func TypeLTE(v int8) predicate.ConversationContent {
	return predicate.ConversationContent(sql.FieldLTE(FieldType, v))
}

// HasConversation applies the HasEdge predicate on the "conversation" edge.
func HasConversation() predicate.ConversationContent {
	return predicate.ConversationContent(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ConversationTable, ConversationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasConversationWith applies the HasEdge predicate on the "conversation" edge with a given conditions (other predicates).
func HasConversationWith(preds ...predicate.Conversation) predicate.ConversationContent {
	return predicate.ConversationContent(func(s *sql.Selector) {
		step := newConversationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.ConversationContent) predicate.ConversationContent {
	return predicate.ConversationContent(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.ConversationContent) predicate.ConversationContent {
	return predicate.ConversationContent(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.ConversationContent) predicate.ConversationContent {
	return predicate.ConversationContent(sql.NotPredicates(p))
}
