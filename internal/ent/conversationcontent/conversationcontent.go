// Code generated by ent, DO NOT EDIT.

package conversationcontent

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the conversationcontent type in the database.
	Label = "conversation_content"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldConversationID holds the string denoting the conversation_id field in the database.
	FieldConversationID = "conversation_id"
	// FieldContent holds the string denoting the content field in the database.
	FieldContent = "content"
	// FieldType holds the string denoting the type field in the database.
	FieldType = "type"
	// EdgeConversation holds the string denoting the conversation edge name in mutations.
	EdgeConversation = "conversation"
	// Table holds the table name of the conversationcontent in the database.
	Table = "conversation_contents"
	// ConversationTable is the table that holds the conversation relation/edge.
	ConversationTable = "conversation_contents"
	// ConversationInverseTable is the table name for the Conversation entity.
	// It exists in this package in order to avoid circular dependency with the "conversation" package.
	ConversationInverseTable = "conversations"
	// ConversationColumn is the table column denoting the conversation relation/edge.
	ConversationColumn = "conversation_id"
)

// Columns holds all SQL columns for conversationcontent fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldConversationID,
	FieldContent,
	FieldType,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultType holds the default value on creation for the "type" field.
	DefaultType int8
)

// OrderOption defines the ordering options for the ConversationContent queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByConversationID orders the results by the conversation_id field.
func ByConversationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldConversationID, opts...).ToFunc()
}

// ByContent orders the results by the content field.
func ByContent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldContent, opts...).ToFunc()
}

// ByType orders the results by the type field.
func ByType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldType, opts...).ToFunc()
}

// ByConversationField orders the results by conversation field.
func ByConversationField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newConversationStep(), sql.OrderByField(field, opts...))
	}
}
func newConversationStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ConversationInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ConversationTable, ConversationColumn),
	)
}
