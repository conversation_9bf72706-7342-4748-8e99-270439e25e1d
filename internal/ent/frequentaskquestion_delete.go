// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/predicate"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrequentAskQuestionDelete is the builder for deleting a FrequentAskQuestion entity.
type FrequentAskQuestionDelete struct {
	config
	hooks    []Hook
	mutation *FrequentAskQuestionMutation
}

// Where appends a list predicates to the FrequentAskQuestionDelete builder.
func (faqd *FrequentAskQuestionDelete) Where(ps ...predicate.FrequentAskQuestion) *FrequentAskQuestionDelete {
	faqd.mutation.Where(ps...)
	return faqd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (faqd *FrequentAskQuestionDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, faqd.sqlExec, faqd.mutation, faqd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (faqd *FrequentAskQuestionDelete) ExecX(ctx context.Context) int {
	n, err := faqd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (faqd *FrequentAskQuestionDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(frequentaskquestion.Table, sqlgraph.NewFieldSpec(frequentaskquestion.FieldID, field.TypeInt))
	if ps := faqd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, faqd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	faqd.mutation.done = true
	return affected, err
}

// FrequentAskQuestionDeleteOne is the builder for deleting a single FrequentAskQuestion entity.
type FrequentAskQuestionDeleteOne struct {
	faqd *FrequentAskQuestionDelete
}

// Where appends a list predicates to the FrequentAskQuestionDelete builder.
func (faqdo *FrequentAskQuestionDeleteOne) Where(ps ...predicate.FrequentAskQuestion) *FrequentAskQuestionDeleteOne {
	faqdo.faqd.mutation.Where(ps...)
	return faqdo
}

// Exec executes the deletion query.
func (faqdo *FrequentAskQuestionDeleteOne) Exec(ctx context.Context) error {
	n, err := faqdo.faqd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{frequentaskquestion.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (faqdo *FrequentAskQuestionDeleteOne) ExecX(ctx context.Context) {
	if err := faqdo.Exec(ctx); err != nil {
		panic(err)
	}
}
