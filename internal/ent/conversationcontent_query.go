// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/predicate"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationContentQuery is the builder for querying ConversationContent entities.
type ConversationContentQuery struct {
	config
	ctx              *QueryContext
	order            []conversationcontent.OrderOption
	inters           []Interceptor
	predicates       []predicate.ConversationContent
	withConversation *ConversationQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ConversationContentQuery builder.
func (ccq *ConversationContentQuery) Where(ps ...predicate.ConversationContent) *ConversationContentQuery {
	ccq.predicates = append(ccq.predicates, ps...)
	return ccq
}

// Limit the number of records to be returned by this query.
func (ccq *ConversationContentQuery) Limit(limit int) *ConversationContentQuery {
	ccq.ctx.Limit = &limit
	return ccq
}

// Offset to start from.
func (ccq *ConversationContentQuery) Offset(offset int) *ConversationContentQuery {
	ccq.ctx.Offset = &offset
	return ccq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ccq *ConversationContentQuery) Unique(unique bool) *ConversationContentQuery {
	ccq.ctx.Unique = &unique
	return ccq
}

// Order specifies how the records should be ordered.
func (ccq *ConversationContentQuery) Order(o ...conversationcontent.OrderOption) *ConversationContentQuery {
	ccq.order = append(ccq.order, o...)
	return ccq
}

// QueryConversation chains the current query on the "conversation" edge.
func (ccq *ConversationContentQuery) QueryConversation() *ConversationQuery {
	query := (&ConversationClient{config: ccq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ccq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ccq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(conversationcontent.Table, conversationcontent.FieldID, selector),
			sqlgraph.To(conversation.Table, conversation.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, conversationcontent.ConversationTable, conversationcontent.ConversationColumn),
		)
		fromU = sqlgraph.SetNeighbors(ccq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first ConversationContent entity from the query.
// Returns a *NotFoundError when no ConversationContent was found.
func (ccq *ConversationContentQuery) First(ctx context.Context) (*ConversationContent, error) {
	nodes, err := ccq.Limit(1).All(setContextOp(ctx, ccq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{conversationcontent.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ccq *ConversationContentQuery) FirstX(ctx context.Context) *ConversationContent {
	node, err := ccq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first ConversationContent ID from the query.
// Returns a *NotFoundError when no ConversationContent ID was found.
func (ccq *ConversationContentQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = ccq.Limit(1).IDs(setContextOp(ctx, ccq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{conversationcontent.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ccq *ConversationContentQuery) FirstIDX(ctx context.Context) int {
	id, err := ccq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single ConversationContent entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one ConversationContent entity is found.
// Returns a *NotFoundError when no ConversationContent entities are found.
func (ccq *ConversationContentQuery) Only(ctx context.Context) (*ConversationContent, error) {
	nodes, err := ccq.Limit(2).All(setContextOp(ctx, ccq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{conversationcontent.Label}
	default:
		return nil, &NotSingularError{conversationcontent.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ccq *ConversationContentQuery) OnlyX(ctx context.Context) *ConversationContent {
	node, err := ccq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only ConversationContent ID in the query.
// Returns a *NotSingularError when more than one ConversationContent ID is found.
// Returns a *NotFoundError when no entities are found.
func (ccq *ConversationContentQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = ccq.Limit(2).IDs(setContextOp(ctx, ccq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{conversationcontent.Label}
	default:
		err = &NotSingularError{conversationcontent.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ccq *ConversationContentQuery) OnlyIDX(ctx context.Context) int {
	id, err := ccq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of ConversationContents.
func (ccq *ConversationContentQuery) All(ctx context.Context) ([]*ConversationContent, error) {
	ctx = setContextOp(ctx, ccq.ctx, ent.OpQueryAll)
	if err := ccq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*ConversationContent, *ConversationContentQuery]()
	return withInterceptors[[]*ConversationContent](ctx, ccq, qr, ccq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ccq *ConversationContentQuery) AllX(ctx context.Context) []*ConversationContent {
	nodes, err := ccq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of ConversationContent IDs.
func (ccq *ConversationContentQuery) IDs(ctx context.Context) (ids []int, err error) {
	if ccq.ctx.Unique == nil && ccq.path != nil {
		ccq.Unique(true)
	}
	ctx = setContextOp(ctx, ccq.ctx, ent.OpQueryIDs)
	if err = ccq.Select(conversationcontent.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ccq *ConversationContentQuery) IDsX(ctx context.Context) []int {
	ids, err := ccq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ccq *ConversationContentQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ccq.ctx, ent.OpQueryCount)
	if err := ccq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ccq, querierCount[*ConversationContentQuery](), ccq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ccq *ConversationContentQuery) CountX(ctx context.Context) int {
	count, err := ccq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ccq *ConversationContentQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ccq.ctx, ent.OpQueryExist)
	switch _, err := ccq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ccq *ConversationContentQuery) ExistX(ctx context.Context) bool {
	exist, err := ccq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ConversationContentQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ccq *ConversationContentQuery) Clone() *ConversationContentQuery {
	if ccq == nil {
		return nil
	}
	return &ConversationContentQuery{
		config:           ccq.config,
		ctx:              ccq.ctx.Clone(),
		order:            append([]conversationcontent.OrderOption{}, ccq.order...),
		inters:           append([]Interceptor{}, ccq.inters...),
		predicates:       append([]predicate.ConversationContent{}, ccq.predicates...),
		withConversation: ccq.withConversation.Clone(),
		// clone intermediate query.
		sql:  ccq.sql.Clone(),
		path: ccq.path,
	}
}

// WithConversation tells the query-builder to eager-load the nodes that are connected to
// the "conversation" edge. The optional arguments are used to configure the query builder of the edge.
func (ccq *ConversationContentQuery) WithConversation(opts ...func(*ConversationQuery)) *ConversationContentQuery {
	query := (&ConversationClient{config: ccq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ccq.withConversation = query
	return ccq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.ConversationContent.Query().
//		GroupBy(conversationcontent.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ccq *ConversationContentQuery) GroupBy(field string, fields ...string) *ConversationContentGroupBy {
	ccq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ConversationContentGroupBy{build: ccq}
	grbuild.flds = &ccq.ctx.Fields
	grbuild.label = conversationcontent.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.ConversationContent.Query().
//		Select(conversationcontent.FieldCreatedAt).
//		Scan(ctx, &v)
func (ccq *ConversationContentQuery) Select(fields ...string) *ConversationContentSelect {
	ccq.ctx.Fields = append(ccq.ctx.Fields, fields...)
	sbuild := &ConversationContentSelect{ConversationContentQuery: ccq}
	sbuild.label = conversationcontent.Label
	sbuild.flds, sbuild.scan = &ccq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ConversationContentSelect configured with the given aggregations.
func (ccq *ConversationContentQuery) Aggregate(fns ...AggregateFunc) *ConversationContentSelect {
	return ccq.Select().Aggregate(fns...)
}

func (ccq *ConversationContentQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ccq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ccq); err != nil {
				return err
			}
		}
	}
	for _, f := range ccq.ctx.Fields {
		if !conversationcontent.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ccq.path != nil {
		prev, err := ccq.path(ctx)
		if err != nil {
			return err
		}
		ccq.sql = prev
	}
	return nil
}

func (ccq *ConversationContentQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*ConversationContent, error) {
	var (
		nodes       = []*ConversationContent{}
		_spec       = ccq.querySpec()
		loadedTypes = [1]bool{
			ccq.withConversation != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*ConversationContent).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &ConversationContent{config: ccq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ccq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ccq.withConversation; query != nil {
		if err := ccq.loadConversation(ctx, query, nodes, nil,
			func(n *ConversationContent, e *Conversation) { n.Edges.Conversation = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ccq *ConversationContentQuery) loadConversation(ctx context.Context, query *ConversationQuery, nodes []*ConversationContent, init func(*ConversationContent), assign func(*ConversationContent, *Conversation)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*ConversationContent)
	for i := range nodes {
		fk := nodes[i].ConversationID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(conversation.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "conversation_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (ccq *ConversationContentQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ccq.querySpec()
	_spec.Node.Columns = ccq.ctx.Fields
	if len(ccq.ctx.Fields) > 0 {
		_spec.Unique = ccq.ctx.Unique != nil && *ccq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ccq.driver, _spec)
}

func (ccq *ConversationContentQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(conversationcontent.Table, conversationcontent.Columns, sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt))
	_spec.From = ccq.sql
	if unique := ccq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ccq.path != nil {
		_spec.Unique = true
	}
	if fields := ccq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, conversationcontent.FieldID)
		for i := range fields {
			if fields[i] != conversationcontent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if ccq.withConversation != nil {
			_spec.Node.AddColumnOnce(conversationcontent.FieldConversationID)
		}
	}
	if ps := ccq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ccq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ccq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ccq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ccq *ConversationContentQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ccq.driver.Dialect())
	t1 := builder.Table(conversationcontent.Table)
	columns := ccq.ctx.Fields
	if len(columns) == 0 {
		columns = conversationcontent.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ccq.sql != nil {
		selector = ccq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ccq.ctx.Unique != nil && *ccq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ccq.predicates {
		p(selector)
	}
	for _, p := range ccq.order {
		p(selector)
	}
	if offset := ccq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ccq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ConversationContentGroupBy is the group-by builder for ConversationContent entities.
type ConversationContentGroupBy struct {
	selector
	build *ConversationContentQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ccgb *ConversationContentGroupBy) Aggregate(fns ...AggregateFunc) *ConversationContentGroupBy {
	ccgb.fns = append(ccgb.fns, fns...)
	return ccgb
}

// Scan applies the selector query and scans the result into the given value.
func (ccgb *ConversationContentGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ccgb.build.ctx, ent.OpQueryGroupBy)
	if err := ccgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ConversationContentQuery, *ConversationContentGroupBy](ctx, ccgb.build, ccgb, ccgb.build.inters, v)
}

func (ccgb *ConversationContentGroupBy) sqlScan(ctx context.Context, root *ConversationContentQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ccgb.fns))
	for _, fn := range ccgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ccgb.flds)+len(ccgb.fns))
		for _, f := range *ccgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ccgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ccgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ConversationContentSelect is the builder for selecting fields of ConversationContent entities.
type ConversationContentSelect struct {
	*ConversationContentQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ccs *ConversationContentSelect) Aggregate(fns ...AggregateFunc) *ConversationContentSelect {
	ccs.fns = append(ccs.fns, fns...)
	return ccs
}

// Scan applies the selector query and scans the result into the given value.
func (ccs *ConversationContentSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ccs.ctx, ent.OpQuerySelect)
	if err := ccs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ConversationContentQuery, *ConversationContentSelect](ctx, ccs.ConversationContentQuery, ccs, ccs.inters, v)
}

func (ccs *ConversationContentSelect) sqlScan(ctx context.Context, root *ConversationContentQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ccs.fns))
	for _, fn := range ccs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ccs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ccs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
