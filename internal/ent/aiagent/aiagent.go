// Code generated by ent, DO NOT EDIT.

package aiagent

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the aiagent type in the database.
	Label = "ai_agent"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldIcon holds the string denoting the icon field in the database.
	FieldIcon = "icon"
	// FieldDescription holds the string denoting the description field in the database.
	FieldDescription = "description"
	// FieldTarget holds the string denoting the target field in the database.
	FieldTarget = "target"
	// FieldGuide holds the string denoting the guide field in the database.
	FieldGuide = "guide"
	// FieldSecret holds the string denoting the secret field in the database.
	FieldSecret = "secret"
	// FieldMethod holds the string denoting the method field in the database.
	FieldMethod = "method"
	// FieldInputs holds the string denoting the inputs field in the database.
	FieldInputs = "inputs"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// EdgeConversations holds the string denoting the conversations edge name in mutations.
	EdgeConversations = "conversations"
	// Table holds the table name of the aiagent in the database.
	Table = "ai_agents"
	// ConversationsTable is the table that holds the conversations relation/edge.
	ConversationsTable = "conversations"
	// ConversationsInverseTable is the table name for the Conversation entity.
	// It exists in this package in order to avoid circular dependency with the "conversation" package.
	ConversationsInverseTable = "conversations"
	// ConversationsColumn is the table column denoting the conversations relation/edge.
	ConversationsColumn = "ai_agent_id"
)

// Columns holds all SQL columns for aiagent fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldName,
	FieldIcon,
	FieldDescription,
	FieldTarget,
	FieldGuide,
	FieldSecret,
	FieldMethod,
	FieldInputs,
	FieldStatus,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
)

// OrderOption defines the ordering options for the AiAgent queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByIcon orders the results by the icon field.
func ByIcon(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcon, opts...).ToFunc()
}

// ByDescription orders the results by the description field.
func ByDescription(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDescription, opts...).ToFunc()
}

// ByTarget orders the results by the target field.
func ByTarget(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTarget, opts...).ToFunc()
}

// ByGuide orders the results by the guide field.
func ByGuide(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGuide, opts...).ToFunc()
}

// BySecret orders the results by the secret field.
func BySecret(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecret, opts...).ToFunc()
}

// ByMethod orders the results by the method field.
func ByMethod(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMethod, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByConversationsCount orders the results by conversations count.
func ByConversationsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newConversationsStep(), opts...)
	}
}

// ByConversations orders the results by conversations terms.
func ByConversations(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newConversationsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newConversationsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ConversationsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ConversationsTable, ConversationsColumn),
	)
}
