// Code generated by ent, DO NOT EDIT.

package aiagent

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUpdatedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldName, v))
}

// Icon applies equality check predicate on the "icon" field. It's identical to IconEQ.
func Icon(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIcon, v))
}

// Description applies equality check predicate on the "description" field. It's identical to DescriptionEQ.
func Description(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDescription, v))
}

// Target applies equality check predicate on the "target" field. It's identical to TargetEQ.
func Target(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldTarget, v))
}

// Guide applies equality check predicate on the "guide" field. It's identical to GuideEQ.
func Guide(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldGuide, v))
}

// Secret applies equality check predicate on the "secret" field. It's identical to SecretEQ.
func Secret(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSecret, v))
}

// Method applies equality check predicate on the "method" field. It's identical to MethodEQ.
func Method(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldMethod, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldStatus, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldUpdatedAt, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldName, v))
}

// IconEQ applies the EQ predicate on the "icon" field.
func IconEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldIcon, v))
}

// IconNEQ applies the NEQ predicate on the "icon" field.
func IconNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldIcon, v))
}

// IconIn applies the In predicate on the "icon" field.
func IconIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldIcon, vs...))
}

// IconNotIn applies the NotIn predicate on the "icon" field.
func IconNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldIcon, vs...))
}

// IconGT applies the GT predicate on the "icon" field.
func IconGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldIcon, v))
}

// IconGTE applies the GTE predicate on the "icon" field.
func IconGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldIcon, v))
}

// IconLT applies the LT predicate on the "icon" field.
func IconLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldIcon, v))
}

// IconLTE applies the LTE predicate on the "icon" field.
func IconLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldIcon, v))
}

// IconContains applies the Contains predicate on the "icon" field.
func IconContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldIcon, v))
}

// IconHasPrefix applies the HasPrefix predicate on the "icon" field.
func IconHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldIcon, v))
}

// IconHasSuffix applies the HasSuffix predicate on the "icon" field.
func IconHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldIcon, v))
}

// IconIsNil applies the IsNil predicate on the "icon" field.
func IconIsNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIsNull(FieldIcon))
}

// IconNotNil applies the NotNil predicate on the "icon" field.
func IconNotNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotNull(FieldIcon))
}

// IconEqualFold applies the EqualFold predicate on the "icon" field.
func IconEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldIcon, v))
}

// IconContainsFold applies the ContainsFold predicate on the "icon" field.
func IconContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldIcon, v))
}

// DescriptionEQ applies the EQ predicate on the "description" field.
func DescriptionEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldDescription, v))
}

// DescriptionNEQ applies the NEQ predicate on the "description" field.
func DescriptionNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldDescription, v))
}

// DescriptionIn applies the In predicate on the "description" field.
func DescriptionIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldDescription, vs...))
}

// DescriptionNotIn applies the NotIn predicate on the "description" field.
func DescriptionNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldDescription, vs...))
}

// DescriptionGT applies the GT predicate on the "description" field.
func DescriptionGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldDescription, v))
}

// DescriptionGTE applies the GTE predicate on the "description" field.
func DescriptionGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldDescription, v))
}

// DescriptionLT applies the LT predicate on the "description" field.
func DescriptionLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldDescription, v))
}

// DescriptionLTE applies the LTE predicate on the "description" field.
func DescriptionLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldDescription, v))
}

// DescriptionContains applies the Contains predicate on the "description" field.
func DescriptionContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldDescription, v))
}

// DescriptionHasPrefix applies the HasPrefix predicate on the "description" field.
func DescriptionHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldDescription, v))
}

// DescriptionHasSuffix applies the HasSuffix predicate on the "description" field.
func DescriptionHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldDescription, v))
}

// DescriptionIsNil applies the IsNil predicate on the "description" field.
func DescriptionIsNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIsNull(FieldDescription))
}

// DescriptionNotNil applies the NotNil predicate on the "description" field.
func DescriptionNotNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotNull(FieldDescription))
}

// DescriptionEqualFold applies the EqualFold predicate on the "description" field.
func DescriptionEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldDescription, v))
}

// DescriptionContainsFold applies the ContainsFold predicate on the "description" field.
func DescriptionContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldDescription, v))
}

// TargetEQ applies the EQ predicate on the "target" field.
func TargetEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldTarget, v))
}

// TargetNEQ applies the NEQ predicate on the "target" field.
func TargetNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldTarget, v))
}

// TargetIn applies the In predicate on the "target" field.
func TargetIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldTarget, vs...))
}

// TargetNotIn applies the NotIn predicate on the "target" field.
func TargetNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldTarget, vs...))
}

// TargetGT applies the GT predicate on the "target" field.
func TargetGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldTarget, v))
}

// TargetGTE applies the GTE predicate on the "target" field.
func TargetGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldTarget, v))
}

// TargetLT applies the LT predicate on the "target" field.
func TargetLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldTarget, v))
}

// TargetLTE applies the LTE predicate on the "target" field.
func TargetLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldTarget, v))
}

// TargetContains applies the Contains predicate on the "target" field.
func TargetContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldTarget, v))
}

// TargetHasPrefix applies the HasPrefix predicate on the "target" field.
func TargetHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldTarget, v))
}

// TargetHasSuffix applies the HasSuffix predicate on the "target" field.
func TargetHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldTarget, v))
}

// TargetEqualFold applies the EqualFold predicate on the "target" field.
func TargetEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldTarget, v))
}

// TargetContainsFold applies the ContainsFold predicate on the "target" field.
func TargetContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldTarget, v))
}

// GuideEQ applies the EQ predicate on the "guide" field.
func GuideEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldGuide, v))
}

// GuideNEQ applies the NEQ predicate on the "guide" field.
func GuideNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldGuide, v))
}

// GuideIn applies the In predicate on the "guide" field.
func GuideIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldGuide, vs...))
}

// GuideNotIn applies the NotIn predicate on the "guide" field.
func GuideNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldGuide, vs...))
}

// GuideGT applies the GT predicate on the "guide" field.
func GuideGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldGuide, v))
}

// GuideGTE applies the GTE predicate on the "guide" field.
func GuideGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldGuide, v))
}

// GuideLT applies the LT predicate on the "guide" field.
func GuideLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldGuide, v))
}

// GuideLTE applies the LTE predicate on the "guide" field.
func GuideLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldGuide, v))
}

// GuideContains applies the Contains predicate on the "guide" field.
func GuideContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldGuide, v))
}

// GuideHasPrefix applies the HasPrefix predicate on the "guide" field.
func GuideHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldGuide, v))
}

// GuideHasSuffix applies the HasSuffix predicate on the "guide" field.
func GuideHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldGuide, v))
}

// GuideIsNil applies the IsNil predicate on the "guide" field.
func GuideIsNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIsNull(FieldGuide))
}

// GuideNotNil applies the NotNil predicate on the "guide" field.
func GuideNotNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotNull(FieldGuide))
}

// GuideEqualFold applies the EqualFold predicate on the "guide" field.
func GuideEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldGuide, v))
}

// GuideContainsFold applies the ContainsFold predicate on the "guide" field.
func GuideContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldGuide, v))
}

// SecretEQ applies the EQ predicate on the "secret" field.
func SecretEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldSecret, v))
}

// SecretNEQ applies the NEQ predicate on the "secret" field.
func SecretNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldSecret, v))
}

// SecretIn applies the In predicate on the "secret" field.
func SecretIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldSecret, vs...))
}

// SecretNotIn applies the NotIn predicate on the "secret" field.
func SecretNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldSecret, vs...))
}

// SecretGT applies the GT predicate on the "secret" field.
func SecretGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldSecret, v))
}

// SecretGTE applies the GTE predicate on the "secret" field.
func SecretGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldSecret, v))
}

// SecretLT applies the LT predicate on the "secret" field.
func SecretLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldSecret, v))
}

// SecretLTE applies the LTE predicate on the "secret" field.
func SecretLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldSecret, v))
}

// SecretContains applies the Contains predicate on the "secret" field.
func SecretContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldSecret, v))
}

// SecretHasPrefix applies the HasPrefix predicate on the "secret" field.
func SecretHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldSecret, v))
}

// SecretHasSuffix applies the HasSuffix predicate on the "secret" field.
func SecretHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldSecret, v))
}

// SecretEqualFold applies the EqualFold predicate on the "secret" field.
func SecretEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldSecret, v))
}

// SecretContainsFold applies the ContainsFold predicate on the "secret" field.
func SecretContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldSecret, v))
}

// MethodEQ applies the EQ predicate on the "method" field.
func MethodEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldMethod, v))
}

// MethodNEQ applies the NEQ predicate on the "method" field.
func MethodNEQ(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldMethod, v))
}

// MethodIn applies the In predicate on the "method" field.
func MethodIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldMethod, vs...))
}

// MethodNotIn applies the NotIn predicate on the "method" field.
func MethodNotIn(vs ...string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldMethod, vs...))
}

// MethodGT applies the GT predicate on the "method" field.
func MethodGT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldMethod, v))
}

// MethodGTE applies the GTE predicate on the "method" field.
func MethodGTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldMethod, v))
}

// MethodLT applies the LT predicate on the "method" field.
func MethodLT(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldMethod, v))
}

// MethodLTE applies the LTE predicate on the "method" field.
func MethodLTE(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldMethod, v))
}

// MethodContains applies the Contains predicate on the "method" field.
func MethodContains(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContains(FieldMethod, v))
}

// MethodHasPrefix applies the HasPrefix predicate on the "method" field.
func MethodHasPrefix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasPrefix(FieldMethod, v))
}

// MethodHasSuffix applies the HasSuffix predicate on the "method" field.
func MethodHasSuffix(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldHasSuffix(FieldMethod, v))
}

// MethodEqualFold applies the EqualFold predicate on the "method" field.
func MethodEqualFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEqualFold(FieldMethod, v))
}

// MethodContainsFold applies the ContainsFold predicate on the "method" field.
func MethodContainsFold(v string) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldContainsFold(FieldMethod, v))
}

// InputsIsNil applies the IsNil predicate on the "inputs" field.
func InputsIsNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIsNull(FieldInputs))
}

// InputsNotNil applies the NotNil predicate on the "inputs" field.
func InputsNotNil() predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotNull(FieldInputs))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.AiAgent {
	return predicate.AiAgent(sql.FieldLTE(FieldStatus, v))
}

// HasConversations applies the HasEdge predicate on the "conversations" edge.
func HasConversations() predicate.AiAgent {
	return predicate.AiAgent(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ConversationsTable, ConversationsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasConversationsWith applies the HasEdge predicate on the "conversations" edge with a given conditions (other predicates).
func HasConversationsWith(preds ...predicate.Conversation) predicate.AiAgent {
	return predicate.AiAgent(func(s *sql.Selector) {
		step := newConversationsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.AiAgent) predicate.AiAgent {
	return predicate.AiAgent(sql.NotPredicates(p))
}
