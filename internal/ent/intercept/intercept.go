// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"

	"bole-ai/internal/ent"
	"bole-ai/internal/ent/adminuser"
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/screenplay"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"

	"entgo.io/ent/dialect/sql"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next ent.Querier) ent.Querier {
	return ent.QuerierFunc(func(ctx context.Context, q ent.Query) (ent.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q ent.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The AdminUserFunc type is an adapter to allow the use of ordinary function as a Querier.
type AdminUserFunc func(context.Context, *ent.AdminUserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AdminUserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AdminUserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AdminUserQuery", q)
}

// The TraverseAdminUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAdminUser func(context.Context, *ent.AdminUserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAdminUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAdminUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AdminUserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AdminUserQuery", q)
}

// The AiAgentFunc type is an adapter to allow the use of ordinary function as a Querier.
type AiAgentFunc func(context.Context, *ent.AiAgentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f AiAgentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.AiAgentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.AiAgentQuery", q)
}

// The TraverseAiAgent type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAiAgent func(context.Context, *ent.AiAgentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAiAgent) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAiAgent) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.AiAgentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.AiAgentQuery", q)
}

// The BatchFunc type is an adapter to allow the use of ordinary function as a Querier.
type BatchFunc func(context.Context, *ent.BatchQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f BatchFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.BatchQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.BatchQuery", q)
}

// The TraverseBatch type is an adapter to allow the use of ordinary function as Traverser.
type TraverseBatch func(context.Context, *ent.BatchQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseBatch) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseBatch) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.BatchQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.BatchQuery", q)
}

// The ConversationFunc type is an adapter to allow the use of ordinary function as a Querier.
type ConversationFunc func(context.Context, *ent.ConversationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ConversationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ConversationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ConversationQuery", q)
}

// The TraverseConversation type is an adapter to allow the use of ordinary function as Traverser.
type TraverseConversation func(context.Context, *ent.ConversationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseConversation) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseConversation) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ConversationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ConversationQuery", q)
}

// The ConversationContentFunc type is an adapter to allow the use of ordinary function as a Querier.
type ConversationContentFunc func(context.Context, *ent.ConversationContentQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ConversationContentFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ConversationContentQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ConversationContentQuery", q)
}

// The TraverseConversationContent type is an adapter to allow the use of ordinary function as Traverser.
type TraverseConversationContent func(context.Context, *ent.ConversationContentQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseConversationContent) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseConversationContent) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ConversationContentQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ConversationContentQuery", q)
}

// The FrequentAskQuestionFunc type is an adapter to allow the use of ordinary function as a Querier.
type FrequentAskQuestionFunc func(context.Context, *ent.FrequentAskQuestionQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f FrequentAskQuestionFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.FrequentAskQuestionQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.FrequentAskQuestionQuery", q)
}

// The TraverseFrequentAskQuestion type is an adapter to allow the use of ordinary function as Traverser.
type TraverseFrequentAskQuestion func(context.Context, *ent.FrequentAskQuestionQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFrequentAskQuestion) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFrequentAskQuestion) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FrequentAskQuestionQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.FrequentAskQuestionQuery", q)
}

// The InternalMessageFunc type is an adapter to allow the use of ordinary function as a Querier.
type InternalMessageFunc func(context.Context, *ent.InternalMessageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f InternalMessageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.InternalMessageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.InternalMessageQuery", q)
}

// The TraverseInternalMessage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseInternalMessage func(context.Context, *ent.InternalMessageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseInternalMessage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseInternalMessage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.InternalMessageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.InternalMessageQuery", q)
}

// The ProjectFunc type is an adapter to allow the use of ordinary function as a Querier.
type ProjectFunc func(context.Context, *ent.ProjectQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ProjectFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ProjectQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ProjectQuery", q)
}

// The TraverseProject type is an adapter to allow the use of ordinary function as Traverser.
type TraverseProject func(context.Context, *ent.ProjectQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseProject) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseProject) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ProjectQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ProjectQuery", q)
}

// The ScreenplayFunc type is an adapter to allow the use of ordinary function as a Querier.
type ScreenplayFunc func(context.Context, *ent.ScreenplayQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ScreenplayFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ScreenplayQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ScreenplayQuery", q)
}

// The TraverseScreenplay type is an adapter to allow the use of ordinary function as Traverser.
type TraverseScreenplay func(context.Context, *ent.ScreenplayQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseScreenplay) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseScreenplay) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ScreenplayQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ScreenplayQuery", q)
}

// The TestCodeFunc type is an adapter to allow the use of ordinary function as a Querier.
type TestCodeFunc func(context.Context, *ent.TestCodeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TestCodeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TestCodeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TestCodeQuery", q)
}

// The TraverseTestCode type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTestCode func(context.Context, *ent.TestCodeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTestCode) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTestCode) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TestCodeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TestCodeQuery", q)
}

// The UserFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserFunc func(context.Context, *ent.UserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// The TraverseUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUser func(context.Context, *ent.UserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// The UserInternalMessageFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserInternalMessageFunc func(context.Context, *ent.UserInternalMessageQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserInternalMessageFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserInternalMessageQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserInternalMessageQuery", q)
}

// The TraverseUserInternalMessage type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUserInternalMessage func(context.Context, *ent.UserInternalMessageQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUserInternalMessage) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUserInternalMessage) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserInternalMessageQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserInternalMessageQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q ent.Query) (Query, error) {
	switch q := q.(type) {
	case *ent.AdminUserQuery:
		return &query[*ent.AdminUserQuery, predicate.AdminUser, adminuser.OrderOption]{typ: ent.TypeAdminUser, tq: q}, nil
	case *ent.AiAgentQuery:
		return &query[*ent.AiAgentQuery, predicate.AiAgent, aiagent.OrderOption]{typ: ent.TypeAiAgent, tq: q}, nil
	case *ent.BatchQuery:
		return &query[*ent.BatchQuery, predicate.Batch, batch.OrderOption]{typ: ent.TypeBatch, tq: q}, nil
	case *ent.ConversationQuery:
		return &query[*ent.ConversationQuery, predicate.Conversation, conversation.OrderOption]{typ: ent.TypeConversation, tq: q}, nil
	case *ent.ConversationContentQuery:
		return &query[*ent.ConversationContentQuery, predicate.ConversationContent, conversationcontent.OrderOption]{typ: ent.TypeConversationContent, tq: q}, nil
	case *ent.FrequentAskQuestionQuery:
		return &query[*ent.FrequentAskQuestionQuery, predicate.FrequentAskQuestion, frequentaskquestion.OrderOption]{typ: ent.TypeFrequentAskQuestion, tq: q}, nil
	case *ent.InternalMessageQuery:
		return &query[*ent.InternalMessageQuery, predicate.InternalMessage, internalmessage.OrderOption]{typ: ent.TypeInternalMessage, tq: q}, nil
	case *ent.ProjectQuery:
		return &query[*ent.ProjectQuery, predicate.Project, project.OrderOption]{typ: ent.TypeProject, tq: q}, nil
	case *ent.ScreenplayQuery:
		return &query[*ent.ScreenplayQuery, predicate.Screenplay, screenplay.OrderOption]{typ: ent.TypeScreenplay, tq: q}, nil
	case *ent.TestCodeQuery:
		return &query[*ent.TestCodeQuery, predicate.TestCode, testcode.OrderOption]{typ: ent.TypeTestCode, tq: q}, nil
	case *ent.UserQuery:
		return &query[*ent.UserQuery, predicate.User, user.OrderOption]{typ: ent.TypeUser, tq: q}, nil
	case *ent.UserInternalMessageQuery:
		return &query[*ent.UserInternalMessageQuery, predicate.UserInternalMessage, userinternalmessage.OrderOption]{typ: ent.TypeUserInternalMessage, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}
