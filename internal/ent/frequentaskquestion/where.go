// Code generated by ent, DO NOT EDIT.

package frequentaskquestion

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldUpdatedAt, v))
}

// Content applies equality check predicate on the "content" field. It's identical to ContentEQ.
func Content(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldContent, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLTE(FieldUpdatedAt, v))
}

// ContentEQ applies the EQ predicate on the "content" field.
func ContentEQ(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEQ(FieldContent, v))
}

// ContentNEQ applies the NEQ predicate on the "content" field.
func ContentNEQ(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNEQ(FieldContent, v))
}

// ContentIn applies the In predicate on the "content" field.
func ContentIn(vs ...string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldIn(FieldContent, vs...))
}

// ContentNotIn applies the NotIn predicate on the "content" field.
func ContentNotIn(vs ...string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldNotIn(FieldContent, vs...))
}

// ContentGT applies the GT predicate on the "content" field.
func ContentGT(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGT(FieldContent, v))
}

// ContentGTE applies the GTE predicate on the "content" field.
func ContentGTE(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldGTE(FieldContent, v))
}

// ContentLT applies the LT predicate on the "content" field.
func ContentLT(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLT(FieldContent, v))
}

// ContentLTE applies the LTE predicate on the "content" field.
func ContentLTE(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldLTE(FieldContent, v))
}

// ContentContains applies the Contains predicate on the "content" field.
func ContentContains(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldContains(FieldContent, v))
}

// ContentHasPrefix applies the HasPrefix predicate on the "content" field.
func ContentHasPrefix(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldHasPrefix(FieldContent, v))
}

// ContentHasSuffix applies the HasSuffix predicate on the "content" field.
func ContentHasSuffix(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldHasSuffix(FieldContent, v))
}

// ContentEqualFold applies the EqualFold predicate on the "content" field.
func ContentEqualFold(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldEqualFold(FieldContent, v))
}

// ContentContainsFold applies the ContainsFold predicate on the "content" field.
func ContentContainsFold(v string) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.FieldContainsFold(FieldContent, v))
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.FrequentAskQuestion) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.FrequentAskQuestion) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.FrequentAskQuestion) predicate.FrequentAskQuestion {
	return predicate.FrequentAskQuestion(sql.NotPredicates(p))
}
