// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/adminuser"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 管理后台用户表
type AdminUser struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 账号
	Username string `json:"username,omitempty"`
	// 密码
	Password     string `json:"-"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AdminUser) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case adminuser.FieldID:
			values[i] = new(sql.NullInt64)
		case adminuser.FieldUsername, adminuser.FieldPassword:
			values[i] = new(sql.NullString)
		case adminuser.FieldCreatedAt, adminuser.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AdminUser fields.
func (au *AdminUser) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case adminuser.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			au.ID = int(value.Int64)
		case adminuser.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				au.CreatedAt = value.Time
			}
		case adminuser.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				au.UpdatedAt = value.Time
			}
		case adminuser.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				au.Username = value.String
			}
		case adminuser.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				au.Password = value.String
			}
		default:
			au.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AdminUser.
// This includes values selected through modifiers, order, etc.
func (au *AdminUser) Value(name string) (ent.Value, error) {
	return au.selectValues.Get(name)
}

// Update returns a builder for updating this AdminUser.
// Note that you need to call AdminUser.Unwrap() before calling this method if this AdminUser
// was returned from a transaction, and the transaction was committed or rolled back.
func (au *AdminUser) Update() *AdminUserUpdateOne {
	return NewAdminUserClient(au.config).UpdateOne(au)
}

// Unwrap unwraps the AdminUser entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (au *AdminUser) Unwrap() *AdminUser {
	_tx, ok := au.config.driver.(*txDriver)
	if !ok {
		panic("ent: AdminUser is not a transactional entity")
	}
	au.config.driver = _tx.drv
	return au
}

// String implements the fmt.Stringer.
func (au *AdminUser) String() string {
	var builder strings.Builder
	builder.WriteString("AdminUser(")
	builder.WriteString(fmt.Sprintf("id=%v, ", au.ID))
	builder.WriteString("created_at=")
	builder.WriteString(au.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(au.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(au.Username)
	builder.WriteString(", ")
	builder.WriteString("password=<sensitive>")
	builder.WriteByte(')')
	return builder.String()
}

// AdminUsers is a parsable slice of AdminUser.
type AdminUsers []*AdminUser
