// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrequentAskQuestionUpdate is the builder for updating FrequentAskQuestion entities.
type FrequentAskQuestionUpdate struct {
	config
	hooks    []Hook
	mutation *FrequentAskQuestionMutation
}

// Where appends a list predicates to the FrequentAskQuestionUpdate builder.
func (faqu *FrequentAskQuestionUpdate) Where(ps ...predicate.FrequentAskQuestion) *FrequentAskQuestionUpdate {
	faqu.mutation.Where(ps...)
	return faqu
}

// SetUpdatedAt sets the "updated_at" field.
func (faqu *FrequentAskQuestionUpdate) SetUpdatedAt(t time.Time) *FrequentAskQuestionUpdate {
	faqu.mutation.SetUpdatedAt(t)
	return faqu
}

// SetContent sets the "content" field.
func (faqu *FrequentAskQuestionUpdate) SetContent(s string) *FrequentAskQuestionUpdate {
	faqu.mutation.SetContent(s)
	return faqu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (faqu *FrequentAskQuestionUpdate) SetNillableContent(s *string) *FrequentAskQuestionUpdate {
	if s != nil {
		faqu.SetContent(*s)
	}
	return faqu
}

// Mutation returns the FrequentAskQuestionMutation object of the builder.
func (faqu *FrequentAskQuestionUpdate) Mutation() *FrequentAskQuestionMutation {
	return faqu.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (faqu *FrequentAskQuestionUpdate) Save(ctx context.Context) (int, error) {
	faqu.defaults()
	return withHooks(ctx, faqu.sqlSave, faqu.mutation, faqu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (faqu *FrequentAskQuestionUpdate) SaveX(ctx context.Context) int {
	affected, err := faqu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (faqu *FrequentAskQuestionUpdate) Exec(ctx context.Context) error {
	_, err := faqu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (faqu *FrequentAskQuestionUpdate) ExecX(ctx context.Context) {
	if err := faqu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (faqu *FrequentAskQuestionUpdate) defaults() {
	if _, ok := faqu.mutation.UpdatedAt(); !ok {
		v := frequentaskquestion.UpdateDefaultUpdatedAt()
		faqu.mutation.SetUpdatedAt(v)
	}
}

func (faqu *FrequentAskQuestionUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(frequentaskquestion.Table, frequentaskquestion.Columns, sqlgraph.NewFieldSpec(frequentaskquestion.FieldID, field.TypeInt))
	if ps := faqu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := faqu.mutation.UpdatedAt(); ok {
		_spec.SetField(frequentaskquestion.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := faqu.mutation.Content(); ok {
		_spec.SetField(frequentaskquestion.FieldContent, field.TypeString, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, faqu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{frequentaskquestion.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	faqu.mutation.done = true
	return n, nil
}

// FrequentAskQuestionUpdateOne is the builder for updating a single FrequentAskQuestion entity.
type FrequentAskQuestionUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FrequentAskQuestionMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (faquo *FrequentAskQuestionUpdateOne) SetUpdatedAt(t time.Time) *FrequentAskQuestionUpdateOne {
	faquo.mutation.SetUpdatedAt(t)
	return faquo
}

// SetContent sets the "content" field.
func (faquo *FrequentAskQuestionUpdateOne) SetContent(s string) *FrequentAskQuestionUpdateOne {
	faquo.mutation.SetContent(s)
	return faquo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (faquo *FrequentAskQuestionUpdateOne) SetNillableContent(s *string) *FrequentAskQuestionUpdateOne {
	if s != nil {
		faquo.SetContent(*s)
	}
	return faquo
}

// Mutation returns the FrequentAskQuestionMutation object of the builder.
func (faquo *FrequentAskQuestionUpdateOne) Mutation() *FrequentAskQuestionMutation {
	return faquo.mutation
}

// Where appends a list predicates to the FrequentAskQuestionUpdate builder.
func (faquo *FrequentAskQuestionUpdateOne) Where(ps ...predicate.FrequentAskQuestion) *FrequentAskQuestionUpdateOne {
	faquo.mutation.Where(ps...)
	return faquo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (faquo *FrequentAskQuestionUpdateOne) Select(field string, fields ...string) *FrequentAskQuestionUpdateOne {
	faquo.fields = append([]string{field}, fields...)
	return faquo
}

// Save executes the query and returns the updated FrequentAskQuestion entity.
func (faquo *FrequentAskQuestionUpdateOne) Save(ctx context.Context) (*FrequentAskQuestion, error) {
	faquo.defaults()
	return withHooks(ctx, faquo.sqlSave, faquo.mutation, faquo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (faquo *FrequentAskQuestionUpdateOne) SaveX(ctx context.Context) *FrequentAskQuestion {
	node, err := faquo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (faquo *FrequentAskQuestionUpdateOne) Exec(ctx context.Context) error {
	_, err := faquo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (faquo *FrequentAskQuestionUpdateOne) ExecX(ctx context.Context) {
	if err := faquo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (faquo *FrequentAskQuestionUpdateOne) defaults() {
	if _, ok := faquo.mutation.UpdatedAt(); !ok {
		v := frequentaskquestion.UpdateDefaultUpdatedAt()
		faquo.mutation.SetUpdatedAt(v)
	}
}

func (faquo *FrequentAskQuestionUpdateOne) sqlSave(ctx context.Context) (_node *FrequentAskQuestion, err error) {
	_spec := sqlgraph.NewUpdateSpec(frequentaskquestion.Table, frequentaskquestion.Columns, sqlgraph.NewFieldSpec(frequentaskquestion.FieldID, field.TypeInt))
	id, ok := faquo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "FrequentAskQuestion.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := faquo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, frequentaskquestion.FieldID)
		for _, f := range fields {
			if !frequentaskquestion.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != frequentaskquestion.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := faquo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := faquo.mutation.UpdatedAt(); ok {
		_spec.SetField(frequentaskquestion.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := faquo.mutation.Content(); ok {
		_spec.SetField(frequentaskquestion.FieldContent, field.TypeString, value)
	}
	_node = &FrequentAskQuestion{config: faquo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, faquo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{frequentaskquestion.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	faquo.mutation.done = true
	return _node, nil
}
