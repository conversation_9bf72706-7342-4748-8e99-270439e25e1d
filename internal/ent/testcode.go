// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 测试码表
type TestCode struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 批次id
	BatchID int `json:"batch_id,omitempty"`
	// 测试码
	Code string `json:"code,omitempty"`
	// 设备id
	Device string `json:"device,omitempty"`
	// 状态:1默认；2已领取 3 已使用
	Status int8 `json:"status,omitempty"`
	// 过期时间
	ExpiredAt time.Time `json:"expired_at,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the TestCodeQuery when eager-loading is set.
	Edges        TestCodeEdges `json:"edges"`
	selectValues sql.SelectValues
}

// TestCodeEdges holds the relations/edges for other nodes in the graph.
type TestCodeEdges struct {
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// Batch holds the value of the batch edge.
	Batch *Batch `json:"batch,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TestCodeEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// BatchOrErr returns the Batch value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TestCodeEdges) BatchOrErr() (*Batch, error) {
	if e.Batch != nil {
		return e.Batch, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: batch.Label}
	}
	return nil, &NotLoadedError{edge: "batch"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*TestCode) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case testcode.FieldID, testcode.FieldBatchID, testcode.FieldStatus:
			values[i] = new(sql.NullInt64)
		case testcode.FieldCode, testcode.FieldDevice:
			values[i] = new(sql.NullString)
		case testcode.FieldCreatedAt, testcode.FieldUpdatedAt, testcode.FieldExpiredAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the TestCode fields.
func (tc *TestCode) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case testcode.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			tc.ID = int(value.Int64)
		case testcode.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				tc.CreatedAt = value.Time
			}
		case testcode.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				tc.UpdatedAt = value.Time
			}
		case testcode.FieldBatchID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field batch_id", values[i])
			} else if value.Valid {
				tc.BatchID = int(value.Int64)
			}
		case testcode.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				tc.Code = value.String
			}
		case testcode.FieldDevice:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device", values[i])
			} else if value.Valid {
				tc.Device = value.String
			}
		case testcode.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				tc.Status = int8(value.Int64)
			}
		case testcode.FieldExpiredAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expired_at", values[i])
			} else if value.Valid {
				tc.ExpiredAt = value.Time
			}
		default:
			tc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the TestCode.
// This includes values selected through modifiers, order, etc.
func (tc *TestCode) Value(name string) (ent.Value, error) {
	return tc.selectValues.Get(name)
}

// QueryUser queries the "user" edge of the TestCode entity.
func (tc *TestCode) QueryUser() *UserQuery {
	return NewTestCodeClient(tc.config).QueryUser(tc)
}

// QueryBatch queries the "batch" edge of the TestCode entity.
func (tc *TestCode) QueryBatch() *BatchQuery {
	return NewTestCodeClient(tc.config).QueryBatch(tc)
}

// Update returns a builder for updating this TestCode.
// Note that you need to call TestCode.Unwrap() before calling this method if this TestCode
// was returned from a transaction, and the transaction was committed or rolled back.
func (tc *TestCode) Update() *TestCodeUpdateOne {
	return NewTestCodeClient(tc.config).UpdateOne(tc)
}

// Unwrap unwraps the TestCode entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (tc *TestCode) Unwrap() *TestCode {
	_tx, ok := tc.config.driver.(*txDriver)
	if !ok {
		panic("ent: TestCode is not a transactional entity")
	}
	tc.config.driver = _tx.drv
	return tc
}

// String implements the fmt.Stringer.
func (tc *TestCode) String() string {
	var builder strings.Builder
	builder.WriteString("TestCode(")
	builder.WriteString(fmt.Sprintf("id=%v, ", tc.ID))
	builder.WriteString("created_at=")
	builder.WriteString(tc.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(tc.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("batch_id=")
	builder.WriteString(fmt.Sprintf("%v", tc.BatchID))
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(tc.Code)
	builder.WriteString(", ")
	builder.WriteString("device=")
	builder.WriteString(tc.Device)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", tc.Status))
	builder.WriteString(", ")
	builder.WriteString("expired_at=")
	builder.WriteString(tc.ExpiredAt.Format(time.ANSIC))
	builder.WriteByte(')')
	return builder.String()
}

// TestCodes is a parsable slice of TestCode.
type TestCodes []*TestCode
