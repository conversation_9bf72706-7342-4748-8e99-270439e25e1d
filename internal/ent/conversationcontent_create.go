// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationContentCreate is the builder for creating a ConversationContent entity.
type ConversationContentCreate struct {
	config
	mutation *ConversationContentMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (ccc *ConversationContentCreate) SetCreatedAt(t time.Time) *ConversationContentCreate {
	ccc.mutation.SetCreatedAt(t)
	return ccc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ccc *ConversationContentCreate) SetNillableCreatedAt(t *time.Time) *ConversationContentCreate {
	if t != nil {
		ccc.SetCreatedAt(*t)
	}
	return ccc
}

// SetUpdatedAt sets the "updated_at" field.
func (ccc *ConversationContentCreate) SetUpdatedAt(t time.Time) *ConversationContentCreate {
	ccc.mutation.SetUpdatedAt(t)
	return ccc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ccc *ConversationContentCreate) SetNillableUpdatedAt(t *time.Time) *ConversationContentCreate {
	if t != nil {
		ccc.SetUpdatedAt(*t)
	}
	return ccc
}

// SetConversationID sets the "conversation_id" field.
func (ccc *ConversationContentCreate) SetConversationID(i int) *ConversationContentCreate {
	ccc.mutation.SetConversationID(i)
	return ccc
}

// SetContent sets the "content" field.
func (ccc *ConversationContentCreate) SetContent(s string) *ConversationContentCreate {
	ccc.mutation.SetContent(s)
	return ccc
}

// SetType sets the "type" field.
func (ccc *ConversationContentCreate) SetType(i int8) *ConversationContentCreate {
	ccc.mutation.SetType(i)
	return ccc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ccc *ConversationContentCreate) SetNillableType(i *int8) *ConversationContentCreate {
	if i != nil {
		ccc.SetType(*i)
	}
	return ccc
}

// SetConversation sets the "conversation" edge to the Conversation entity.
func (ccc *ConversationContentCreate) SetConversation(c *Conversation) *ConversationContentCreate {
	return ccc.SetConversationID(c.ID)
}

// Mutation returns the ConversationContentMutation object of the builder.
func (ccc *ConversationContentCreate) Mutation() *ConversationContentMutation {
	return ccc.mutation
}

// Save creates the ConversationContent in the database.
func (ccc *ConversationContentCreate) Save(ctx context.Context) (*ConversationContent, error) {
	ccc.defaults()
	return withHooks(ctx, ccc.sqlSave, ccc.mutation, ccc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ccc *ConversationContentCreate) SaveX(ctx context.Context) *ConversationContent {
	v, err := ccc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ccc *ConversationContentCreate) Exec(ctx context.Context) error {
	_, err := ccc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ccc *ConversationContentCreate) ExecX(ctx context.Context) {
	if err := ccc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ccc *ConversationContentCreate) defaults() {
	if _, ok := ccc.mutation.CreatedAt(); !ok {
		v := conversationcontent.DefaultCreatedAt()
		ccc.mutation.SetCreatedAt(v)
	}
	if _, ok := ccc.mutation.UpdatedAt(); !ok {
		v := conversationcontent.DefaultUpdatedAt()
		ccc.mutation.SetUpdatedAt(v)
	}
	if _, ok := ccc.mutation.GetType(); !ok {
		v := conversationcontent.DefaultType
		ccc.mutation.SetType(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ccc *ConversationContentCreate) check() error {
	if _, ok := ccc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "ConversationContent.created_at"`)}
	}
	if _, ok := ccc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "ConversationContent.updated_at"`)}
	}
	if _, ok := ccc.mutation.ConversationID(); !ok {
		return &ValidationError{Name: "conversation_id", err: errors.New(`ent: missing required field "ConversationContent.conversation_id"`)}
	}
	if _, ok := ccc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "ConversationContent.content"`)}
	}
	if _, ok := ccc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "ConversationContent.type"`)}
	}
	if len(ccc.mutation.ConversationIDs()) == 0 {
		return &ValidationError{Name: "conversation", err: errors.New(`ent: missing required edge "ConversationContent.conversation"`)}
	}
	return nil
}

func (ccc *ConversationContentCreate) sqlSave(ctx context.Context) (*ConversationContent, error) {
	if err := ccc.check(); err != nil {
		return nil, err
	}
	_node, _spec := ccc.createSpec()
	if err := sqlgraph.CreateNode(ctx, ccc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	ccc.mutation.id = &_node.ID
	ccc.mutation.done = true
	return _node, nil
}

func (ccc *ConversationContentCreate) createSpec() (*ConversationContent, *sqlgraph.CreateSpec) {
	var (
		_node = &ConversationContent{config: ccc.config}
		_spec = sqlgraph.NewCreateSpec(conversationcontent.Table, sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt))
	)
	if value, ok := ccc.mutation.CreatedAt(); ok {
		_spec.SetField(conversationcontent.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ccc.mutation.UpdatedAt(); ok {
		_spec.SetField(conversationcontent.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ccc.mutation.Content(); ok {
		_spec.SetField(conversationcontent.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := ccc.mutation.GetType(); ok {
		_spec.SetField(conversationcontent.FieldType, field.TypeInt8, value)
		_node.Type = value
	}
	if nodes := ccc.mutation.ConversationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversationcontent.ConversationTable,
			Columns: []string{conversationcontent.ConversationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ConversationID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// ConversationContentCreateBulk is the builder for creating many ConversationContent entities in bulk.
type ConversationContentCreateBulk struct {
	config
	err      error
	builders []*ConversationContentCreate
}

// Save creates the ConversationContent entities in the database.
func (cccb *ConversationContentCreateBulk) Save(ctx context.Context) ([]*ConversationContent, error) {
	if cccb.err != nil {
		return nil, cccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(cccb.builders))
	nodes := make([]*ConversationContent, len(cccb.builders))
	mutators := make([]Mutator, len(cccb.builders))
	for i := range cccb.builders {
		func(i int, root context.Context) {
			builder := cccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ConversationContentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, cccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, cccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, cccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (cccb *ConversationContentCreateBulk) SaveX(ctx context.Context) []*ConversationContent {
	v, err := cccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cccb *ConversationContentCreateBulk) Exec(ctx context.Context) error {
	_, err := cccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cccb *ConversationContentCreateBulk) ExecX(ctx context.Context) {
	if err := cccb.Exec(ctx); err != nil {
		panic(err)
	}
}
