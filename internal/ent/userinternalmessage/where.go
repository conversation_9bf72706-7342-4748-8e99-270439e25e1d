// Code generated by ent, DO NOT EDIT.

package userinternalmessage

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldUpdatedAt, v))
}

// InternalMessageID applies equality check predicate on the "internal_message_id" field. It's identical to InternalMessageIDEQ.
func InternalMessageID(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldInternalMessageID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldUserID, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldStatus, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLTE(FieldUpdatedAt, v))
}

// InternalMessageIDEQ applies the EQ predicate on the "internal_message_id" field.
func InternalMessageIDEQ(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldInternalMessageID, v))
}

// InternalMessageIDNEQ applies the NEQ predicate on the "internal_message_id" field.
func InternalMessageIDNEQ(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldInternalMessageID, v))
}

// InternalMessageIDIn applies the In predicate on the "internal_message_id" field.
func InternalMessageIDIn(vs ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldInternalMessageID, vs...))
}

// InternalMessageIDNotIn applies the NotIn predicate on the "internal_message_id" field.
func InternalMessageIDNotIn(vs ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldInternalMessageID, vs...))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldUserID, vs...))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.FieldLTE(FieldStatus, v))
}

// HasMessage applies the HasEdge predicate on the "message" edge.
func HasMessage() predicate.UserInternalMessage {
	return predicate.UserInternalMessage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, MessageTable, MessageColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMessageWith applies the HasEdge predicate on the "message" edge with a given conditions (other predicates).
func HasMessageWith(preds ...predicate.InternalMessage) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(func(s *sql.Selector) {
		step := newMessageStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.UserInternalMessage {
	return predicate.UserInternalMessage(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.UserInternalMessage) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.UserInternalMessage) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.UserInternalMessage) predicate.UserInternalMessage {
	return predicate.UserInternalMessage(sql.NotPredicates(p))
}
