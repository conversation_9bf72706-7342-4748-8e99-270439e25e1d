// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/predicate"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationContentDelete is the builder for deleting a ConversationContent entity.
type ConversationContentDelete struct {
	config
	hooks    []Hook
	mutation *ConversationContentMutation
}

// Where appends a list predicates to the ConversationContentDelete builder.
func (ccd *ConversationContentDelete) Where(ps ...predicate.ConversationContent) *ConversationContentDelete {
	ccd.mutation.Where(ps...)
	return ccd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ccd *ConversationContentDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ccd.sqlExec, ccd.mutation, ccd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ccd *ConversationContentDelete) ExecX(ctx context.Context) int {
	n, err := ccd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ccd *ConversationContentDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(conversationcontent.Table, sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt))
	if ps := ccd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ccd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ccd.mutation.done = true
	return affected, err
}

// ConversationContentDeleteOne is the builder for deleting a single ConversationContent entity.
type ConversationContentDeleteOne struct {
	ccd *ConversationContentDelete
}

// Where appends a list predicates to the ConversationContentDelete builder.
func (ccdo *ConversationContentDeleteOne) Where(ps ...predicate.ConversationContent) *ConversationContentDeleteOne {
	ccdo.ccd.mutation.Where(ps...)
	return ccdo
}

// Exec executes the deletion query.
func (ccdo *ConversationContentDeleteOne) Exec(ctx context.Context) error {
	n, err := ccdo.ccd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{conversationcontent.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ccdo *ConversationContentDeleteOne) ExecX(ctx context.Context) {
	if err := ccdo.Exec(ctx); err != nil {
		panic(err)
	}
}
