// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationCreate is the builder for creating a Conversation entity.
type ConversationCreate struct {
	config
	mutation *ConversationMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (cc *ConversationCreate) SetCreatedAt(t time.Time) *ConversationCreate {
	cc.mutation.SetCreatedAt(t)
	return cc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (cc *ConversationCreate) SetNillableCreatedAt(t *time.Time) *ConversationCreate {
	if t != nil {
		cc.SetCreatedAt(*t)
	}
	return cc
}

// SetUpdatedAt sets the "updated_at" field.
func (cc *ConversationCreate) SetUpdatedAt(t time.Time) *ConversationCreate {
	cc.mutation.SetUpdatedAt(t)
	return cc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (cc *ConversationCreate) SetNillableUpdatedAt(t *time.Time) *ConversationCreate {
	if t != nil {
		cc.SetUpdatedAt(*t)
	}
	return cc
}

// SetAiAgentID sets the "ai_agent_id" field.
func (cc *ConversationCreate) SetAiAgentID(i int) *ConversationCreate {
	cc.mutation.SetAiAgentID(i)
	return cc
}

// SetUserID sets the "user_id" field.
func (cc *ConversationCreate) SetUserID(i int) *ConversationCreate {
	cc.mutation.SetUserID(i)
	return cc
}

// SetAiAgentConversationID sets the "ai_agent_conversation_id" field.
func (cc *ConversationCreate) SetAiAgentConversationID(s string) *ConversationCreate {
	cc.mutation.SetAiAgentConversationID(s)
	return cc
}

// SetName sets the "name" field.
func (cc *ConversationCreate) SetName(s string) *ConversationCreate {
	cc.mutation.SetName(s)
	return cc
}

// AddContentIDs adds the "contents" edge to the ConversationContent entity by IDs.
func (cc *ConversationCreate) AddContentIDs(ids ...int) *ConversationCreate {
	cc.mutation.AddContentIDs(ids...)
	return cc
}

// AddContents adds the "contents" edges to the ConversationContent entity.
func (cc *ConversationCreate) AddContents(c ...*ConversationContent) *ConversationCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cc.AddContentIDs(ids...)
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (cc *ConversationCreate) SetAiAgent(a *AiAgent) *ConversationCreate {
	return cc.SetAiAgentID(a.ID)
}

// Mutation returns the ConversationMutation object of the builder.
func (cc *ConversationCreate) Mutation() *ConversationMutation {
	return cc.mutation
}

// Save creates the Conversation in the database.
func (cc *ConversationCreate) Save(ctx context.Context) (*Conversation, error) {
	cc.defaults()
	return withHooks(ctx, cc.sqlSave, cc.mutation, cc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (cc *ConversationCreate) SaveX(ctx context.Context) *Conversation {
	v, err := cc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (cc *ConversationCreate) Exec(ctx context.Context) error {
	_, err := cc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cc *ConversationCreate) ExecX(ctx context.Context) {
	if err := cc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cc *ConversationCreate) defaults() {
	if _, ok := cc.mutation.CreatedAt(); !ok {
		v := conversation.DefaultCreatedAt()
		cc.mutation.SetCreatedAt(v)
	}
	if _, ok := cc.mutation.UpdatedAt(); !ok {
		v := conversation.DefaultUpdatedAt()
		cc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cc *ConversationCreate) check() error {
	if _, ok := cc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Conversation.created_at"`)}
	}
	if _, ok := cc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Conversation.updated_at"`)}
	}
	if _, ok := cc.mutation.AiAgentID(); !ok {
		return &ValidationError{Name: "ai_agent_id", err: errors.New(`ent: missing required field "Conversation.ai_agent_id"`)}
	}
	if _, ok := cc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Conversation.user_id"`)}
	}
	if _, ok := cc.mutation.AiAgentConversationID(); !ok {
		return &ValidationError{Name: "ai_agent_conversation_id", err: errors.New(`ent: missing required field "Conversation.ai_agent_conversation_id"`)}
	}
	if _, ok := cc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Conversation.name"`)}
	}
	if len(cc.mutation.AiAgentIDs()) == 0 {
		return &ValidationError{Name: "ai_agent", err: errors.New(`ent: missing required edge "Conversation.ai_agent"`)}
	}
	return nil
}

func (cc *ConversationCreate) sqlSave(ctx context.Context) (*Conversation, error) {
	if err := cc.check(); err != nil {
		return nil, err
	}
	_node, _spec := cc.createSpec()
	if err := sqlgraph.CreateNode(ctx, cc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	cc.mutation.id = &_node.ID
	cc.mutation.done = true
	return _node, nil
}

func (cc *ConversationCreate) createSpec() (*Conversation, *sqlgraph.CreateSpec) {
	var (
		_node = &Conversation{config: cc.config}
		_spec = sqlgraph.NewCreateSpec(conversation.Table, sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt))
	)
	if value, ok := cc.mutation.CreatedAt(); ok {
		_spec.SetField(conversation.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := cc.mutation.UpdatedAt(); ok {
		_spec.SetField(conversation.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := cc.mutation.UserID(); ok {
		_spec.SetField(conversation.FieldUserID, field.TypeInt, value)
		_node.UserID = value
	}
	if value, ok := cc.mutation.AiAgentConversationID(); ok {
		_spec.SetField(conversation.FieldAiAgentConversationID, field.TypeString, value)
		_node.AiAgentConversationID = value
	}
	if value, ok := cc.mutation.Name(); ok {
		_spec.SetField(conversation.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if nodes := cc.mutation.ContentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := cc.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversation.AiAgentTable,
			Columns: []string{conversation.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.AiAgentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// ConversationCreateBulk is the builder for creating many Conversation entities in bulk.
type ConversationCreateBulk struct {
	config
	err      error
	builders []*ConversationCreate
}

// Save creates the Conversation entities in the database.
func (ccb *ConversationCreateBulk) Save(ctx context.Context) ([]*Conversation, error) {
	if ccb.err != nil {
		return nil, ccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ccb.builders))
	nodes := make([]*Conversation, len(ccb.builders))
	mutators := make([]Mutator, len(ccb.builders))
	for i := range ccb.builders {
		func(i int, root context.Context) {
			builder := ccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ConversationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ccb *ConversationCreateBulk) SaveX(ctx context.Context) []*Conversation {
	v, err := ccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ccb *ConversationCreateBulk) Exec(ctx context.Context) error {
	_, err := ccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ccb *ConversationCreateBulk) ExecX(ctx context.Context) {
	if err := ccb.Exec(ctx); err != nil {
		panic(err)
	}
}
