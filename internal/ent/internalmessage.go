// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 站内信
type InternalMessage struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 标题
	Title string `json:"title,omitempty"`
	// 内容
	Content string `json:"content,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the InternalMessageQuery when eager-loading is set.
	Edges        InternalMessageEdges `json:"edges"`
	selectValues sql.SelectValues
}

// InternalMessageEdges holds the relations/edges for other nodes in the graph.
type InternalMessageEdges struct {
	// Users holds the value of the users edge.
	Users []*UserInternalMessage `json:"users,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading.
func (e InternalMessageEdges) UsersOrErr() ([]*UserInternalMessage, error) {
	if e.loadedTypes[0] {
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*InternalMessage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case internalmessage.FieldID:
			values[i] = new(sql.NullInt64)
		case internalmessage.FieldTitle, internalmessage.FieldContent:
			values[i] = new(sql.NullString)
		case internalmessage.FieldCreatedAt, internalmessage.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the InternalMessage fields.
func (im *InternalMessage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case internalmessage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			im.ID = int(value.Int64)
		case internalmessage.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				im.CreatedAt = value.Time
			}
		case internalmessage.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				im.UpdatedAt = value.Time
			}
		case internalmessage.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				im.Title = value.String
			}
		case internalmessage.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				im.Content = value.String
			}
		default:
			im.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the InternalMessage.
// This includes values selected through modifiers, order, etc.
func (im *InternalMessage) Value(name string) (ent.Value, error) {
	return im.selectValues.Get(name)
}

// QueryUsers queries the "users" edge of the InternalMessage entity.
func (im *InternalMessage) QueryUsers() *UserInternalMessageQuery {
	return NewInternalMessageClient(im.config).QueryUsers(im)
}

// Update returns a builder for updating this InternalMessage.
// Note that you need to call InternalMessage.Unwrap() before calling this method if this InternalMessage
// was returned from a transaction, and the transaction was committed or rolled back.
func (im *InternalMessage) Update() *InternalMessageUpdateOne {
	return NewInternalMessageClient(im.config).UpdateOne(im)
}

// Unwrap unwraps the InternalMessage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (im *InternalMessage) Unwrap() *InternalMessage {
	_tx, ok := im.config.driver.(*txDriver)
	if !ok {
		panic("ent: InternalMessage is not a transactional entity")
	}
	im.config.driver = _tx.drv
	return im
}

// String implements the fmt.Stringer.
func (im *InternalMessage) String() string {
	var builder strings.Builder
	builder.WriteString("InternalMessage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", im.ID))
	builder.WriteString("created_at=")
	builder.WriteString(im.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(im.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(im.Title)
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(im.Content)
	builder.WriteByte(')')
	return builder.String()
}

// InternalMessages is a parsable slice of InternalMessage.
type InternalMessages []*InternalMessage
