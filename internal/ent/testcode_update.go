// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCodeUpdate is the builder for updating TestCode entities.
type TestCodeUpdate struct {
	config
	hooks    []Hook
	mutation *TestCodeMutation
}

// Where appends a list predicates to the TestCodeUpdate builder.
func (tcu *TestCodeUpdate) Where(ps ...predicate.TestCode) *TestCodeUpdate {
	tcu.mutation.Where(ps...)
	return tcu
}

// SetUpdatedAt sets the "updated_at" field.
func (tcu *TestCodeUpdate) SetUpdatedAt(t time.Time) *TestCodeUpdate {
	tcu.mutation.SetUpdatedAt(t)
	return tcu
}

// SetBatchID sets the "batch_id" field.
func (tcu *TestCodeUpdate) SetBatchID(i int) *TestCodeUpdate {
	tcu.mutation.SetBatchID(i)
	return tcu
}

// SetNillableBatchID sets the "batch_id" field if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableBatchID(i *int) *TestCodeUpdate {
	if i != nil {
		tcu.SetBatchID(*i)
	}
	return tcu
}

// SetCode sets the "code" field.
func (tcu *TestCodeUpdate) SetCode(s string) *TestCodeUpdate {
	tcu.mutation.SetCode(s)
	return tcu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableCode(s *string) *TestCodeUpdate {
	if s != nil {
		tcu.SetCode(*s)
	}
	return tcu
}

// SetDevice sets the "device" field.
func (tcu *TestCodeUpdate) SetDevice(s string) *TestCodeUpdate {
	tcu.mutation.SetDevice(s)
	return tcu
}

// SetNillableDevice sets the "device" field if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableDevice(s *string) *TestCodeUpdate {
	if s != nil {
		tcu.SetDevice(*s)
	}
	return tcu
}

// ClearDevice clears the value of the "device" field.
func (tcu *TestCodeUpdate) ClearDevice() *TestCodeUpdate {
	tcu.mutation.ClearDevice()
	return tcu
}

// SetStatus sets the "status" field.
func (tcu *TestCodeUpdate) SetStatus(i int8) *TestCodeUpdate {
	tcu.mutation.ResetStatus()
	tcu.mutation.SetStatus(i)
	return tcu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableStatus(i *int8) *TestCodeUpdate {
	if i != nil {
		tcu.SetStatus(*i)
	}
	return tcu
}

// AddStatus adds i to the "status" field.
func (tcu *TestCodeUpdate) AddStatus(i int8) *TestCodeUpdate {
	tcu.mutation.AddStatus(i)
	return tcu
}

// SetExpiredAt sets the "expired_at" field.
func (tcu *TestCodeUpdate) SetExpiredAt(t time.Time) *TestCodeUpdate {
	tcu.mutation.SetExpiredAt(t)
	return tcu
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableExpiredAt(t *time.Time) *TestCodeUpdate {
	if t != nil {
		tcu.SetExpiredAt(*t)
	}
	return tcu
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (tcu *TestCodeUpdate) ClearExpiredAt() *TestCodeUpdate {
	tcu.mutation.ClearExpiredAt()
	return tcu
}

// SetUserID sets the "user" edge to the User entity by ID.
func (tcu *TestCodeUpdate) SetUserID(id int) *TestCodeUpdate {
	tcu.mutation.SetUserID(id)
	return tcu
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (tcu *TestCodeUpdate) SetNillableUserID(id *int) *TestCodeUpdate {
	if id != nil {
		tcu = tcu.SetUserID(*id)
	}
	return tcu
}

// SetUser sets the "user" edge to the User entity.
func (tcu *TestCodeUpdate) SetUser(u *User) *TestCodeUpdate {
	return tcu.SetUserID(u.ID)
}

// SetBatch sets the "batch" edge to the Batch entity.
func (tcu *TestCodeUpdate) SetBatch(b *Batch) *TestCodeUpdate {
	return tcu.SetBatchID(b.ID)
}

// Mutation returns the TestCodeMutation object of the builder.
func (tcu *TestCodeUpdate) Mutation() *TestCodeMutation {
	return tcu.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (tcu *TestCodeUpdate) ClearUser() *TestCodeUpdate {
	tcu.mutation.ClearUser()
	return tcu
}

// ClearBatch clears the "batch" edge to the Batch entity.
func (tcu *TestCodeUpdate) ClearBatch() *TestCodeUpdate {
	tcu.mutation.ClearBatch()
	return tcu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tcu *TestCodeUpdate) Save(ctx context.Context) (int, error) {
	tcu.defaults()
	return withHooks(ctx, tcu.sqlSave, tcu.mutation, tcu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tcu *TestCodeUpdate) SaveX(ctx context.Context) int {
	affected, err := tcu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tcu *TestCodeUpdate) Exec(ctx context.Context) error {
	_, err := tcu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcu *TestCodeUpdate) ExecX(ctx context.Context) {
	if err := tcu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tcu *TestCodeUpdate) defaults() {
	if _, ok := tcu.mutation.UpdatedAt(); !ok {
		v := testcode.UpdateDefaultUpdatedAt()
		tcu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tcu *TestCodeUpdate) check() error {
	if tcu.mutation.BatchCleared() && len(tcu.mutation.BatchIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "TestCode.batch"`)
	}
	return nil
}

func (tcu *TestCodeUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := tcu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(testcode.Table, testcode.Columns, sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt))
	if ps := tcu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tcu.mutation.UpdatedAt(); ok {
		_spec.SetField(testcode.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tcu.mutation.Code(); ok {
		_spec.SetField(testcode.FieldCode, field.TypeString, value)
	}
	if value, ok := tcu.mutation.Device(); ok {
		_spec.SetField(testcode.FieldDevice, field.TypeString, value)
	}
	if tcu.mutation.DeviceCleared() {
		_spec.ClearField(testcode.FieldDevice, field.TypeString)
	}
	if value, ok := tcu.mutation.Status(); ok {
		_spec.SetField(testcode.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tcu.mutation.AddedStatus(); ok {
		_spec.AddField(testcode.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tcu.mutation.ExpiredAt(); ok {
		_spec.SetField(testcode.FieldExpiredAt, field.TypeTime, value)
	}
	if tcu.mutation.ExpiredAtCleared() {
		_spec.ClearField(testcode.FieldExpiredAt, field.TypeTime)
	}
	if tcu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   testcode.UserTable,
			Columns: []string{testcode.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tcu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   testcode.UserTable,
			Columns: []string{testcode.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tcu.mutation.BatchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   testcode.BatchTable,
			Columns: []string{testcode.BatchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tcu.mutation.BatchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   testcode.BatchTable,
			Columns: []string{testcode.BatchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tcu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{testcode.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tcu.mutation.done = true
	return n, nil
}

// TestCodeUpdateOne is the builder for updating a single TestCode entity.
type TestCodeUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TestCodeMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (tcuo *TestCodeUpdateOne) SetUpdatedAt(t time.Time) *TestCodeUpdateOne {
	tcuo.mutation.SetUpdatedAt(t)
	return tcuo
}

// SetBatchID sets the "batch_id" field.
func (tcuo *TestCodeUpdateOne) SetBatchID(i int) *TestCodeUpdateOne {
	tcuo.mutation.SetBatchID(i)
	return tcuo
}

// SetNillableBatchID sets the "batch_id" field if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableBatchID(i *int) *TestCodeUpdateOne {
	if i != nil {
		tcuo.SetBatchID(*i)
	}
	return tcuo
}

// SetCode sets the "code" field.
func (tcuo *TestCodeUpdateOne) SetCode(s string) *TestCodeUpdateOne {
	tcuo.mutation.SetCode(s)
	return tcuo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableCode(s *string) *TestCodeUpdateOne {
	if s != nil {
		tcuo.SetCode(*s)
	}
	return tcuo
}

// SetDevice sets the "device" field.
func (tcuo *TestCodeUpdateOne) SetDevice(s string) *TestCodeUpdateOne {
	tcuo.mutation.SetDevice(s)
	return tcuo
}

// SetNillableDevice sets the "device" field if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableDevice(s *string) *TestCodeUpdateOne {
	if s != nil {
		tcuo.SetDevice(*s)
	}
	return tcuo
}

// ClearDevice clears the value of the "device" field.
func (tcuo *TestCodeUpdateOne) ClearDevice() *TestCodeUpdateOne {
	tcuo.mutation.ClearDevice()
	return tcuo
}

// SetStatus sets the "status" field.
func (tcuo *TestCodeUpdateOne) SetStatus(i int8) *TestCodeUpdateOne {
	tcuo.mutation.ResetStatus()
	tcuo.mutation.SetStatus(i)
	return tcuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableStatus(i *int8) *TestCodeUpdateOne {
	if i != nil {
		tcuo.SetStatus(*i)
	}
	return tcuo
}

// AddStatus adds i to the "status" field.
func (tcuo *TestCodeUpdateOne) AddStatus(i int8) *TestCodeUpdateOne {
	tcuo.mutation.AddStatus(i)
	return tcuo
}

// SetExpiredAt sets the "expired_at" field.
func (tcuo *TestCodeUpdateOne) SetExpiredAt(t time.Time) *TestCodeUpdateOne {
	tcuo.mutation.SetExpiredAt(t)
	return tcuo
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableExpiredAt(t *time.Time) *TestCodeUpdateOne {
	if t != nil {
		tcuo.SetExpiredAt(*t)
	}
	return tcuo
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (tcuo *TestCodeUpdateOne) ClearExpiredAt() *TestCodeUpdateOne {
	tcuo.mutation.ClearExpiredAt()
	return tcuo
}

// SetUserID sets the "user" edge to the User entity by ID.
func (tcuo *TestCodeUpdateOne) SetUserID(id int) *TestCodeUpdateOne {
	tcuo.mutation.SetUserID(id)
	return tcuo
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (tcuo *TestCodeUpdateOne) SetNillableUserID(id *int) *TestCodeUpdateOne {
	if id != nil {
		tcuo = tcuo.SetUserID(*id)
	}
	return tcuo
}

// SetUser sets the "user" edge to the User entity.
func (tcuo *TestCodeUpdateOne) SetUser(u *User) *TestCodeUpdateOne {
	return tcuo.SetUserID(u.ID)
}

// SetBatch sets the "batch" edge to the Batch entity.
func (tcuo *TestCodeUpdateOne) SetBatch(b *Batch) *TestCodeUpdateOne {
	return tcuo.SetBatchID(b.ID)
}

// Mutation returns the TestCodeMutation object of the builder.
func (tcuo *TestCodeUpdateOne) Mutation() *TestCodeMutation {
	return tcuo.mutation
}

// ClearUser clears the "user" edge to the User entity.
func (tcuo *TestCodeUpdateOne) ClearUser() *TestCodeUpdateOne {
	tcuo.mutation.ClearUser()
	return tcuo
}

// ClearBatch clears the "batch" edge to the Batch entity.
func (tcuo *TestCodeUpdateOne) ClearBatch() *TestCodeUpdateOne {
	tcuo.mutation.ClearBatch()
	return tcuo
}

// Where appends a list predicates to the TestCodeUpdate builder.
func (tcuo *TestCodeUpdateOne) Where(ps ...predicate.TestCode) *TestCodeUpdateOne {
	tcuo.mutation.Where(ps...)
	return tcuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tcuo *TestCodeUpdateOne) Select(field string, fields ...string) *TestCodeUpdateOne {
	tcuo.fields = append([]string{field}, fields...)
	return tcuo
}

// Save executes the query and returns the updated TestCode entity.
func (tcuo *TestCodeUpdateOne) Save(ctx context.Context) (*TestCode, error) {
	tcuo.defaults()
	return withHooks(ctx, tcuo.sqlSave, tcuo.mutation, tcuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tcuo *TestCodeUpdateOne) SaveX(ctx context.Context) *TestCode {
	node, err := tcuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tcuo *TestCodeUpdateOne) Exec(ctx context.Context) error {
	_, err := tcuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcuo *TestCodeUpdateOne) ExecX(ctx context.Context) {
	if err := tcuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tcuo *TestCodeUpdateOne) defaults() {
	if _, ok := tcuo.mutation.UpdatedAt(); !ok {
		v := testcode.UpdateDefaultUpdatedAt()
		tcuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tcuo *TestCodeUpdateOne) check() error {
	if tcuo.mutation.BatchCleared() && len(tcuo.mutation.BatchIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "TestCode.batch"`)
	}
	return nil
}

func (tcuo *TestCodeUpdateOne) sqlSave(ctx context.Context) (_node *TestCode, err error) {
	if err := tcuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(testcode.Table, testcode.Columns, sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt))
	id, ok := tcuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "TestCode.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tcuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, testcode.FieldID)
		for _, f := range fields {
			if !testcode.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != testcode.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tcuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tcuo.mutation.UpdatedAt(); ok {
		_spec.SetField(testcode.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tcuo.mutation.Code(); ok {
		_spec.SetField(testcode.FieldCode, field.TypeString, value)
	}
	if value, ok := tcuo.mutation.Device(); ok {
		_spec.SetField(testcode.FieldDevice, field.TypeString, value)
	}
	if tcuo.mutation.DeviceCleared() {
		_spec.ClearField(testcode.FieldDevice, field.TypeString)
	}
	if value, ok := tcuo.mutation.Status(); ok {
		_spec.SetField(testcode.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tcuo.mutation.AddedStatus(); ok {
		_spec.AddField(testcode.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := tcuo.mutation.ExpiredAt(); ok {
		_spec.SetField(testcode.FieldExpiredAt, field.TypeTime, value)
	}
	if tcuo.mutation.ExpiredAtCleared() {
		_spec.ClearField(testcode.FieldExpiredAt, field.TypeTime)
	}
	if tcuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   testcode.UserTable,
			Columns: []string{testcode.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tcuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   testcode.UserTable,
			Columns: []string{testcode.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tcuo.mutation.BatchCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   testcode.BatchTable,
			Columns: []string{testcode.BatchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tcuo.mutation.BatchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   testcode.BatchTable,
			Columns: []string{testcode.BatchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &TestCode{config: tcuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tcuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{testcode.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tcuo.mutation.done = true
	return _node, nil
}
