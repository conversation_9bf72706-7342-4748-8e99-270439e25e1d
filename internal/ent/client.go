// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"bole-ai/internal/ent/migrate"

	"bole-ai/internal/ent/adminuser"
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/screenplay"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// AdminUser is the client for interacting with the AdminUser builders.
	AdminUser *AdminUserClient
	// AiAgent is the client for interacting with the AiAgent builders.
	AiAgent *AiAgentClient
	// Batch is the client for interacting with the Batch builders.
	Batch *BatchClient
	// Conversation is the client for interacting with the Conversation builders.
	Conversation *ConversationClient
	// ConversationContent is the client for interacting with the ConversationContent builders.
	ConversationContent *ConversationContentClient
	// FrequentAskQuestion is the client for interacting with the FrequentAskQuestion builders.
	FrequentAskQuestion *FrequentAskQuestionClient
	// InternalMessage is the client for interacting with the InternalMessage builders.
	InternalMessage *InternalMessageClient
	// Project is the client for interacting with the Project builders.
	Project *ProjectClient
	// Screenplay is the client for interacting with the Screenplay builders.
	Screenplay *ScreenplayClient
	// TestCode is the client for interacting with the TestCode builders.
	TestCode *TestCodeClient
	// User is the client for interacting with the User builders.
	User *UserClient
	// UserInternalMessage is the client for interacting with the UserInternalMessage builders.
	UserInternalMessage *UserInternalMessageClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.AdminUser = NewAdminUserClient(c.config)
	c.AiAgent = NewAiAgentClient(c.config)
	c.Batch = NewBatchClient(c.config)
	c.Conversation = NewConversationClient(c.config)
	c.ConversationContent = NewConversationContentClient(c.config)
	c.FrequentAskQuestion = NewFrequentAskQuestionClient(c.config)
	c.InternalMessage = NewInternalMessageClient(c.config)
	c.Project = NewProjectClient(c.config)
	c.Screenplay = NewScreenplayClient(c.config)
	c.TestCode = NewTestCodeClient(c.config)
	c.User = NewUserClient(c.config)
	c.UserInternalMessage = NewUserInternalMessageClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		AdminUser:           NewAdminUserClient(cfg),
		AiAgent:             NewAiAgentClient(cfg),
		Batch:               NewBatchClient(cfg),
		Conversation:        NewConversationClient(cfg),
		ConversationContent: NewConversationContentClient(cfg),
		FrequentAskQuestion: NewFrequentAskQuestionClient(cfg),
		InternalMessage:     NewInternalMessageClient(cfg),
		Project:             NewProjectClient(cfg),
		Screenplay:          NewScreenplayClient(cfg),
		TestCode:            NewTestCodeClient(cfg),
		User:                NewUserClient(cfg),
		UserInternalMessage: NewUserInternalMessageClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                 ctx,
		config:              cfg,
		AdminUser:           NewAdminUserClient(cfg),
		AiAgent:             NewAiAgentClient(cfg),
		Batch:               NewBatchClient(cfg),
		Conversation:        NewConversationClient(cfg),
		ConversationContent: NewConversationContentClient(cfg),
		FrequentAskQuestion: NewFrequentAskQuestionClient(cfg),
		InternalMessage:     NewInternalMessageClient(cfg),
		Project:             NewProjectClient(cfg),
		Screenplay:          NewScreenplayClient(cfg),
		TestCode:            NewTestCodeClient(cfg),
		User:                NewUserClient(cfg),
		UserInternalMessage: NewUserInternalMessageClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		AdminUser.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.AdminUser, c.AiAgent, c.Batch, c.Conversation, c.ConversationContent,
		c.FrequentAskQuestion, c.InternalMessage, c.Project, c.Screenplay, c.TestCode,
		c.User, c.UserInternalMessage,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.AdminUser, c.AiAgent, c.Batch, c.Conversation, c.ConversationContent,
		c.FrequentAskQuestion, c.InternalMessage, c.Project, c.Screenplay, c.TestCode,
		c.User, c.UserInternalMessage,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *AdminUserMutation:
		return c.AdminUser.mutate(ctx, m)
	case *AiAgentMutation:
		return c.AiAgent.mutate(ctx, m)
	case *BatchMutation:
		return c.Batch.mutate(ctx, m)
	case *ConversationMutation:
		return c.Conversation.mutate(ctx, m)
	case *ConversationContentMutation:
		return c.ConversationContent.mutate(ctx, m)
	case *FrequentAskQuestionMutation:
		return c.FrequentAskQuestion.mutate(ctx, m)
	case *InternalMessageMutation:
		return c.InternalMessage.mutate(ctx, m)
	case *ProjectMutation:
		return c.Project.mutate(ctx, m)
	case *ScreenplayMutation:
		return c.Screenplay.mutate(ctx, m)
	case *TestCodeMutation:
		return c.TestCode.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	case *UserInternalMessageMutation:
		return c.UserInternalMessage.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// AdminUserClient is a client for the AdminUser schema.
type AdminUserClient struct {
	config
}

// NewAdminUserClient returns a client for the AdminUser from the given config.
func NewAdminUserClient(c config) *AdminUserClient {
	return &AdminUserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `adminuser.Hooks(f(g(h())))`.
func (c *AdminUserClient) Use(hooks ...Hook) {
	c.hooks.AdminUser = append(c.hooks.AdminUser, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `adminuser.Intercept(f(g(h())))`.
func (c *AdminUserClient) Intercept(interceptors ...Interceptor) {
	c.inters.AdminUser = append(c.inters.AdminUser, interceptors...)
}

// Create returns a builder for creating a AdminUser entity.
func (c *AdminUserClient) Create() *AdminUserCreate {
	mutation := newAdminUserMutation(c.config, OpCreate)
	return &AdminUserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AdminUser entities.
func (c *AdminUserClient) CreateBulk(builders ...*AdminUserCreate) *AdminUserCreateBulk {
	return &AdminUserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AdminUserClient) MapCreateBulk(slice any, setFunc func(*AdminUserCreate, int)) *AdminUserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AdminUserCreateBulk{err: fmt.Errorf("calling to AdminUserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AdminUserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AdminUserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AdminUser.
func (c *AdminUserClient) Update() *AdminUserUpdate {
	mutation := newAdminUserMutation(c.config, OpUpdate)
	return &AdminUserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AdminUserClient) UpdateOne(au *AdminUser) *AdminUserUpdateOne {
	mutation := newAdminUserMutation(c.config, OpUpdateOne, withAdminUser(au))
	return &AdminUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AdminUserClient) UpdateOneID(id int) *AdminUserUpdateOne {
	mutation := newAdminUserMutation(c.config, OpUpdateOne, withAdminUserID(id))
	return &AdminUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AdminUser.
func (c *AdminUserClient) Delete() *AdminUserDelete {
	mutation := newAdminUserMutation(c.config, OpDelete)
	return &AdminUserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AdminUserClient) DeleteOne(au *AdminUser) *AdminUserDeleteOne {
	return c.DeleteOneID(au.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AdminUserClient) DeleteOneID(id int) *AdminUserDeleteOne {
	builder := c.Delete().Where(adminuser.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AdminUserDeleteOne{builder}
}

// Query returns a query builder for AdminUser.
func (c *AdminUserClient) Query() *AdminUserQuery {
	return &AdminUserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAdminUser},
		inters: c.Interceptors(),
	}
}

// Get returns a AdminUser entity by its id.
func (c *AdminUserClient) Get(ctx context.Context, id int) (*AdminUser, error) {
	return c.Query().Where(adminuser.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AdminUserClient) GetX(ctx context.Context, id int) *AdminUser {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *AdminUserClient) Hooks() []Hook {
	return c.hooks.AdminUser
}

// Interceptors returns the client interceptors.
func (c *AdminUserClient) Interceptors() []Interceptor {
	return c.inters.AdminUser
}

func (c *AdminUserClient) mutate(ctx context.Context, m *AdminUserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AdminUserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AdminUserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AdminUserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AdminUserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AdminUser mutation op: %q", m.Op())
	}
}

// AiAgentClient is a client for the AiAgent schema.
type AiAgentClient struct {
	config
}

// NewAiAgentClient returns a client for the AiAgent from the given config.
func NewAiAgentClient(c config) *AiAgentClient {
	return &AiAgentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `aiagent.Hooks(f(g(h())))`.
func (c *AiAgentClient) Use(hooks ...Hook) {
	c.hooks.AiAgent = append(c.hooks.AiAgent, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `aiagent.Intercept(f(g(h())))`.
func (c *AiAgentClient) Intercept(interceptors ...Interceptor) {
	c.inters.AiAgent = append(c.inters.AiAgent, interceptors...)
}

// Create returns a builder for creating a AiAgent entity.
func (c *AiAgentClient) Create() *AiAgentCreate {
	mutation := newAiAgentMutation(c.config, OpCreate)
	return &AiAgentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of AiAgent entities.
func (c *AiAgentClient) CreateBulk(builders ...*AiAgentCreate) *AiAgentCreateBulk {
	return &AiAgentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *AiAgentClient) MapCreateBulk(slice any, setFunc func(*AiAgentCreate, int)) *AiAgentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &AiAgentCreateBulk{err: fmt.Errorf("calling to AiAgentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*AiAgentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &AiAgentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for AiAgent.
func (c *AiAgentClient) Update() *AiAgentUpdate {
	mutation := newAiAgentMutation(c.config, OpUpdate)
	return &AiAgentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *AiAgentClient) UpdateOne(aa *AiAgent) *AiAgentUpdateOne {
	mutation := newAiAgentMutation(c.config, OpUpdateOne, withAiAgent(aa))
	return &AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *AiAgentClient) UpdateOneID(id int) *AiAgentUpdateOne {
	mutation := newAiAgentMutation(c.config, OpUpdateOne, withAiAgentID(id))
	return &AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for AiAgent.
func (c *AiAgentClient) Delete() *AiAgentDelete {
	mutation := newAiAgentMutation(c.config, OpDelete)
	return &AiAgentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *AiAgentClient) DeleteOne(aa *AiAgent) *AiAgentDeleteOne {
	return c.DeleteOneID(aa.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *AiAgentClient) DeleteOneID(id int) *AiAgentDeleteOne {
	builder := c.Delete().Where(aiagent.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &AiAgentDeleteOne{builder}
}

// Query returns a query builder for AiAgent.
func (c *AiAgentClient) Query() *AiAgentQuery {
	return &AiAgentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAiAgent},
		inters: c.Interceptors(),
	}
}

// Get returns a AiAgent entity by its id.
func (c *AiAgentClient) Get(ctx context.Context, id int) (*AiAgent, error) {
	return c.Query().Where(aiagent.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *AiAgentClient) GetX(ctx context.Context, id int) *AiAgent {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryConversations queries the conversations edge of a AiAgent.
func (c *AiAgentClient) QueryConversations(aa *AiAgent) *ConversationQuery {
	query := (&ConversationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := aa.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(aiagent.Table, aiagent.FieldID, id),
			sqlgraph.To(conversation.Table, conversation.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, aiagent.ConversationsTable, aiagent.ConversationsColumn),
		)
		fromV = sqlgraph.Neighbors(aa.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *AiAgentClient) Hooks() []Hook {
	return c.hooks.AiAgent
}

// Interceptors returns the client interceptors.
func (c *AiAgentClient) Interceptors() []Interceptor {
	return c.inters.AiAgent
}

func (c *AiAgentClient) mutate(ctx context.Context, m *AiAgentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&AiAgentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&AiAgentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&AiAgentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&AiAgentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown AiAgent mutation op: %q", m.Op())
	}
}

// BatchClient is a client for the Batch schema.
type BatchClient struct {
	config
}

// NewBatchClient returns a client for the Batch from the given config.
func NewBatchClient(c config) *BatchClient {
	return &BatchClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `batch.Hooks(f(g(h())))`.
func (c *BatchClient) Use(hooks ...Hook) {
	c.hooks.Batch = append(c.hooks.Batch, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `batch.Intercept(f(g(h())))`.
func (c *BatchClient) Intercept(interceptors ...Interceptor) {
	c.inters.Batch = append(c.inters.Batch, interceptors...)
}

// Create returns a builder for creating a Batch entity.
func (c *BatchClient) Create() *BatchCreate {
	mutation := newBatchMutation(c.config, OpCreate)
	return &BatchCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Batch entities.
func (c *BatchClient) CreateBulk(builders ...*BatchCreate) *BatchCreateBulk {
	return &BatchCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *BatchClient) MapCreateBulk(slice any, setFunc func(*BatchCreate, int)) *BatchCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &BatchCreateBulk{err: fmt.Errorf("calling to BatchClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*BatchCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &BatchCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Batch.
func (c *BatchClient) Update() *BatchUpdate {
	mutation := newBatchMutation(c.config, OpUpdate)
	return &BatchUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *BatchClient) UpdateOne(b *Batch) *BatchUpdateOne {
	mutation := newBatchMutation(c.config, OpUpdateOne, withBatch(b))
	return &BatchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *BatchClient) UpdateOneID(id int) *BatchUpdateOne {
	mutation := newBatchMutation(c.config, OpUpdateOne, withBatchID(id))
	return &BatchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Batch.
func (c *BatchClient) Delete() *BatchDelete {
	mutation := newBatchMutation(c.config, OpDelete)
	return &BatchDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *BatchClient) DeleteOne(b *Batch) *BatchDeleteOne {
	return c.DeleteOneID(b.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *BatchClient) DeleteOneID(id int) *BatchDeleteOne {
	builder := c.Delete().Where(batch.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &BatchDeleteOne{builder}
}

// Query returns a query builder for Batch.
func (c *BatchClient) Query() *BatchQuery {
	return &BatchQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeBatch},
		inters: c.Interceptors(),
	}
}

// Get returns a Batch entity by its id.
func (c *BatchClient) Get(ctx context.Context, id int) (*Batch, error) {
	return c.Query().Where(batch.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *BatchClient) GetX(ctx context.Context, id int) *Batch {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCodes queries the codes edge of a Batch.
func (c *BatchClient) QueryCodes(b *Batch) *TestCodeQuery {
	query := (&TestCodeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(batch.Table, batch.FieldID, id),
			sqlgraph.To(testcode.Table, testcode.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, batch.CodesTable, batch.CodesColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Batch.
func (c *BatchClient) QueryUsers(b *Batch) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(batch.Table, batch.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, batch.UsersTable, batch.UsersColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *BatchClient) Hooks() []Hook {
	return c.hooks.Batch
}

// Interceptors returns the client interceptors.
func (c *BatchClient) Interceptors() []Interceptor {
	return c.inters.Batch
}

func (c *BatchClient) mutate(ctx context.Context, m *BatchMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&BatchCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&BatchUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&BatchUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&BatchDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Batch mutation op: %q", m.Op())
	}
}

// ConversationClient is a client for the Conversation schema.
type ConversationClient struct {
	config
}

// NewConversationClient returns a client for the Conversation from the given config.
func NewConversationClient(c config) *ConversationClient {
	return &ConversationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `conversation.Hooks(f(g(h())))`.
func (c *ConversationClient) Use(hooks ...Hook) {
	c.hooks.Conversation = append(c.hooks.Conversation, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `conversation.Intercept(f(g(h())))`.
func (c *ConversationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Conversation = append(c.inters.Conversation, interceptors...)
}

// Create returns a builder for creating a Conversation entity.
func (c *ConversationClient) Create() *ConversationCreate {
	mutation := newConversationMutation(c.config, OpCreate)
	return &ConversationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Conversation entities.
func (c *ConversationClient) CreateBulk(builders ...*ConversationCreate) *ConversationCreateBulk {
	return &ConversationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ConversationClient) MapCreateBulk(slice any, setFunc func(*ConversationCreate, int)) *ConversationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ConversationCreateBulk{err: fmt.Errorf("calling to ConversationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ConversationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ConversationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Conversation.
func (c *ConversationClient) Update() *ConversationUpdate {
	mutation := newConversationMutation(c.config, OpUpdate)
	return &ConversationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ConversationClient) UpdateOne(co *Conversation) *ConversationUpdateOne {
	mutation := newConversationMutation(c.config, OpUpdateOne, withConversation(co))
	return &ConversationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ConversationClient) UpdateOneID(id int) *ConversationUpdateOne {
	mutation := newConversationMutation(c.config, OpUpdateOne, withConversationID(id))
	return &ConversationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Conversation.
func (c *ConversationClient) Delete() *ConversationDelete {
	mutation := newConversationMutation(c.config, OpDelete)
	return &ConversationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ConversationClient) DeleteOne(co *Conversation) *ConversationDeleteOne {
	return c.DeleteOneID(co.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ConversationClient) DeleteOneID(id int) *ConversationDeleteOne {
	builder := c.Delete().Where(conversation.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ConversationDeleteOne{builder}
}

// Query returns a query builder for Conversation.
func (c *ConversationClient) Query() *ConversationQuery {
	return &ConversationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeConversation},
		inters: c.Interceptors(),
	}
}

// Get returns a Conversation entity by its id.
func (c *ConversationClient) Get(ctx context.Context, id int) (*Conversation, error) {
	return c.Query().Where(conversation.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ConversationClient) GetX(ctx context.Context, id int) *Conversation {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryContents queries the contents edge of a Conversation.
func (c *ConversationClient) QueryContents(co *Conversation) *ConversationContentQuery {
	query := (&ConversationContentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := co.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(conversation.Table, conversation.FieldID, id),
			sqlgraph.To(conversationcontent.Table, conversationcontent.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, conversation.ContentsTable, conversation.ContentsColumn),
		)
		fromV = sqlgraph.Neighbors(co.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAiAgent queries the ai_agent edge of a Conversation.
func (c *ConversationClient) QueryAiAgent(co *Conversation) *AiAgentQuery {
	query := (&AiAgentClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := co.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(conversation.Table, conversation.FieldID, id),
			sqlgraph.To(aiagent.Table, aiagent.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, conversation.AiAgentTable, conversation.AiAgentColumn),
		)
		fromV = sqlgraph.Neighbors(co.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ConversationClient) Hooks() []Hook {
	return c.hooks.Conversation
}

// Interceptors returns the client interceptors.
func (c *ConversationClient) Interceptors() []Interceptor {
	return c.inters.Conversation
}

func (c *ConversationClient) mutate(ctx context.Context, m *ConversationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ConversationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ConversationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ConversationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ConversationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Conversation mutation op: %q", m.Op())
	}
}

// ConversationContentClient is a client for the ConversationContent schema.
type ConversationContentClient struct {
	config
}

// NewConversationContentClient returns a client for the ConversationContent from the given config.
func NewConversationContentClient(c config) *ConversationContentClient {
	return &ConversationContentClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `conversationcontent.Hooks(f(g(h())))`.
func (c *ConversationContentClient) Use(hooks ...Hook) {
	c.hooks.ConversationContent = append(c.hooks.ConversationContent, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `conversationcontent.Intercept(f(g(h())))`.
func (c *ConversationContentClient) Intercept(interceptors ...Interceptor) {
	c.inters.ConversationContent = append(c.inters.ConversationContent, interceptors...)
}

// Create returns a builder for creating a ConversationContent entity.
func (c *ConversationContentClient) Create() *ConversationContentCreate {
	mutation := newConversationContentMutation(c.config, OpCreate)
	return &ConversationContentCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of ConversationContent entities.
func (c *ConversationContentClient) CreateBulk(builders ...*ConversationContentCreate) *ConversationContentCreateBulk {
	return &ConversationContentCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ConversationContentClient) MapCreateBulk(slice any, setFunc func(*ConversationContentCreate, int)) *ConversationContentCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ConversationContentCreateBulk{err: fmt.Errorf("calling to ConversationContentClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ConversationContentCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ConversationContentCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for ConversationContent.
func (c *ConversationContentClient) Update() *ConversationContentUpdate {
	mutation := newConversationContentMutation(c.config, OpUpdate)
	return &ConversationContentUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ConversationContentClient) UpdateOne(cc *ConversationContent) *ConversationContentUpdateOne {
	mutation := newConversationContentMutation(c.config, OpUpdateOne, withConversationContent(cc))
	return &ConversationContentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ConversationContentClient) UpdateOneID(id int) *ConversationContentUpdateOne {
	mutation := newConversationContentMutation(c.config, OpUpdateOne, withConversationContentID(id))
	return &ConversationContentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for ConversationContent.
func (c *ConversationContentClient) Delete() *ConversationContentDelete {
	mutation := newConversationContentMutation(c.config, OpDelete)
	return &ConversationContentDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ConversationContentClient) DeleteOne(cc *ConversationContent) *ConversationContentDeleteOne {
	return c.DeleteOneID(cc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ConversationContentClient) DeleteOneID(id int) *ConversationContentDeleteOne {
	builder := c.Delete().Where(conversationcontent.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ConversationContentDeleteOne{builder}
}

// Query returns a query builder for ConversationContent.
func (c *ConversationContentClient) Query() *ConversationContentQuery {
	return &ConversationContentQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeConversationContent},
		inters: c.Interceptors(),
	}
}

// Get returns a ConversationContent entity by its id.
func (c *ConversationContentClient) Get(ctx context.Context, id int) (*ConversationContent, error) {
	return c.Query().Where(conversationcontent.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ConversationContentClient) GetX(ctx context.Context, id int) *ConversationContent {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryConversation queries the conversation edge of a ConversationContent.
func (c *ConversationContentClient) QueryConversation(cc *ConversationContent) *ConversationQuery {
	query := (&ConversationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := cc.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(conversationcontent.Table, conversationcontent.FieldID, id),
			sqlgraph.To(conversation.Table, conversation.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, conversationcontent.ConversationTable, conversationcontent.ConversationColumn),
		)
		fromV = sqlgraph.Neighbors(cc.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ConversationContentClient) Hooks() []Hook {
	return c.hooks.ConversationContent
}

// Interceptors returns the client interceptors.
func (c *ConversationContentClient) Interceptors() []Interceptor {
	return c.inters.ConversationContent
}

func (c *ConversationContentClient) mutate(ctx context.Context, m *ConversationContentMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ConversationContentCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ConversationContentUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ConversationContentUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ConversationContentDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown ConversationContent mutation op: %q", m.Op())
	}
}

// FrequentAskQuestionClient is a client for the FrequentAskQuestion schema.
type FrequentAskQuestionClient struct {
	config
}

// NewFrequentAskQuestionClient returns a client for the FrequentAskQuestion from the given config.
func NewFrequentAskQuestionClient(c config) *FrequentAskQuestionClient {
	return &FrequentAskQuestionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `frequentaskquestion.Hooks(f(g(h())))`.
func (c *FrequentAskQuestionClient) Use(hooks ...Hook) {
	c.hooks.FrequentAskQuestion = append(c.hooks.FrequentAskQuestion, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `frequentaskquestion.Intercept(f(g(h())))`.
func (c *FrequentAskQuestionClient) Intercept(interceptors ...Interceptor) {
	c.inters.FrequentAskQuestion = append(c.inters.FrequentAskQuestion, interceptors...)
}

// Create returns a builder for creating a FrequentAskQuestion entity.
func (c *FrequentAskQuestionClient) Create() *FrequentAskQuestionCreate {
	mutation := newFrequentAskQuestionMutation(c.config, OpCreate)
	return &FrequentAskQuestionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of FrequentAskQuestion entities.
func (c *FrequentAskQuestionClient) CreateBulk(builders ...*FrequentAskQuestionCreate) *FrequentAskQuestionCreateBulk {
	return &FrequentAskQuestionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FrequentAskQuestionClient) MapCreateBulk(slice any, setFunc func(*FrequentAskQuestionCreate, int)) *FrequentAskQuestionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FrequentAskQuestionCreateBulk{err: fmt.Errorf("calling to FrequentAskQuestionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FrequentAskQuestionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FrequentAskQuestionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for FrequentAskQuestion.
func (c *FrequentAskQuestionClient) Update() *FrequentAskQuestionUpdate {
	mutation := newFrequentAskQuestionMutation(c.config, OpUpdate)
	return &FrequentAskQuestionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FrequentAskQuestionClient) UpdateOne(faq *FrequentAskQuestion) *FrequentAskQuestionUpdateOne {
	mutation := newFrequentAskQuestionMutation(c.config, OpUpdateOne, withFrequentAskQuestion(faq))
	return &FrequentAskQuestionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FrequentAskQuestionClient) UpdateOneID(id int) *FrequentAskQuestionUpdateOne {
	mutation := newFrequentAskQuestionMutation(c.config, OpUpdateOne, withFrequentAskQuestionID(id))
	return &FrequentAskQuestionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for FrequentAskQuestion.
func (c *FrequentAskQuestionClient) Delete() *FrequentAskQuestionDelete {
	mutation := newFrequentAskQuestionMutation(c.config, OpDelete)
	return &FrequentAskQuestionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FrequentAskQuestionClient) DeleteOne(faq *FrequentAskQuestion) *FrequentAskQuestionDeleteOne {
	return c.DeleteOneID(faq.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FrequentAskQuestionClient) DeleteOneID(id int) *FrequentAskQuestionDeleteOne {
	builder := c.Delete().Where(frequentaskquestion.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FrequentAskQuestionDeleteOne{builder}
}

// Query returns a query builder for FrequentAskQuestion.
func (c *FrequentAskQuestionClient) Query() *FrequentAskQuestionQuery {
	return &FrequentAskQuestionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFrequentAskQuestion},
		inters: c.Interceptors(),
	}
}

// Get returns a FrequentAskQuestion entity by its id.
func (c *FrequentAskQuestionClient) Get(ctx context.Context, id int) (*FrequentAskQuestion, error) {
	return c.Query().Where(frequentaskquestion.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FrequentAskQuestionClient) GetX(ctx context.Context, id int) *FrequentAskQuestion {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *FrequentAskQuestionClient) Hooks() []Hook {
	return c.hooks.FrequentAskQuestion
}

// Interceptors returns the client interceptors.
func (c *FrequentAskQuestionClient) Interceptors() []Interceptor {
	return c.inters.FrequentAskQuestion
}

func (c *FrequentAskQuestionClient) mutate(ctx context.Context, m *FrequentAskQuestionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FrequentAskQuestionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FrequentAskQuestionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FrequentAskQuestionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FrequentAskQuestionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown FrequentAskQuestion mutation op: %q", m.Op())
	}
}

// InternalMessageClient is a client for the InternalMessage schema.
type InternalMessageClient struct {
	config
}

// NewInternalMessageClient returns a client for the InternalMessage from the given config.
func NewInternalMessageClient(c config) *InternalMessageClient {
	return &InternalMessageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `internalmessage.Hooks(f(g(h())))`.
func (c *InternalMessageClient) Use(hooks ...Hook) {
	c.hooks.InternalMessage = append(c.hooks.InternalMessage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `internalmessage.Intercept(f(g(h())))`.
func (c *InternalMessageClient) Intercept(interceptors ...Interceptor) {
	c.inters.InternalMessage = append(c.inters.InternalMessage, interceptors...)
}

// Create returns a builder for creating a InternalMessage entity.
func (c *InternalMessageClient) Create() *InternalMessageCreate {
	mutation := newInternalMessageMutation(c.config, OpCreate)
	return &InternalMessageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of InternalMessage entities.
func (c *InternalMessageClient) CreateBulk(builders ...*InternalMessageCreate) *InternalMessageCreateBulk {
	return &InternalMessageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *InternalMessageClient) MapCreateBulk(slice any, setFunc func(*InternalMessageCreate, int)) *InternalMessageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &InternalMessageCreateBulk{err: fmt.Errorf("calling to InternalMessageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*InternalMessageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &InternalMessageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for InternalMessage.
func (c *InternalMessageClient) Update() *InternalMessageUpdate {
	mutation := newInternalMessageMutation(c.config, OpUpdate)
	return &InternalMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *InternalMessageClient) UpdateOne(im *InternalMessage) *InternalMessageUpdateOne {
	mutation := newInternalMessageMutation(c.config, OpUpdateOne, withInternalMessage(im))
	return &InternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *InternalMessageClient) UpdateOneID(id int) *InternalMessageUpdateOne {
	mutation := newInternalMessageMutation(c.config, OpUpdateOne, withInternalMessageID(id))
	return &InternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for InternalMessage.
func (c *InternalMessageClient) Delete() *InternalMessageDelete {
	mutation := newInternalMessageMutation(c.config, OpDelete)
	return &InternalMessageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *InternalMessageClient) DeleteOne(im *InternalMessage) *InternalMessageDeleteOne {
	return c.DeleteOneID(im.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *InternalMessageClient) DeleteOneID(id int) *InternalMessageDeleteOne {
	builder := c.Delete().Where(internalmessage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &InternalMessageDeleteOne{builder}
}

// Query returns a query builder for InternalMessage.
func (c *InternalMessageClient) Query() *InternalMessageQuery {
	return &InternalMessageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeInternalMessage},
		inters: c.Interceptors(),
	}
}

// Get returns a InternalMessage entity by its id.
func (c *InternalMessageClient) Get(ctx context.Context, id int) (*InternalMessage, error) {
	return c.Query().Where(internalmessage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *InternalMessageClient) GetX(ctx context.Context, id int) *InternalMessage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUsers queries the users edge of a InternalMessage.
func (c *InternalMessageClient) QueryUsers(im *InternalMessage) *UserInternalMessageQuery {
	query := (&UserInternalMessageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := im.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(internalmessage.Table, internalmessage.FieldID, id),
			sqlgraph.To(userinternalmessage.Table, userinternalmessage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, internalmessage.UsersTable, internalmessage.UsersColumn),
		)
		fromV = sqlgraph.Neighbors(im.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *InternalMessageClient) Hooks() []Hook {
	return c.hooks.InternalMessage
}

// Interceptors returns the client interceptors.
func (c *InternalMessageClient) Interceptors() []Interceptor {
	return c.inters.InternalMessage
}

func (c *InternalMessageClient) mutate(ctx context.Context, m *InternalMessageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&InternalMessageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&InternalMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&InternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&InternalMessageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown InternalMessage mutation op: %q", m.Op())
	}
}

// ProjectClient is a client for the Project schema.
type ProjectClient struct {
	config
}

// NewProjectClient returns a client for the Project from the given config.
func NewProjectClient(c config) *ProjectClient {
	return &ProjectClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `project.Hooks(f(g(h())))`.
func (c *ProjectClient) Use(hooks ...Hook) {
	c.hooks.Project = append(c.hooks.Project, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `project.Intercept(f(g(h())))`.
func (c *ProjectClient) Intercept(interceptors ...Interceptor) {
	c.inters.Project = append(c.inters.Project, interceptors...)
}

// Create returns a builder for creating a Project entity.
func (c *ProjectClient) Create() *ProjectCreate {
	mutation := newProjectMutation(c.config, OpCreate)
	return &ProjectCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Project entities.
func (c *ProjectClient) CreateBulk(builders ...*ProjectCreate) *ProjectCreateBulk {
	return &ProjectCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ProjectClient) MapCreateBulk(slice any, setFunc func(*ProjectCreate, int)) *ProjectCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ProjectCreateBulk{err: fmt.Errorf("calling to ProjectClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ProjectCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ProjectCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Project.
func (c *ProjectClient) Update() *ProjectUpdate {
	mutation := newProjectMutation(c.config, OpUpdate)
	return &ProjectUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ProjectClient) UpdateOne(pr *Project) *ProjectUpdateOne {
	mutation := newProjectMutation(c.config, OpUpdateOne, withProject(pr))
	return &ProjectUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ProjectClient) UpdateOneID(id int) *ProjectUpdateOne {
	mutation := newProjectMutation(c.config, OpUpdateOne, withProjectID(id))
	return &ProjectUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Project.
func (c *ProjectClient) Delete() *ProjectDelete {
	mutation := newProjectMutation(c.config, OpDelete)
	return &ProjectDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ProjectClient) DeleteOne(pr *Project) *ProjectDeleteOne {
	return c.DeleteOneID(pr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ProjectClient) DeleteOneID(id int) *ProjectDeleteOne {
	builder := c.Delete().Where(project.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ProjectDeleteOne{builder}
}

// Query returns a query builder for Project.
func (c *ProjectClient) Query() *ProjectQuery {
	return &ProjectQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeProject},
		inters: c.Interceptors(),
	}
}

// Get returns a Project entity by its id.
func (c *ProjectClient) Get(ctx context.Context, id int) (*Project, error) {
	return c.Query().Where(project.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ProjectClient) GetX(ctx context.Context, id int) *Project {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ProjectClient) Hooks() []Hook {
	return c.hooks.Project
}

// Interceptors returns the client interceptors.
func (c *ProjectClient) Interceptors() []Interceptor {
	return c.inters.Project
}

func (c *ProjectClient) mutate(ctx context.Context, m *ProjectMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ProjectCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ProjectUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ProjectUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ProjectDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Project mutation op: %q", m.Op())
	}
}

// ScreenplayClient is a client for the Screenplay schema.
type ScreenplayClient struct {
	config
}

// NewScreenplayClient returns a client for the Screenplay from the given config.
func NewScreenplayClient(c config) *ScreenplayClient {
	return &ScreenplayClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `screenplay.Hooks(f(g(h())))`.
func (c *ScreenplayClient) Use(hooks ...Hook) {
	c.hooks.Screenplay = append(c.hooks.Screenplay, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `screenplay.Intercept(f(g(h())))`.
func (c *ScreenplayClient) Intercept(interceptors ...Interceptor) {
	c.inters.Screenplay = append(c.inters.Screenplay, interceptors...)
}

// Create returns a builder for creating a Screenplay entity.
func (c *ScreenplayClient) Create() *ScreenplayCreate {
	mutation := newScreenplayMutation(c.config, OpCreate)
	return &ScreenplayCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Screenplay entities.
func (c *ScreenplayClient) CreateBulk(builders ...*ScreenplayCreate) *ScreenplayCreateBulk {
	return &ScreenplayCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ScreenplayClient) MapCreateBulk(slice any, setFunc func(*ScreenplayCreate, int)) *ScreenplayCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ScreenplayCreateBulk{err: fmt.Errorf("calling to ScreenplayClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ScreenplayCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ScreenplayCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Screenplay.
func (c *ScreenplayClient) Update() *ScreenplayUpdate {
	mutation := newScreenplayMutation(c.config, OpUpdate)
	return &ScreenplayUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ScreenplayClient) UpdateOne(s *Screenplay) *ScreenplayUpdateOne {
	mutation := newScreenplayMutation(c.config, OpUpdateOne, withScreenplay(s))
	return &ScreenplayUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ScreenplayClient) UpdateOneID(id int) *ScreenplayUpdateOne {
	mutation := newScreenplayMutation(c.config, OpUpdateOne, withScreenplayID(id))
	return &ScreenplayUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Screenplay.
func (c *ScreenplayClient) Delete() *ScreenplayDelete {
	mutation := newScreenplayMutation(c.config, OpDelete)
	return &ScreenplayDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ScreenplayClient) DeleteOne(s *Screenplay) *ScreenplayDeleteOne {
	return c.DeleteOneID(s.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ScreenplayClient) DeleteOneID(id int) *ScreenplayDeleteOne {
	builder := c.Delete().Where(screenplay.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ScreenplayDeleteOne{builder}
}

// Query returns a query builder for Screenplay.
func (c *ScreenplayClient) Query() *ScreenplayQuery {
	return &ScreenplayQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeScreenplay},
		inters: c.Interceptors(),
	}
}

// Get returns a Screenplay entity by its id.
func (c *ScreenplayClient) Get(ctx context.Context, id int) (*Screenplay, error) {
	return c.Query().Where(screenplay.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ScreenplayClient) GetX(ctx context.Context, id int) *Screenplay {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *ScreenplayClient) Hooks() []Hook {
	return c.hooks.Screenplay
}

// Interceptors returns the client interceptors.
func (c *ScreenplayClient) Interceptors() []Interceptor {
	return c.inters.Screenplay
}

func (c *ScreenplayClient) mutate(ctx context.Context, m *ScreenplayMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ScreenplayCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ScreenplayUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ScreenplayUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ScreenplayDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Screenplay mutation op: %q", m.Op())
	}
}

// TestCodeClient is a client for the TestCode schema.
type TestCodeClient struct {
	config
}

// NewTestCodeClient returns a client for the TestCode from the given config.
func NewTestCodeClient(c config) *TestCodeClient {
	return &TestCodeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `testcode.Hooks(f(g(h())))`.
func (c *TestCodeClient) Use(hooks ...Hook) {
	c.hooks.TestCode = append(c.hooks.TestCode, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `testcode.Intercept(f(g(h())))`.
func (c *TestCodeClient) Intercept(interceptors ...Interceptor) {
	c.inters.TestCode = append(c.inters.TestCode, interceptors...)
}

// Create returns a builder for creating a TestCode entity.
func (c *TestCodeClient) Create() *TestCodeCreate {
	mutation := newTestCodeMutation(c.config, OpCreate)
	return &TestCodeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TestCode entities.
func (c *TestCodeClient) CreateBulk(builders ...*TestCodeCreate) *TestCodeCreateBulk {
	return &TestCodeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TestCodeClient) MapCreateBulk(slice any, setFunc func(*TestCodeCreate, int)) *TestCodeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TestCodeCreateBulk{err: fmt.Errorf("calling to TestCodeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TestCodeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TestCodeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TestCode.
func (c *TestCodeClient) Update() *TestCodeUpdate {
	mutation := newTestCodeMutation(c.config, OpUpdate)
	return &TestCodeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TestCodeClient) UpdateOne(tc *TestCode) *TestCodeUpdateOne {
	mutation := newTestCodeMutation(c.config, OpUpdateOne, withTestCode(tc))
	return &TestCodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TestCodeClient) UpdateOneID(id int) *TestCodeUpdateOne {
	mutation := newTestCodeMutation(c.config, OpUpdateOne, withTestCodeID(id))
	return &TestCodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TestCode.
func (c *TestCodeClient) Delete() *TestCodeDelete {
	mutation := newTestCodeMutation(c.config, OpDelete)
	return &TestCodeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TestCodeClient) DeleteOne(tc *TestCode) *TestCodeDeleteOne {
	return c.DeleteOneID(tc.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TestCodeClient) DeleteOneID(id int) *TestCodeDeleteOne {
	builder := c.Delete().Where(testcode.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TestCodeDeleteOne{builder}
}

// Query returns a query builder for TestCode.
func (c *TestCodeClient) Query() *TestCodeQuery {
	return &TestCodeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTestCode},
		inters: c.Interceptors(),
	}
}

// Get returns a TestCode entity by its id.
func (c *TestCodeClient) Get(ctx context.Context, id int) (*TestCode, error) {
	return c.Query().Where(testcode.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TestCodeClient) GetX(ctx context.Context, id int) *TestCode {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUser queries the user edge of a TestCode.
func (c *TestCodeClient) QueryUser(tc *TestCode) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tc.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(testcode.Table, testcode.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, testcode.UserTable, testcode.UserColumn),
		)
		fromV = sqlgraph.Neighbors(tc.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBatch queries the batch edge of a TestCode.
func (c *TestCodeClient) QueryBatch(tc *TestCode) *BatchQuery {
	query := (&BatchClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tc.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(testcode.Table, testcode.FieldID, id),
			sqlgraph.To(batch.Table, batch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, testcode.BatchTable, testcode.BatchColumn),
		)
		fromV = sqlgraph.Neighbors(tc.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TestCodeClient) Hooks() []Hook {
	return c.hooks.TestCode
}

// Interceptors returns the client interceptors.
func (c *TestCodeClient) Interceptors() []Interceptor {
	return c.inters.TestCode
}

func (c *TestCodeClient) mutate(ctx context.Context, m *TestCodeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TestCodeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TestCodeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TestCodeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TestCodeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TestCode mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id int) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id int) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id int) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id int) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryCode queries the code edge of a User.
func (c *UserClient) QueryCode(u *User) *TestCodeQuery {
	query := (&TestCodeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(testcode.Table, testcode.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, true, user.CodeTable, user.CodeColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryBatch queries the batch edge of a User.
func (c *UserClient) QueryBatch(u *User) *BatchQuery {
	query := (&BatchClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(batch.Table, batch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, user.BatchTable, user.BatchColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMessages queries the messages edge of a User.
func (c *UserClient) QueryMessages(u *User) *UserInternalMessageQuery {
	query := (&UserInternalMessageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(userinternalmessage.Table, userinternalmessage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.MessagesTable, user.MessagesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	return c.hooks.User
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	return c.inters.User
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// UserInternalMessageClient is a client for the UserInternalMessage schema.
type UserInternalMessageClient struct {
	config
}

// NewUserInternalMessageClient returns a client for the UserInternalMessage from the given config.
func NewUserInternalMessageClient(c config) *UserInternalMessageClient {
	return &UserInternalMessageClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `userinternalmessage.Hooks(f(g(h())))`.
func (c *UserInternalMessageClient) Use(hooks ...Hook) {
	c.hooks.UserInternalMessage = append(c.hooks.UserInternalMessage, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `userinternalmessage.Intercept(f(g(h())))`.
func (c *UserInternalMessageClient) Intercept(interceptors ...Interceptor) {
	c.inters.UserInternalMessage = append(c.inters.UserInternalMessage, interceptors...)
}

// Create returns a builder for creating a UserInternalMessage entity.
func (c *UserInternalMessageClient) Create() *UserInternalMessageCreate {
	mutation := newUserInternalMessageMutation(c.config, OpCreate)
	return &UserInternalMessageCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of UserInternalMessage entities.
func (c *UserInternalMessageClient) CreateBulk(builders ...*UserInternalMessageCreate) *UserInternalMessageCreateBulk {
	return &UserInternalMessageCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserInternalMessageClient) MapCreateBulk(slice any, setFunc func(*UserInternalMessageCreate, int)) *UserInternalMessageCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserInternalMessageCreateBulk{err: fmt.Errorf("calling to UserInternalMessageClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserInternalMessageCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserInternalMessageCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for UserInternalMessage.
func (c *UserInternalMessageClient) Update() *UserInternalMessageUpdate {
	mutation := newUserInternalMessageMutation(c.config, OpUpdate)
	return &UserInternalMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserInternalMessageClient) UpdateOne(uim *UserInternalMessage) *UserInternalMessageUpdateOne {
	mutation := newUserInternalMessageMutation(c.config, OpUpdateOne, withUserInternalMessage(uim))
	return &UserInternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserInternalMessageClient) UpdateOneID(id int) *UserInternalMessageUpdateOne {
	mutation := newUserInternalMessageMutation(c.config, OpUpdateOne, withUserInternalMessageID(id))
	return &UserInternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for UserInternalMessage.
func (c *UserInternalMessageClient) Delete() *UserInternalMessageDelete {
	mutation := newUserInternalMessageMutation(c.config, OpDelete)
	return &UserInternalMessageDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserInternalMessageClient) DeleteOne(uim *UserInternalMessage) *UserInternalMessageDeleteOne {
	return c.DeleteOneID(uim.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserInternalMessageClient) DeleteOneID(id int) *UserInternalMessageDeleteOne {
	builder := c.Delete().Where(userinternalmessage.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserInternalMessageDeleteOne{builder}
}

// Query returns a query builder for UserInternalMessage.
func (c *UserInternalMessageClient) Query() *UserInternalMessageQuery {
	return &UserInternalMessageQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUserInternalMessage},
		inters: c.Interceptors(),
	}
}

// Get returns a UserInternalMessage entity by its id.
func (c *UserInternalMessageClient) Get(ctx context.Context, id int) (*UserInternalMessage, error) {
	return c.Query().Where(userinternalmessage.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserInternalMessageClient) GetX(ctx context.Context, id int) *UserInternalMessage {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryMessage queries the message edge of a UserInternalMessage.
func (c *UserInternalMessageClient) QueryMessage(uim *UserInternalMessage) *InternalMessageQuery {
	query := (&InternalMessageClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := uim.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(userinternalmessage.Table, userinternalmessage.FieldID, id),
			sqlgraph.To(internalmessage.Table, internalmessage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, userinternalmessage.MessageTable, userinternalmessage.MessageColumn),
		)
		fromV = sqlgraph.Neighbors(uim.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a UserInternalMessage.
func (c *UserInternalMessageClient) QueryUser(uim *UserInternalMessage) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := uim.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(userinternalmessage.Table, userinternalmessage.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, userinternalmessage.UserTable, userinternalmessage.UserColumn),
		)
		fromV = sqlgraph.Neighbors(uim.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserInternalMessageClient) Hooks() []Hook {
	return c.hooks.UserInternalMessage
}

// Interceptors returns the client interceptors.
func (c *UserInternalMessageClient) Interceptors() []Interceptor {
	return c.inters.UserInternalMessage
}

func (c *UserInternalMessageClient) mutate(ctx context.Context, m *UserInternalMessageMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserInternalMessageCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserInternalMessageUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserInternalMessageUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserInternalMessageDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown UserInternalMessage mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		AdminUser, AiAgent, Batch, Conversation, ConversationContent,
		FrequentAskQuestion, InternalMessage, Project, Screenplay, TestCode, User,
		UserInternalMessage []ent.Hook
	}
	inters struct {
		AdminUser, AiAgent, Batch, Conversation, ConversationContent,
		FrequentAskQuestion, InternalMessage, Project, Screenplay, TestCode, User,
		UserInternalMessage []ent.Interceptor
	}
)
