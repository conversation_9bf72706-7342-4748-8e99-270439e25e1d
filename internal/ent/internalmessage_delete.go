// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InternalMessageDelete is the builder for deleting a InternalMessage entity.
type InternalMessageDelete struct {
	config
	hooks    []Hook
	mutation *InternalMessageMutation
}

// Where appends a list predicates to the InternalMessageDelete builder.
func (imd *InternalMessageDelete) Where(ps ...predicate.InternalMessage) *InternalMessageDelete {
	imd.mutation.Where(ps...)
	return imd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (imd *InternalMessageDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, imd.sqlExec, imd.mutation, imd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (imd *InternalMessageDelete) ExecX(ctx context.Context) int {
	n, err := imd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (imd *InternalMessageDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(internalmessage.Table, sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt))
	if ps := imd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, imd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	imd.mutation.done = true
	return affected, err
}

// InternalMessageDeleteOne is the builder for deleting a single InternalMessage entity.
type InternalMessageDeleteOne struct {
	imd *InternalMessageDelete
}

// Where appends a list predicates to the InternalMessageDelete builder.
func (imdo *InternalMessageDeleteOne) Where(ps ...predicate.InternalMessage) *InternalMessageDeleteOne {
	imdo.imd.mutation.Where(ps...)
	return imdo
}

// Exec executes the deletion query.
func (imdo *InternalMessageDeleteOne) Exec(ctx context.Context) error {
	n, err := imdo.imd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{internalmessage.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (imdo *InternalMessageDeleteOne) ExecX(ctx context.Context) {
	if err := imdo.Exec(ctx); err != nil {
		panic(err)
	}
}
