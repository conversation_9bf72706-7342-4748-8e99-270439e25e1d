// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InternalMessageQuery is the builder for querying InternalMessage entities.
type InternalMessageQuery struct {
	config
	ctx        *QueryContext
	order      []internalmessage.OrderOption
	inters     []Interceptor
	predicates []predicate.InternalMessage
	withUsers  *UserInternalMessageQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the InternalMessageQuery builder.
func (imq *InternalMessageQuery) Where(ps ...predicate.InternalMessage) *InternalMessageQuery {
	imq.predicates = append(imq.predicates, ps...)
	return imq
}

// Limit the number of records to be returned by this query.
func (imq *InternalMessageQuery) Limit(limit int) *InternalMessageQuery {
	imq.ctx.Limit = &limit
	return imq
}

// Offset to start from.
func (imq *InternalMessageQuery) Offset(offset int) *InternalMessageQuery {
	imq.ctx.Offset = &offset
	return imq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (imq *InternalMessageQuery) Unique(unique bool) *InternalMessageQuery {
	imq.ctx.Unique = &unique
	return imq
}

// Order specifies how the records should be ordered.
func (imq *InternalMessageQuery) Order(o ...internalmessage.OrderOption) *InternalMessageQuery {
	imq.order = append(imq.order, o...)
	return imq
}

// QueryUsers chains the current query on the "users" edge.
func (imq *InternalMessageQuery) QueryUsers() *UserInternalMessageQuery {
	query := (&UserInternalMessageClient{config: imq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := imq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := imq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(internalmessage.Table, internalmessage.FieldID, selector),
			sqlgraph.To(userinternalmessage.Table, userinternalmessage.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, internalmessage.UsersTable, internalmessage.UsersColumn),
		)
		fromU = sqlgraph.SetNeighbors(imq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first InternalMessage entity from the query.
// Returns a *NotFoundError when no InternalMessage was found.
func (imq *InternalMessageQuery) First(ctx context.Context) (*InternalMessage, error) {
	nodes, err := imq.Limit(1).All(setContextOp(ctx, imq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{internalmessage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (imq *InternalMessageQuery) FirstX(ctx context.Context) *InternalMessage {
	node, err := imq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first InternalMessage ID from the query.
// Returns a *NotFoundError when no InternalMessage ID was found.
func (imq *InternalMessageQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = imq.Limit(1).IDs(setContextOp(ctx, imq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{internalmessage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (imq *InternalMessageQuery) FirstIDX(ctx context.Context) int {
	id, err := imq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single InternalMessage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one InternalMessage entity is found.
// Returns a *NotFoundError when no InternalMessage entities are found.
func (imq *InternalMessageQuery) Only(ctx context.Context) (*InternalMessage, error) {
	nodes, err := imq.Limit(2).All(setContextOp(ctx, imq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{internalmessage.Label}
	default:
		return nil, &NotSingularError{internalmessage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (imq *InternalMessageQuery) OnlyX(ctx context.Context) *InternalMessage {
	node, err := imq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only InternalMessage ID in the query.
// Returns a *NotSingularError when more than one InternalMessage ID is found.
// Returns a *NotFoundError when no entities are found.
func (imq *InternalMessageQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = imq.Limit(2).IDs(setContextOp(ctx, imq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{internalmessage.Label}
	default:
		err = &NotSingularError{internalmessage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (imq *InternalMessageQuery) OnlyIDX(ctx context.Context) int {
	id, err := imq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of InternalMessages.
func (imq *InternalMessageQuery) All(ctx context.Context) ([]*InternalMessage, error) {
	ctx = setContextOp(ctx, imq.ctx, ent.OpQueryAll)
	if err := imq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*InternalMessage, *InternalMessageQuery]()
	return withInterceptors[[]*InternalMessage](ctx, imq, qr, imq.inters)
}

// AllX is like All, but panics if an error occurs.
func (imq *InternalMessageQuery) AllX(ctx context.Context) []*InternalMessage {
	nodes, err := imq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of InternalMessage IDs.
func (imq *InternalMessageQuery) IDs(ctx context.Context) (ids []int, err error) {
	if imq.ctx.Unique == nil && imq.path != nil {
		imq.Unique(true)
	}
	ctx = setContextOp(ctx, imq.ctx, ent.OpQueryIDs)
	if err = imq.Select(internalmessage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (imq *InternalMessageQuery) IDsX(ctx context.Context) []int {
	ids, err := imq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (imq *InternalMessageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, imq.ctx, ent.OpQueryCount)
	if err := imq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, imq, querierCount[*InternalMessageQuery](), imq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (imq *InternalMessageQuery) CountX(ctx context.Context) int {
	count, err := imq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (imq *InternalMessageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, imq.ctx, ent.OpQueryExist)
	switch _, err := imq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (imq *InternalMessageQuery) ExistX(ctx context.Context) bool {
	exist, err := imq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the InternalMessageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (imq *InternalMessageQuery) Clone() *InternalMessageQuery {
	if imq == nil {
		return nil
	}
	return &InternalMessageQuery{
		config:     imq.config,
		ctx:        imq.ctx.Clone(),
		order:      append([]internalmessage.OrderOption{}, imq.order...),
		inters:     append([]Interceptor{}, imq.inters...),
		predicates: append([]predicate.InternalMessage{}, imq.predicates...),
		withUsers:  imq.withUsers.Clone(),
		// clone intermediate query.
		sql:  imq.sql.Clone(),
		path: imq.path,
	}
}

// WithUsers tells the query-builder to eager-load the nodes that are connected to
// the "users" edge. The optional arguments are used to configure the query builder of the edge.
func (imq *InternalMessageQuery) WithUsers(opts ...func(*UserInternalMessageQuery)) *InternalMessageQuery {
	query := (&UserInternalMessageClient{config: imq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	imq.withUsers = query
	return imq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.InternalMessage.Query().
//		GroupBy(internalmessage.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (imq *InternalMessageQuery) GroupBy(field string, fields ...string) *InternalMessageGroupBy {
	imq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &InternalMessageGroupBy{build: imq}
	grbuild.flds = &imq.ctx.Fields
	grbuild.label = internalmessage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.InternalMessage.Query().
//		Select(internalmessage.FieldCreatedAt).
//		Scan(ctx, &v)
func (imq *InternalMessageQuery) Select(fields ...string) *InternalMessageSelect {
	imq.ctx.Fields = append(imq.ctx.Fields, fields...)
	sbuild := &InternalMessageSelect{InternalMessageQuery: imq}
	sbuild.label = internalmessage.Label
	sbuild.flds, sbuild.scan = &imq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a InternalMessageSelect configured with the given aggregations.
func (imq *InternalMessageQuery) Aggregate(fns ...AggregateFunc) *InternalMessageSelect {
	return imq.Select().Aggregate(fns...)
}

func (imq *InternalMessageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range imq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, imq); err != nil {
				return err
			}
		}
	}
	for _, f := range imq.ctx.Fields {
		if !internalmessage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if imq.path != nil {
		prev, err := imq.path(ctx)
		if err != nil {
			return err
		}
		imq.sql = prev
	}
	return nil
}

func (imq *InternalMessageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*InternalMessage, error) {
	var (
		nodes       = []*InternalMessage{}
		_spec       = imq.querySpec()
		loadedTypes = [1]bool{
			imq.withUsers != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*InternalMessage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &InternalMessage{config: imq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, imq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := imq.withUsers; query != nil {
		if err := imq.loadUsers(ctx, query, nodes,
			func(n *InternalMessage) { n.Edges.Users = []*UserInternalMessage{} },
			func(n *InternalMessage, e *UserInternalMessage) { n.Edges.Users = append(n.Edges.Users, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (imq *InternalMessageQuery) loadUsers(ctx context.Context, query *UserInternalMessageQuery, nodes []*InternalMessage, init func(*InternalMessage), assign func(*InternalMessage, *UserInternalMessage)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*InternalMessage)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(userinternalmessage.FieldInternalMessageID)
	}
	query.Where(predicate.UserInternalMessage(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(internalmessage.UsersColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.InternalMessageID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "internal_message_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (imq *InternalMessageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := imq.querySpec()
	_spec.Node.Columns = imq.ctx.Fields
	if len(imq.ctx.Fields) > 0 {
		_spec.Unique = imq.ctx.Unique != nil && *imq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, imq.driver, _spec)
}

func (imq *InternalMessageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(internalmessage.Table, internalmessage.Columns, sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt))
	_spec.From = imq.sql
	if unique := imq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if imq.path != nil {
		_spec.Unique = true
	}
	if fields := imq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, internalmessage.FieldID)
		for i := range fields {
			if fields[i] != internalmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := imq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := imq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := imq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := imq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (imq *InternalMessageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(imq.driver.Dialect())
	t1 := builder.Table(internalmessage.Table)
	columns := imq.ctx.Fields
	if len(columns) == 0 {
		columns = internalmessage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if imq.sql != nil {
		selector = imq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if imq.ctx.Unique != nil && *imq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range imq.predicates {
		p(selector)
	}
	for _, p := range imq.order {
		p(selector)
	}
	if offset := imq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := imq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// InternalMessageGroupBy is the group-by builder for InternalMessage entities.
type InternalMessageGroupBy struct {
	selector
	build *InternalMessageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (imgb *InternalMessageGroupBy) Aggregate(fns ...AggregateFunc) *InternalMessageGroupBy {
	imgb.fns = append(imgb.fns, fns...)
	return imgb
}

// Scan applies the selector query and scans the result into the given value.
func (imgb *InternalMessageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, imgb.build.ctx, ent.OpQueryGroupBy)
	if err := imgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*InternalMessageQuery, *InternalMessageGroupBy](ctx, imgb.build, imgb, imgb.build.inters, v)
}

func (imgb *InternalMessageGroupBy) sqlScan(ctx context.Context, root *InternalMessageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(imgb.fns))
	for _, fn := range imgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*imgb.flds)+len(imgb.fns))
		for _, f := range *imgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*imgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := imgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// InternalMessageSelect is the builder for selecting fields of InternalMessage entities.
type InternalMessageSelect struct {
	*InternalMessageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ims *InternalMessageSelect) Aggregate(fns ...AggregateFunc) *InternalMessageSelect {
	ims.fns = append(ims.fns, fns...)
	return ims
}

// Scan applies the selector query and scans the result into the given value.
func (ims *InternalMessageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ims.ctx, ent.OpQuerySelect)
	if err := ims.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*InternalMessageQuery, *InternalMessageSelect](ctx, ims.InternalMessageQuery, ims, ims.inters, v)
}

func (ims *InternalMessageSelect) sqlScan(ctx context.Context, root *InternalMessageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ims.fns))
	for _, fn := range ims.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ims.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ims.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
