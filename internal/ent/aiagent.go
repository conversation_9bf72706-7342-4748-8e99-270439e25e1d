// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/schema"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// AI智能体表
type AiAgent struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 名称
	Name string `json:"name,omitempty"`
	// 图标
	Icon string `json:"icon,omitempty"`
	// 描述
	Description string `json:"description,omitempty"`
	// target
	Target string `json:"target,omitempty"`
	// 引导
	Guide string `json:"guide,omitempty"`
	// secret
	Secret string `json:"secret,omitempty"`
	// http method
	Method string `json:"method,omitempty"`
	// inputs参数
	Inputs []schema.AiAgentParam `json:"inputs,omitempty"`
	// 状态:1默认；2下线
	Status int8 `json:"status,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the AiAgentQuery when eager-loading is set.
	Edges        AiAgentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// AiAgentEdges holds the relations/edges for other nodes in the graph.
type AiAgentEdges struct {
	// Conversations holds the value of the conversations edge.
	Conversations []*Conversation `json:"conversations,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// ConversationsOrErr returns the Conversations value or an error if the edge
// was not loaded in eager-loading.
func (e AiAgentEdges) ConversationsOrErr() ([]*Conversation, error) {
	if e.loadedTypes[0] {
		return e.Conversations, nil
	}
	return nil, &NotLoadedError{edge: "conversations"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*AiAgent) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case aiagent.FieldInputs:
			values[i] = new([]byte)
		case aiagent.FieldID, aiagent.FieldStatus:
			values[i] = new(sql.NullInt64)
		case aiagent.FieldName, aiagent.FieldIcon, aiagent.FieldDescription, aiagent.FieldTarget, aiagent.FieldGuide, aiagent.FieldSecret, aiagent.FieldMethod:
			values[i] = new(sql.NullString)
		case aiagent.FieldCreatedAt, aiagent.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the AiAgent fields.
func (aa *AiAgent) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case aiagent.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			aa.ID = int(value.Int64)
		case aiagent.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				aa.CreatedAt = value.Time
			}
		case aiagent.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				aa.UpdatedAt = value.Time
			}
		case aiagent.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				aa.Name = value.String
			}
		case aiagent.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				aa.Icon = value.String
			}
		case aiagent.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				aa.Description = value.String
			}
		case aiagent.FieldTarget:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field target", values[i])
			} else if value.Valid {
				aa.Target = value.String
			}
		case aiagent.FieldGuide:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field guide", values[i])
			} else if value.Valid {
				aa.Guide = value.String
			}
		case aiagent.FieldSecret:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field secret", values[i])
			} else if value.Valid {
				aa.Secret = value.String
			}
		case aiagent.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				aa.Method = value.String
			}
		case aiagent.FieldInputs:
			if value, ok := values[i].(*[]byte); !ok {
				return fmt.Errorf("unexpected type %T for field inputs", values[i])
			} else if value != nil && len(*value) > 0 {
				if err := json.Unmarshal(*value, &aa.Inputs); err != nil {
					return fmt.Errorf("unmarshal field inputs: %w", err)
				}
			}
		case aiagent.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				aa.Status = int8(value.Int64)
			}
		default:
			aa.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the AiAgent.
// This includes values selected through modifiers, order, etc.
func (aa *AiAgent) Value(name string) (ent.Value, error) {
	return aa.selectValues.Get(name)
}

// QueryConversations queries the "conversations" edge of the AiAgent entity.
func (aa *AiAgent) QueryConversations() *ConversationQuery {
	return NewAiAgentClient(aa.config).QueryConversations(aa)
}

// Update returns a builder for updating this AiAgent.
// Note that you need to call AiAgent.Unwrap() before calling this method if this AiAgent
// was returned from a transaction, and the transaction was committed or rolled back.
func (aa *AiAgent) Update() *AiAgentUpdateOne {
	return NewAiAgentClient(aa.config).UpdateOne(aa)
}

// Unwrap unwraps the AiAgent entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (aa *AiAgent) Unwrap() *AiAgent {
	_tx, ok := aa.config.driver.(*txDriver)
	if !ok {
		panic("ent: AiAgent is not a transactional entity")
	}
	aa.config.driver = _tx.drv
	return aa
}

// String implements the fmt.Stringer.
func (aa *AiAgent) String() string {
	var builder strings.Builder
	builder.WriteString("AiAgent(")
	builder.WriteString(fmt.Sprintf("id=%v, ", aa.ID))
	builder.WriteString("created_at=")
	builder.WriteString(aa.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(aa.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(aa.Name)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(aa.Icon)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(aa.Description)
	builder.WriteString(", ")
	builder.WriteString("target=")
	builder.WriteString(aa.Target)
	builder.WriteString(", ")
	builder.WriteString("guide=")
	builder.WriteString(aa.Guide)
	builder.WriteString(", ")
	builder.WriteString("secret=")
	builder.WriteString(aa.Secret)
	builder.WriteString(", ")
	builder.WriteString("method=")
	builder.WriteString(aa.Method)
	builder.WriteString(", ")
	builder.WriteString("inputs=")
	builder.WriteString(fmt.Sprintf("%v", aa.Inputs))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", aa.Status))
	builder.WriteByte(')')
	return builder.String()
}

// AiAgents is a parsable slice of AiAgent.
type AiAgents []*AiAgent
