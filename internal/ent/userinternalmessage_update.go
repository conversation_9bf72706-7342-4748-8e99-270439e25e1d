// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserInternalMessageUpdate is the builder for updating UserInternalMessage entities.
type UserInternalMessageUpdate struct {
	config
	hooks    []Hook
	mutation *UserInternalMessageMutation
}

// Where appends a list predicates to the UserInternalMessageUpdate builder.
func (uimu *UserInternalMessageUpdate) Where(ps ...predicate.UserInternalMessage) *UserInternalMessageUpdate {
	uimu.mutation.Where(ps...)
	return uimu
}

// SetUpdatedAt sets the "updated_at" field.
func (uimu *UserInternalMessageUpdate) SetUpdatedAt(t time.Time) *UserInternalMessageUpdate {
	uimu.mutation.SetUpdatedAt(t)
	return uimu
}

// SetInternalMessageID sets the "internal_message_id" field.
func (uimu *UserInternalMessageUpdate) SetInternalMessageID(i int) *UserInternalMessageUpdate {
	uimu.mutation.SetInternalMessageID(i)
	return uimu
}

// SetNillableInternalMessageID sets the "internal_message_id" field if the given value is not nil.
func (uimu *UserInternalMessageUpdate) SetNillableInternalMessageID(i *int) *UserInternalMessageUpdate {
	if i != nil {
		uimu.SetInternalMessageID(*i)
	}
	return uimu
}

// SetUserID sets the "user_id" field.
func (uimu *UserInternalMessageUpdate) SetUserID(i int) *UserInternalMessageUpdate {
	uimu.mutation.SetUserID(i)
	return uimu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (uimu *UserInternalMessageUpdate) SetNillableUserID(i *int) *UserInternalMessageUpdate {
	if i != nil {
		uimu.SetUserID(*i)
	}
	return uimu
}

// SetStatus sets the "status" field.
func (uimu *UserInternalMessageUpdate) SetStatus(i int8) *UserInternalMessageUpdate {
	uimu.mutation.ResetStatus()
	uimu.mutation.SetStatus(i)
	return uimu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uimu *UserInternalMessageUpdate) SetNillableStatus(i *int8) *UserInternalMessageUpdate {
	if i != nil {
		uimu.SetStatus(*i)
	}
	return uimu
}

// AddStatus adds i to the "status" field.
func (uimu *UserInternalMessageUpdate) AddStatus(i int8) *UserInternalMessageUpdate {
	uimu.mutation.AddStatus(i)
	return uimu
}

// SetMessageID sets the "message" edge to the InternalMessage entity by ID.
func (uimu *UserInternalMessageUpdate) SetMessageID(id int) *UserInternalMessageUpdate {
	uimu.mutation.SetMessageID(id)
	return uimu
}

// SetMessage sets the "message" edge to the InternalMessage entity.
func (uimu *UserInternalMessageUpdate) SetMessage(i *InternalMessage) *UserInternalMessageUpdate {
	return uimu.SetMessageID(i.ID)
}

// SetUser sets the "user" edge to the User entity.
func (uimu *UserInternalMessageUpdate) SetUser(u *User) *UserInternalMessageUpdate {
	return uimu.SetUserID(u.ID)
}

// Mutation returns the UserInternalMessageMutation object of the builder.
func (uimu *UserInternalMessageUpdate) Mutation() *UserInternalMessageMutation {
	return uimu.mutation
}

// ClearMessage clears the "message" edge to the InternalMessage entity.
func (uimu *UserInternalMessageUpdate) ClearMessage() *UserInternalMessageUpdate {
	uimu.mutation.ClearMessage()
	return uimu
}

// ClearUser clears the "user" edge to the User entity.
func (uimu *UserInternalMessageUpdate) ClearUser() *UserInternalMessageUpdate {
	uimu.mutation.ClearUser()
	return uimu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uimu *UserInternalMessageUpdate) Save(ctx context.Context) (int, error) {
	uimu.defaults()
	return withHooks(ctx, uimu.sqlSave, uimu.mutation, uimu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uimu *UserInternalMessageUpdate) SaveX(ctx context.Context) int {
	affected, err := uimu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uimu *UserInternalMessageUpdate) Exec(ctx context.Context) error {
	_, err := uimu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uimu *UserInternalMessageUpdate) ExecX(ctx context.Context) {
	if err := uimu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uimu *UserInternalMessageUpdate) defaults() {
	if _, ok := uimu.mutation.UpdatedAt(); !ok {
		v := userinternalmessage.UpdateDefaultUpdatedAt()
		uimu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uimu *UserInternalMessageUpdate) check() error {
	if uimu.mutation.MessageCleared() && len(uimu.mutation.MessageIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "UserInternalMessage.message"`)
	}
	if uimu.mutation.UserCleared() && len(uimu.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "UserInternalMessage.user"`)
	}
	return nil
}

func (uimu *UserInternalMessageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uimu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(userinternalmessage.Table, userinternalmessage.Columns, sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt))
	if ps := uimu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uimu.mutation.UpdatedAt(); ok {
		_spec.SetField(userinternalmessage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uimu.mutation.Status(); ok {
		_spec.SetField(userinternalmessage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uimu.mutation.AddedStatus(); ok {
		_spec.AddField(userinternalmessage.FieldStatus, field.TypeInt8, value)
	}
	if uimu.mutation.MessageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.MessageTable,
			Columns: []string{userinternalmessage.MessageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uimu.mutation.MessageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.MessageTable,
			Columns: []string{userinternalmessage.MessageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uimu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.UserTable,
			Columns: []string{userinternalmessage.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uimu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.UserTable,
			Columns: []string{userinternalmessage.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uimu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{userinternalmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uimu.mutation.done = true
	return n, nil
}

// UserInternalMessageUpdateOne is the builder for updating a single UserInternalMessage entity.
type UserInternalMessageUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserInternalMessageMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (uimuo *UserInternalMessageUpdateOne) SetUpdatedAt(t time.Time) *UserInternalMessageUpdateOne {
	uimuo.mutation.SetUpdatedAt(t)
	return uimuo
}

// SetInternalMessageID sets the "internal_message_id" field.
func (uimuo *UserInternalMessageUpdateOne) SetInternalMessageID(i int) *UserInternalMessageUpdateOne {
	uimuo.mutation.SetInternalMessageID(i)
	return uimuo
}

// SetNillableInternalMessageID sets the "internal_message_id" field if the given value is not nil.
func (uimuo *UserInternalMessageUpdateOne) SetNillableInternalMessageID(i *int) *UserInternalMessageUpdateOne {
	if i != nil {
		uimuo.SetInternalMessageID(*i)
	}
	return uimuo
}

// SetUserID sets the "user_id" field.
func (uimuo *UserInternalMessageUpdateOne) SetUserID(i int) *UserInternalMessageUpdateOne {
	uimuo.mutation.SetUserID(i)
	return uimuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (uimuo *UserInternalMessageUpdateOne) SetNillableUserID(i *int) *UserInternalMessageUpdateOne {
	if i != nil {
		uimuo.SetUserID(*i)
	}
	return uimuo
}

// SetStatus sets the "status" field.
func (uimuo *UserInternalMessageUpdateOne) SetStatus(i int8) *UserInternalMessageUpdateOne {
	uimuo.mutation.ResetStatus()
	uimuo.mutation.SetStatus(i)
	return uimuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uimuo *UserInternalMessageUpdateOne) SetNillableStatus(i *int8) *UserInternalMessageUpdateOne {
	if i != nil {
		uimuo.SetStatus(*i)
	}
	return uimuo
}

// AddStatus adds i to the "status" field.
func (uimuo *UserInternalMessageUpdateOne) AddStatus(i int8) *UserInternalMessageUpdateOne {
	uimuo.mutation.AddStatus(i)
	return uimuo
}

// SetMessageID sets the "message" edge to the InternalMessage entity by ID.
func (uimuo *UserInternalMessageUpdateOne) SetMessageID(id int) *UserInternalMessageUpdateOne {
	uimuo.mutation.SetMessageID(id)
	return uimuo
}

// SetMessage sets the "message" edge to the InternalMessage entity.
func (uimuo *UserInternalMessageUpdateOne) SetMessage(i *InternalMessage) *UserInternalMessageUpdateOne {
	return uimuo.SetMessageID(i.ID)
}

// SetUser sets the "user" edge to the User entity.
func (uimuo *UserInternalMessageUpdateOne) SetUser(u *User) *UserInternalMessageUpdateOne {
	return uimuo.SetUserID(u.ID)
}

// Mutation returns the UserInternalMessageMutation object of the builder.
func (uimuo *UserInternalMessageUpdateOne) Mutation() *UserInternalMessageMutation {
	return uimuo.mutation
}

// ClearMessage clears the "message" edge to the InternalMessage entity.
func (uimuo *UserInternalMessageUpdateOne) ClearMessage() *UserInternalMessageUpdateOne {
	uimuo.mutation.ClearMessage()
	return uimuo
}

// ClearUser clears the "user" edge to the User entity.
func (uimuo *UserInternalMessageUpdateOne) ClearUser() *UserInternalMessageUpdateOne {
	uimuo.mutation.ClearUser()
	return uimuo
}

// Where appends a list predicates to the UserInternalMessageUpdate builder.
func (uimuo *UserInternalMessageUpdateOne) Where(ps ...predicate.UserInternalMessage) *UserInternalMessageUpdateOne {
	uimuo.mutation.Where(ps...)
	return uimuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uimuo *UserInternalMessageUpdateOne) Select(field string, fields ...string) *UserInternalMessageUpdateOne {
	uimuo.fields = append([]string{field}, fields...)
	return uimuo
}

// Save executes the query and returns the updated UserInternalMessage entity.
func (uimuo *UserInternalMessageUpdateOne) Save(ctx context.Context) (*UserInternalMessage, error) {
	uimuo.defaults()
	return withHooks(ctx, uimuo.sqlSave, uimuo.mutation, uimuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uimuo *UserInternalMessageUpdateOne) SaveX(ctx context.Context) *UserInternalMessage {
	node, err := uimuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uimuo *UserInternalMessageUpdateOne) Exec(ctx context.Context) error {
	_, err := uimuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uimuo *UserInternalMessageUpdateOne) ExecX(ctx context.Context) {
	if err := uimuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uimuo *UserInternalMessageUpdateOne) defaults() {
	if _, ok := uimuo.mutation.UpdatedAt(); !ok {
		v := userinternalmessage.UpdateDefaultUpdatedAt()
		uimuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uimuo *UserInternalMessageUpdateOne) check() error {
	if uimuo.mutation.MessageCleared() && len(uimuo.mutation.MessageIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "UserInternalMessage.message"`)
	}
	if uimuo.mutation.UserCleared() && len(uimuo.mutation.UserIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "UserInternalMessage.user"`)
	}
	return nil
}

func (uimuo *UserInternalMessageUpdateOne) sqlSave(ctx context.Context) (_node *UserInternalMessage, err error) {
	if err := uimuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(userinternalmessage.Table, userinternalmessage.Columns, sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt))
	id, ok := uimuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "UserInternalMessage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uimuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, userinternalmessage.FieldID)
		for _, f := range fields {
			if !userinternalmessage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != userinternalmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uimuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uimuo.mutation.UpdatedAt(); ok {
		_spec.SetField(userinternalmessage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uimuo.mutation.Status(); ok {
		_spec.SetField(userinternalmessage.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := uimuo.mutation.AddedStatus(); ok {
		_spec.AddField(userinternalmessage.FieldStatus, field.TypeInt8, value)
	}
	if uimuo.mutation.MessageCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.MessageTable,
			Columns: []string{userinternalmessage.MessageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uimuo.mutation.MessageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.MessageTable,
			Columns: []string{userinternalmessage.MessageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uimuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.UserTable,
			Columns: []string{userinternalmessage.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uimuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.UserTable,
			Columns: []string{userinternalmessage.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &UserInternalMessage{config: uimuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uimuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{userinternalmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uimuo.mutation.done = true
	return _node, nil
}
