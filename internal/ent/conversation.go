// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 智能体会话表
type Conversation struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 智能体id
	AiAgentID int `json:"ai_agent_id,omitempty"`
	// 用户id
	UserID int `json:"user_id,omitempty"`
	// 智能体会话id
	AiAgentConversationID string `json:"ai_agent_conversation_id,omitempty"`
	// 会话记录名称
	Name string `json:"name,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ConversationQuery when eager-loading is set.
	Edges        ConversationEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ConversationEdges holds the relations/edges for other nodes in the graph.
type ConversationEdges struct {
	// Contents holds the value of the contents edge.
	Contents []*ConversationContent `json:"contents,omitempty"`
	// AiAgent holds the value of the ai_agent edge.
	AiAgent *AiAgent `json:"ai_agent,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// ContentsOrErr returns the Contents value or an error if the edge
// was not loaded in eager-loading.
func (e ConversationEdges) ContentsOrErr() ([]*ConversationContent, error) {
	if e.loadedTypes[0] {
		return e.Contents, nil
	}
	return nil, &NotLoadedError{edge: "contents"}
}

// AiAgentOrErr returns the AiAgent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ConversationEdges) AiAgentOrErr() (*AiAgent, error) {
	if e.AiAgent != nil {
		return e.AiAgent, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: aiagent.Label}
	}
	return nil, &NotLoadedError{edge: "ai_agent"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Conversation) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case conversation.FieldID, conversation.FieldAiAgentID, conversation.FieldUserID:
			values[i] = new(sql.NullInt64)
		case conversation.FieldAiAgentConversationID, conversation.FieldName:
			values[i] = new(sql.NullString)
		case conversation.FieldCreatedAt, conversation.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Conversation fields.
func (c *Conversation) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case conversation.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			c.ID = int(value.Int64)
		case conversation.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				c.CreatedAt = value.Time
			}
		case conversation.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				c.UpdatedAt = value.Time
			}
		case conversation.FieldAiAgentID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field ai_agent_id", values[i])
			} else if value.Valid {
				c.AiAgentID = int(value.Int64)
			}
		case conversation.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				c.UserID = int(value.Int64)
			}
		case conversation.FieldAiAgentConversationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ai_agent_conversation_id", values[i])
			} else if value.Valid {
				c.AiAgentConversationID = value.String
			}
		case conversation.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				c.Name = value.String
			}
		default:
			c.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Conversation.
// This includes values selected through modifiers, order, etc.
func (c *Conversation) Value(name string) (ent.Value, error) {
	return c.selectValues.Get(name)
}

// QueryContents queries the "contents" edge of the Conversation entity.
func (c *Conversation) QueryContents() *ConversationContentQuery {
	return NewConversationClient(c.config).QueryContents(c)
}

// QueryAiAgent queries the "ai_agent" edge of the Conversation entity.
func (c *Conversation) QueryAiAgent() *AiAgentQuery {
	return NewConversationClient(c.config).QueryAiAgent(c)
}

// Update returns a builder for updating this Conversation.
// Note that you need to call Conversation.Unwrap() before calling this method if this Conversation
// was returned from a transaction, and the transaction was committed or rolled back.
func (c *Conversation) Update() *ConversationUpdateOne {
	return NewConversationClient(c.config).UpdateOne(c)
}

// Unwrap unwraps the Conversation entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (c *Conversation) Unwrap() *Conversation {
	_tx, ok := c.config.driver.(*txDriver)
	if !ok {
		panic("ent: Conversation is not a transactional entity")
	}
	c.config.driver = _tx.drv
	return c
}

// String implements the fmt.Stringer.
func (c *Conversation) String() string {
	var builder strings.Builder
	builder.WriteString("Conversation(")
	builder.WriteString(fmt.Sprintf("id=%v, ", c.ID))
	builder.WriteString("created_at=")
	builder.WriteString(c.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(c.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("ai_agent_id=")
	builder.WriteString(fmt.Sprintf("%v", c.AiAgentID))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", c.UserID))
	builder.WriteString(", ")
	builder.WriteString("ai_agent_conversation_id=")
	builder.WriteString(c.AiAgentConversationID)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(c.Name)
	builder.WriteByte(')')
	return builder.String()
}

// Conversations is a parsable slice of Conversation.
type Conversations []*Conversation
