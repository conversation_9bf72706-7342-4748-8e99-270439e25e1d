// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/userinternalmessage"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserInternalMessageDelete is the builder for deleting a UserInternalMessage entity.
type UserInternalMessageDelete struct {
	config
	hooks    []Hook
	mutation *UserInternalMessageMutation
}

// Where appends a list predicates to the UserInternalMessageDelete builder.
func (uimd *UserInternalMessageDelete) Where(ps ...predicate.UserInternalMessage) *UserInternalMessageDelete {
	uimd.mutation.Where(ps...)
	return uimd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (uimd *UserInternalMessageDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, uimd.sqlExec, uimd.mutation, uimd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (uimd *UserInternalMessageDelete) ExecX(ctx context.Context) int {
	n, err := uimd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (uimd *UserInternalMessageDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(userinternalmessage.Table, sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt))
	if ps := uimd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, uimd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	uimd.mutation.done = true
	return affected, err
}

// UserInternalMessageDeleteOne is the builder for deleting a single UserInternalMessage entity.
type UserInternalMessageDeleteOne struct {
	uimd *UserInternalMessageDelete
}

// Where appends a list predicates to the UserInternalMessageDelete builder.
func (uimdo *UserInternalMessageDeleteOne) Where(ps ...predicate.UserInternalMessage) *UserInternalMessageDeleteOne {
	uimdo.uimd.mutation.Where(ps...)
	return uimdo
}

// Exec executes the deletion query.
func (uimdo *UserInternalMessageDeleteOne) Exec(ctx context.Context) error {
	n, err := uimdo.uimd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{userinternalmessage.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (uimdo *UserInternalMessageDeleteOne) ExecX(ctx context.Context) {
	if err := uimdo.Exec(ctx); err != nil {
		panic(err)
	}
}
