// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InternalMessageUpdate is the builder for updating InternalMessage entities.
type InternalMessageUpdate struct {
	config
	hooks    []Hook
	mutation *InternalMessageMutation
}

// Where appends a list predicates to the InternalMessageUpdate builder.
func (imu *InternalMessageUpdate) Where(ps ...predicate.InternalMessage) *InternalMessageUpdate {
	imu.mutation.Where(ps...)
	return imu
}

// SetUpdatedAt sets the "updated_at" field.
func (imu *InternalMessageUpdate) SetUpdatedAt(t time.Time) *InternalMessageUpdate {
	imu.mutation.SetUpdatedAt(t)
	return imu
}

// SetTitle sets the "title" field.
func (imu *InternalMessageUpdate) SetTitle(s string) *InternalMessageUpdate {
	imu.mutation.SetTitle(s)
	return imu
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (imu *InternalMessageUpdate) SetNillableTitle(s *string) *InternalMessageUpdate {
	if s != nil {
		imu.SetTitle(*s)
	}
	return imu
}

// ClearTitle clears the value of the "title" field.
func (imu *InternalMessageUpdate) ClearTitle() *InternalMessageUpdate {
	imu.mutation.ClearTitle()
	return imu
}

// SetContent sets the "content" field.
func (imu *InternalMessageUpdate) SetContent(s string) *InternalMessageUpdate {
	imu.mutation.SetContent(s)
	return imu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (imu *InternalMessageUpdate) SetNillableContent(s *string) *InternalMessageUpdate {
	if s != nil {
		imu.SetContent(*s)
	}
	return imu
}

// AddUserIDs adds the "users" edge to the UserInternalMessage entity by IDs.
func (imu *InternalMessageUpdate) AddUserIDs(ids ...int) *InternalMessageUpdate {
	imu.mutation.AddUserIDs(ids...)
	return imu
}

// AddUsers adds the "users" edges to the UserInternalMessage entity.
func (imu *InternalMessageUpdate) AddUsers(u ...*UserInternalMessage) *InternalMessageUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return imu.AddUserIDs(ids...)
}

// Mutation returns the InternalMessageMutation object of the builder.
func (imu *InternalMessageUpdate) Mutation() *InternalMessageMutation {
	return imu.mutation
}

// ClearUsers clears all "users" edges to the UserInternalMessage entity.
func (imu *InternalMessageUpdate) ClearUsers() *InternalMessageUpdate {
	imu.mutation.ClearUsers()
	return imu
}

// RemoveUserIDs removes the "users" edge to UserInternalMessage entities by IDs.
func (imu *InternalMessageUpdate) RemoveUserIDs(ids ...int) *InternalMessageUpdate {
	imu.mutation.RemoveUserIDs(ids...)
	return imu
}

// RemoveUsers removes "users" edges to UserInternalMessage entities.
func (imu *InternalMessageUpdate) RemoveUsers(u ...*UserInternalMessage) *InternalMessageUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return imu.RemoveUserIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (imu *InternalMessageUpdate) Save(ctx context.Context) (int, error) {
	imu.defaults()
	return withHooks(ctx, imu.sqlSave, imu.mutation, imu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (imu *InternalMessageUpdate) SaveX(ctx context.Context) int {
	affected, err := imu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (imu *InternalMessageUpdate) Exec(ctx context.Context) error {
	_, err := imu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (imu *InternalMessageUpdate) ExecX(ctx context.Context) {
	if err := imu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (imu *InternalMessageUpdate) defaults() {
	if _, ok := imu.mutation.UpdatedAt(); !ok {
		v := internalmessage.UpdateDefaultUpdatedAt()
		imu.mutation.SetUpdatedAt(v)
	}
}

func (imu *InternalMessageUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(internalmessage.Table, internalmessage.Columns, sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt))
	if ps := imu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := imu.mutation.UpdatedAt(); ok {
		_spec.SetField(internalmessage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := imu.mutation.Title(); ok {
		_spec.SetField(internalmessage.FieldTitle, field.TypeString, value)
	}
	if imu.mutation.TitleCleared() {
		_spec.ClearField(internalmessage.FieldTitle, field.TypeString)
	}
	if value, ok := imu.mutation.Content(); ok {
		_spec.SetField(internalmessage.FieldContent, field.TypeString, value)
	}
	if imu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := imu.mutation.RemovedUsersIDs(); len(nodes) > 0 && !imu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := imu.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, imu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{internalmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	imu.mutation.done = true
	return n, nil
}

// InternalMessageUpdateOne is the builder for updating a single InternalMessage entity.
type InternalMessageUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *InternalMessageMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (imuo *InternalMessageUpdateOne) SetUpdatedAt(t time.Time) *InternalMessageUpdateOne {
	imuo.mutation.SetUpdatedAt(t)
	return imuo
}

// SetTitle sets the "title" field.
func (imuo *InternalMessageUpdateOne) SetTitle(s string) *InternalMessageUpdateOne {
	imuo.mutation.SetTitle(s)
	return imuo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (imuo *InternalMessageUpdateOne) SetNillableTitle(s *string) *InternalMessageUpdateOne {
	if s != nil {
		imuo.SetTitle(*s)
	}
	return imuo
}

// ClearTitle clears the value of the "title" field.
func (imuo *InternalMessageUpdateOne) ClearTitle() *InternalMessageUpdateOne {
	imuo.mutation.ClearTitle()
	return imuo
}

// SetContent sets the "content" field.
func (imuo *InternalMessageUpdateOne) SetContent(s string) *InternalMessageUpdateOne {
	imuo.mutation.SetContent(s)
	return imuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (imuo *InternalMessageUpdateOne) SetNillableContent(s *string) *InternalMessageUpdateOne {
	if s != nil {
		imuo.SetContent(*s)
	}
	return imuo
}

// AddUserIDs adds the "users" edge to the UserInternalMessage entity by IDs.
func (imuo *InternalMessageUpdateOne) AddUserIDs(ids ...int) *InternalMessageUpdateOne {
	imuo.mutation.AddUserIDs(ids...)
	return imuo
}

// AddUsers adds the "users" edges to the UserInternalMessage entity.
func (imuo *InternalMessageUpdateOne) AddUsers(u ...*UserInternalMessage) *InternalMessageUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return imuo.AddUserIDs(ids...)
}

// Mutation returns the InternalMessageMutation object of the builder.
func (imuo *InternalMessageUpdateOne) Mutation() *InternalMessageMutation {
	return imuo.mutation
}

// ClearUsers clears all "users" edges to the UserInternalMessage entity.
func (imuo *InternalMessageUpdateOne) ClearUsers() *InternalMessageUpdateOne {
	imuo.mutation.ClearUsers()
	return imuo
}

// RemoveUserIDs removes the "users" edge to UserInternalMessage entities by IDs.
func (imuo *InternalMessageUpdateOne) RemoveUserIDs(ids ...int) *InternalMessageUpdateOne {
	imuo.mutation.RemoveUserIDs(ids...)
	return imuo
}

// RemoveUsers removes "users" edges to UserInternalMessage entities.
func (imuo *InternalMessageUpdateOne) RemoveUsers(u ...*UserInternalMessage) *InternalMessageUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return imuo.RemoveUserIDs(ids...)
}

// Where appends a list predicates to the InternalMessageUpdate builder.
func (imuo *InternalMessageUpdateOne) Where(ps ...predicate.InternalMessage) *InternalMessageUpdateOne {
	imuo.mutation.Where(ps...)
	return imuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (imuo *InternalMessageUpdateOne) Select(field string, fields ...string) *InternalMessageUpdateOne {
	imuo.fields = append([]string{field}, fields...)
	return imuo
}

// Save executes the query and returns the updated InternalMessage entity.
func (imuo *InternalMessageUpdateOne) Save(ctx context.Context) (*InternalMessage, error) {
	imuo.defaults()
	return withHooks(ctx, imuo.sqlSave, imuo.mutation, imuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (imuo *InternalMessageUpdateOne) SaveX(ctx context.Context) *InternalMessage {
	node, err := imuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (imuo *InternalMessageUpdateOne) Exec(ctx context.Context) error {
	_, err := imuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (imuo *InternalMessageUpdateOne) ExecX(ctx context.Context) {
	if err := imuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (imuo *InternalMessageUpdateOne) defaults() {
	if _, ok := imuo.mutation.UpdatedAt(); !ok {
		v := internalmessage.UpdateDefaultUpdatedAt()
		imuo.mutation.SetUpdatedAt(v)
	}
}

func (imuo *InternalMessageUpdateOne) sqlSave(ctx context.Context) (_node *InternalMessage, err error) {
	_spec := sqlgraph.NewUpdateSpec(internalmessage.Table, internalmessage.Columns, sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt))
	id, ok := imuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "InternalMessage.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := imuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, internalmessage.FieldID)
		for _, f := range fields {
			if !internalmessage.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != internalmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := imuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := imuo.mutation.UpdatedAt(); ok {
		_spec.SetField(internalmessage.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := imuo.mutation.Title(); ok {
		_spec.SetField(internalmessage.FieldTitle, field.TypeString, value)
	}
	if imuo.mutation.TitleCleared() {
		_spec.ClearField(internalmessage.FieldTitle, field.TypeString)
	}
	if value, ok := imuo.mutation.Content(); ok {
		_spec.SetField(internalmessage.FieldContent, field.TypeString, value)
	}
	if imuo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := imuo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !imuo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := imuo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &InternalMessage{config: imuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, imuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{internalmessage.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	imuo.mutation.done = true
	return _node, nil
}
