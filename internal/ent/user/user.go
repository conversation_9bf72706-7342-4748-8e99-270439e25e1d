// Code generated by ent, DO NOT EDIT.

package user

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the user type in the database.
	Label = "user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldBatchID holds the string denoting the batch_id field in the database.
	FieldBatchID = "batch_id"
	// FieldTestCodeID holds the string denoting the test_code_id field in the database.
	FieldTestCodeID = "test_code_id"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// EdgeCode holds the string denoting the code edge name in mutations.
	EdgeCode = "code"
	// EdgeBatch holds the string denoting the batch edge name in mutations.
	EdgeBatch = "batch"
	// EdgeMessages holds the string denoting the messages edge name in mutations.
	EdgeMessages = "messages"
	// Table holds the table name of the user in the database.
	Table = "users"
	// CodeTable is the table that holds the code relation/edge.
	CodeTable = "users"
	// CodeInverseTable is the table name for the TestCode entity.
	// It exists in this package in order to avoid circular dependency with the "testcode" package.
	CodeInverseTable = "test_codes"
	// CodeColumn is the table column denoting the code relation/edge.
	CodeColumn = "test_code_id"
	// BatchTable is the table that holds the batch relation/edge.
	BatchTable = "users"
	// BatchInverseTable is the table name for the Batch entity.
	// It exists in this package in order to avoid circular dependency with the "batch" package.
	BatchInverseTable = "batches"
	// BatchColumn is the table column denoting the batch relation/edge.
	BatchColumn = "batch_id"
	// MessagesTable is the table that holds the messages relation/edge.
	MessagesTable = "user_internal_messages"
	// MessagesInverseTable is the table name for the UserInternalMessage entity.
	// It exists in this package in order to avoid circular dependency with the "userinternalmessage" package.
	MessagesInverseTable = "user_internal_messages"
	// MessagesColumn is the table column denoting the messages relation/edge.
	MessagesColumn = "user_id"
)

// Columns holds all SQL columns for user fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldBatchID,
	FieldTestCodeID,
	FieldUsername,
	FieldPassword,
	FieldEmail,
	FieldStatus,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus int8
)

// OrderOption defines the ordering options for the User queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByBatchID orders the results by the batch_id field.
func ByBatchID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldBatchID, opts...).ToFunc()
}

// ByTestCodeID orders the results by the test_code_id field.
func ByTestCodeID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTestCodeID, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByCodeField orders the results by code field.
func ByCodeField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newCodeStep(), sql.OrderByField(field, opts...))
	}
}

// ByBatchField orders the results by batch field.
func ByBatchField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newBatchStep(), sql.OrderByField(field, opts...))
	}
}

// ByMessagesCount orders the results by messages count.
func ByMessagesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMessagesStep(), opts...)
	}
}

// ByMessages orders the results by messages terms.
func ByMessages(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMessagesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newCodeStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(CodeInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2O, true, CodeTable, CodeColumn),
	)
}
func newBatchStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(BatchInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, BatchTable, BatchColumn),
	)
}
func newMessagesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MessagesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MessagesTable, MessagesColumn),
	)
}
