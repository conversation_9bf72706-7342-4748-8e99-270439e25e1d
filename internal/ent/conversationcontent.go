// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 智能体会话详情表
type ConversationContent struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// conversations表id
	ConversationID int `json:"conversation_id,omitempty"`
	// 会话内容
	Content string `json:"content,omitempty"`
	// 类型:1 用户; 2 agent
	Type int8 `json:"type,omitempty"`
	// <PERSON><PERSON> holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ConversationContentQuery when eager-loading is set.
	Edges        ConversationContentEdges `json:"edges"`
	selectValues sql.SelectValues
}

// ConversationContentEdges holds the relations/edges for other nodes in the graph.
type ConversationContentEdges struct {
	// Conversation holds the value of the conversation edge.
	Conversation *Conversation `json:"conversation,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// ConversationOrErr returns the Conversation value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ConversationContentEdges) ConversationOrErr() (*Conversation, error) {
	if e.Conversation != nil {
		return e.Conversation, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: conversation.Label}
	}
	return nil, &NotLoadedError{edge: "conversation"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*ConversationContent) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case conversationcontent.FieldID, conversationcontent.FieldConversationID, conversationcontent.FieldType:
			values[i] = new(sql.NullInt64)
		case conversationcontent.FieldContent:
			values[i] = new(sql.NullString)
		case conversationcontent.FieldCreatedAt, conversationcontent.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the ConversationContent fields.
func (cc *ConversationContent) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case conversationcontent.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			cc.ID = int(value.Int64)
		case conversationcontent.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				cc.CreatedAt = value.Time
			}
		case conversationcontent.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				cc.UpdatedAt = value.Time
			}
		case conversationcontent.FieldConversationID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field conversation_id", values[i])
			} else if value.Valid {
				cc.ConversationID = int(value.Int64)
			}
		case conversationcontent.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				cc.Content = value.String
			}
		case conversationcontent.FieldType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field type", values[i])
			} else if value.Valid {
				cc.Type = int8(value.Int64)
			}
		default:
			cc.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the ConversationContent.
// This includes values selected through modifiers, order, etc.
func (cc *ConversationContent) Value(name string) (ent.Value, error) {
	return cc.selectValues.Get(name)
}

// QueryConversation queries the "conversation" edge of the ConversationContent entity.
func (cc *ConversationContent) QueryConversation() *ConversationQuery {
	return NewConversationContentClient(cc.config).QueryConversation(cc)
}

// Update returns a builder for updating this ConversationContent.
// Note that you need to call ConversationContent.Unwrap() before calling this method if this ConversationContent
// was returned from a transaction, and the transaction was committed or rolled back.
func (cc *ConversationContent) Update() *ConversationContentUpdateOne {
	return NewConversationContentClient(cc.config).UpdateOne(cc)
}

// Unwrap unwraps the ConversationContent entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (cc *ConversationContent) Unwrap() *ConversationContent {
	_tx, ok := cc.config.driver.(*txDriver)
	if !ok {
		panic("ent: ConversationContent is not a transactional entity")
	}
	cc.config.driver = _tx.drv
	return cc
}

// String implements the fmt.Stringer.
func (cc *ConversationContent) String() string {
	var builder strings.Builder
	builder.WriteString("ConversationContent(")
	builder.WriteString(fmt.Sprintf("id=%v, ", cc.ID))
	builder.WriteString("created_at=")
	builder.WriteString(cc.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(cc.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("conversation_id=")
	builder.WriteString(fmt.Sprintf("%v", cc.ConversationID))
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(cc.Content)
	builder.WriteString(", ")
	builder.WriteString("type=")
	builder.WriteString(fmt.Sprintf("%v", cc.Type))
	builder.WriteByte(')')
	return builder.String()
}

// ConversationContents is a parsable slice of ConversationContent.
type ConversationContents []*ConversationContent
