// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// BatchUpdate is the builder for updating Batch entities.
type BatchUpdate struct {
	config
	hooks    []Hook
	mutation *BatchMutation
}

// Where appends a list predicates to the BatchUpdate builder.
func (bu *BatchUpdate) Where(ps ...predicate.Batch) *BatchUpdate {
	bu.mutation.Where(ps...)
	return bu
}

// SetUpdatedAt sets the "updated_at" field.
func (bu *BatchUpdate) SetUpdatedAt(t time.Time) *BatchUpdate {
	bu.mutation.SetUpdatedAt(t)
	return bu
}

// SetName sets the "name" field.
func (bu *BatchUpdate) SetName(s string) *BatchUpdate {
	bu.mutation.SetName(s)
	return bu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (bu *BatchUpdate) SetNillableName(s *string) *BatchUpdate {
	if s != nil {
		bu.SetName(*s)
	}
	return bu
}

// SetNum sets the "num" field.
func (bu *BatchUpdate) SetNum(i int64) *BatchUpdate {
	bu.mutation.ResetNum()
	bu.mutation.SetNum(i)
	return bu
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (bu *BatchUpdate) SetNillableNum(i *int64) *BatchUpdate {
	if i != nil {
		bu.SetNum(*i)
	}
	return bu
}

// AddNum adds i to the "num" field.
func (bu *BatchUpdate) AddNum(i int64) *BatchUpdate {
	bu.mutation.AddNum(i)
	return bu
}

// SetExpiredAt sets the "expired_at" field.
func (bu *BatchUpdate) SetExpiredAt(t time.Time) *BatchUpdate {
	bu.mutation.SetExpiredAt(t)
	return bu
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (bu *BatchUpdate) SetNillableExpiredAt(t *time.Time) *BatchUpdate {
	if t != nil {
		bu.SetExpiredAt(*t)
	}
	return bu
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (bu *BatchUpdate) ClearExpiredAt() *BatchUpdate {
	bu.mutation.ClearExpiredAt()
	return bu
}

// AddCodeIDs adds the "codes" edge to the TestCode entity by IDs.
func (bu *BatchUpdate) AddCodeIDs(ids ...int) *BatchUpdate {
	bu.mutation.AddCodeIDs(ids...)
	return bu
}

// AddCodes adds the "codes" edges to the TestCode entity.
func (bu *BatchUpdate) AddCodes(t ...*TestCode) *BatchUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return bu.AddCodeIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (bu *BatchUpdate) AddUserIDs(ids ...int) *BatchUpdate {
	bu.mutation.AddUserIDs(ids...)
	return bu
}

// AddUsers adds the "users" edges to the User entity.
func (bu *BatchUpdate) AddUsers(u ...*User) *BatchUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return bu.AddUserIDs(ids...)
}

// Mutation returns the BatchMutation object of the builder.
func (bu *BatchUpdate) Mutation() *BatchMutation {
	return bu.mutation
}

// ClearCodes clears all "codes" edges to the TestCode entity.
func (bu *BatchUpdate) ClearCodes() *BatchUpdate {
	bu.mutation.ClearCodes()
	return bu
}

// RemoveCodeIDs removes the "codes" edge to TestCode entities by IDs.
func (bu *BatchUpdate) RemoveCodeIDs(ids ...int) *BatchUpdate {
	bu.mutation.RemoveCodeIDs(ids...)
	return bu
}

// RemoveCodes removes "codes" edges to TestCode entities.
func (bu *BatchUpdate) RemoveCodes(t ...*TestCode) *BatchUpdate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return bu.RemoveCodeIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (bu *BatchUpdate) ClearUsers() *BatchUpdate {
	bu.mutation.ClearUsers()
	return bu
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (bu *BatchUpdate) RemoveUserIDs(ids ...int) *BatchUpdate {
	bu.mutation.RemoveUserIDs(ids...)
	return bu
}

// RemoveUsers removes "users" edges to User entities.
func (bu *BatchUpdate) RemoveUsers(u ...*User) *BatchUpdate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return bu.RemoveUserIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (bu *BatchUpdate) Save(ctx context.Context) (int, error) {
	bu.defaults()
	return withHooks(ctx, bu.sqlSave, bu.mutation, bu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (bu *BatchUpdate) SaveX(ctx context.Context) int {
	affected, err := bu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (bu *BatchUpdate) Exec(ctx context.Context) error {
	_, err := bu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bu *BatchUpdate) ExecX(ctx context.Context) {
	if err := bu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bu *BatchUpdate) defaults() {
	if _, ok := bu.mutation.UpdatedAt(); !ok {
		v := batch.UpdateDefaultUpdatedAt()
		bu.mutation.SetUpdatedAt(v)
	}
}

func (bu *BatchUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(batch.Table, batch.Columns, sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt))
	if ps := bu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := bu.mutation.UpdatedAt(); ok {
		_spec.SetField(batch.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := bu.mutation.Name(); ok {
		_spec.SetField(batch.FieldName, field.TypeString, value)
	}
	if value, ok := bu.mutation.Num(); ok {
		_spec.SetField(batch.FieldNum, field.TypeInt64, value)
	}
	if value, ok := bu.mutation.AddedNum(); ok {
		_spec.AddField(batch.FieldNum, field.TypeInt64, value)
	}
	if value, ok := bu.mutation.ExpiredAt(); ok {
		_spec.SetField(batch.FieldExpiredAt, field.TypeTime, value)
	}
	if bu.mutation.ExpiredAtCleared() {
		_spec.ClearField(batch.FieldExpiredAt, field.TypeTime)
	}
	if bu.mutation.CodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RemovedCodesIDs(); len(nodes) > 0 && !bu.mutation.CodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.CodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if bu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RemovedUsersIDs(); len(nodes) > 0 && !bu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, bu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{batch.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	bu.mutation.done = true
	return n, nil
}

// BatchUpdateOne is the builder for updating a single Batch entity.
type BatchUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *BatchMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (buo *BatchUpdateOne) SetUpdatedAt(t time.Time) *BatchUpdateOne {
	buo.mutation.SetUpdatedAt(t)
	return buo
}

// SetName sets the "name" field.
func (buo *BatchUpdateOne) SetName(s string) *BatchUpdateOne {
	buo.mutation.SetName(s)
	return buo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (buo *BatchUpdateOne) SetNillableName(s *string) *BatchUpdateOne {
	if s != nil {
		buo.SetName(*s)
	}
	return buo
}

// SetNum sets the "num" field.
func (buo *BatchUpdateOne) SetNum(i int64) *BatchUpdateOne {
	buo.mutation.ResetNum()
	buo.mutation.SetNum(i)
	return buo
}

// SetNillableNum sets the "num" field if the given value is not nil.
func (buo *BatchUpdateOne) SetNillableNum(i *int64) *BatchUpdateOne {
	if i != nil {
		buo.SetNum(*i)
	}
	return buo
}

// AddNum adds i to the "num" field.
func (buo *BatchUpdateOne) AddNum(i int64) *BatchUpdateOne {
	buo.mutation.AddNum(i)
	return buo
}

// SetExpiredAt sets the "expired_at" field.
func (buo *BatchUpdateOne) SetExpiredAt(t time.Time) *BatchUpdateOne {
	buo.mutation.SetExpiredAt(t)
	return buo
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (buo *BatchUpdateOne) SetNillableExpiredAt(t *time.Time) *BatchUpdateOne {
	if t != nil {
		buo.SetExpiredAt(*t)
	}
	return buo
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (buo *BatchUpdateOne) ClearExpiredAt() *BatchUpdateOne {
	buo.mutation.ClearExpiredAt()
	return buo
}

// AddCodeIDs adds the "codes" edge to the TestCode entity by IDs.
func (buo *BatchUpdateOne) AddCodeIDs(ids ...int) *BatchUpdateOne {
	buo.mutation.AddCodeIDs(ids...)
	return buo
}

// AddCodes adds the "codes" edges to the TestCode entity.
func (buo *BatchUpdateOne) AddCodes(t ...*TestCode) *BatchUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return buo.AddCodeIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (buo *BatchUpdateOne) AddUserIDs(ids ...int) *BatchUpdateOne {
	buo.mutation.AddUserIDs(ids...)
	return buo
}

// AddUsers adds the "users" edges to the User entity.
func (buo *BatchUpdateOne) AddUsers(u ...*User) *BatchUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return buo.AddUserIDs(ids...)
}

// Mutation returns the BatchMutation object of the builder.
func (buo *BatchUpdateOne) Mutation() *BatchMutation {
	return buo.mutation
}

// ClearCodes clears all "codes" edges to the TestCode entity.
func (buo *BatchUpdateOne) ClearCodes() *BatchUpdateOne {
	buo.mutation.ClearCodes()
	return buo
}

// RemoveCodeIDs removes the "codes" edge to TestCode entities by IDs.
func (buo *BatchUpdateOne) RemoveCodeIDs(ids ...int) *BatchUpdateOne {
	buo.mutation.RemoveCodeIDs(ids...)
	return buo
}

// RemoveCodes removes "codes" edges to TestCode entities.
func (buo *BatchUpdateOne) RemoveCodes(t ...*TestCode) *BatchUpdateOne {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return buo.RemoveCodeIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (buo *BatchUpdateOne) ClearUsers() *BatchUpdateOne {
	buo.mutation.ClearUsers()
	return buo
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (buo *BatchUpdateOne) RemoveUserIDs(ids ...int) *BatchUpdateOne {
	buo.mutation.RemoveUserIDs(ids...)
	return buo
}

// RemoveUsers removes "users" edges to User entities.
func (buo *BatchUpdateOne) RemoveUsers(u ...*User) *BatchUpdateOne {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return buo.RemoveUserIDs(ids...)
}

// Where appends a list predicates to the BatchUpdate builder.
func (buo *BatchUpdateOne) Where(ps ...predicate.Batch) *BatchUpdateOne {
	buo.mutation.Where(ps...)
	return buo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (buo *BatchUpdateOne) Select(field string, fields ...string) *BatchUpdateOne {
	buo.fields = append([]string{field}, fields...)
	return buo
}

// Save executes the query and returns the updated Batch entity.
func (buo *BatchUpdateOne) Save(ctx context.Context) (*Batch, error) {
	buo.defaults()
	return withHooks(ctx, buo.sqlSave, buo.mutation, buo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (buo *BatchUpdateOne) SaveX(ctx context.Context) *Batch {
	node, err := buo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (buo *BatchUpdateOne) Exec(ctx context.Context) error {
	_, err := buo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (buo *BatchUpdateOne) ExecX(ctx context.Context) {
	if err := buo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (buo *BatchUpdateOne) defaults() {
	if _, ok := buo.mutation.UpdatedAt(); !ok {
		v := batch.UpdateDefaultUpdatedAt()
		buo.mutation.SetUpdatedAt(v)
	}
}

func (buo *BatchUpdateOne) sqlSave(ctx context.Context) (_node *Batch, err error) {
	_spec := sqlgraph.NewUpdateSpec(batch.Table, batch.Columns, sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt))
	id, ok := buo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Batch.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := buo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, batch.FieldID)
		for _, f := range fields {
			if !batch.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != batch.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := buo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := buo.mutation.UpdatedAt(); ok {
		_spec.SetField(batch.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := buo.mutation.Name(); ok {
		_spec.SetField(batch.FieldName, field.TypeString, value)
	}
	if value, ok := buo.mutation.Num(); ok {
		_spec.SetField(batch.FieldNum, field.TypeInt64, value)
	}
	if value, ok := buo.mutation.AddedNum(); ok {
		_spec.AddField(batch.FieldNum, field.TypeInt64, value)
	}
	if value, ok := buo.mutation.ExpiredAt(); ok {
		_spec.SetField(batch.FieldExpiredAt, field.TypeTime, value)
	}
	if buo.mutation.ExpiredAtCleared() {
		_spec.ClearField(batch.FieldExpiredAt, field.TypeTime)
	}
	if buo.mutation.CodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RemovedCodesIDs(); len(nodes) > 0 && !buo.mutation.CodesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.CodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if buo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !buo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Batch{config: buo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, buo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{batch.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	buo.mutation.done = true
	return _node, nil
}
