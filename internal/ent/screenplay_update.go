// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/screenplay"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ScreenplayUpdate is the builder for updating Screenplay entities.
type ScreenplayUpdate struct {
	config
	hooks    []Hook
	mutation *ScreenplayMutation
}

// Where appends a list predicates to the ScreenplayUpdate builder.
func (su *ScreenplayUpdate) Where(ps ...predicate.Screenplay) *ScreenplayUpdate {
	su.mutation.Where(ps...)
	return su
}

// SetUpdatedAt sets the "updated_at" field.
func (su *ScreenplayUpdate) SetUpdatedAt(t time.Time) *ScreenplayUpdate {
	su.mutation.SetUpdatedAt(t)
	return su
}

// SetUserID sets the "user_id" field.
func (su *ScreenplayUpdate) SetUserID(i int64) *ScreenplayUpdate {
	su.mutation.ResetUserID()
	su.mutation.SetUserID(i)
	return su
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableUserID(i *int64) *ScreenplayUpdate {
	if i != nil {
		su.SetUserID(*i)
	}
	return su
}

// AddUserID adds i to the "user_id" field.
func (su *ScreenplayUpdate) AddUserID(i int64) *ScreenplayUpdate {
	su.mutation.AddUserID(i)
	return su
}

// SetProjectID sets the "project_id" field.
func (su *ScreenplayUpdate) SetProjectID(i int64) *ScreenplayUpdate {
	su.mutation.ResetProjectID()
	su.mutation.SetProjectID(i)
	return su
}

// SetNillableProjectID sets the "project_id" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableProjectID(i *int64) *ScreenplayUpdate {
	if i != nil {
		su.SetProjectID(*i)
	}
	return su
}

// AddProjectID adds i to the "project_id" field.
func (su *ScreenplayUpdate) AddProjectID(i int64) *ScreenplayUpdate {
	su.mutation.AddProjectID(i)
	return su
}

// SetTitle sets the "title" field.
func (su *ScreenplayUpdate) SetTitle(s string) *ScreenplayUpdate {
	su.mutation.SetTitle(s)
	return su
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableTitle(s *string) *ScreenplayUpdate {
	if s != nil {
		su.SetTitle(*s)
	}
	return su
}

// SetContent sets the "content" field.
func (su *ScreenplayUpdate) SetContent(s string) *ScreenplayUpdate {
	su.mutation.SetContent(s)
	return su
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableContent(s *string) *ScreenplayUpdate {
	if s != nil {
		su.SetContent(*s)
	}
	return su
}

// SetType sets the "type" field.
func (su *ScreenplayUpdate) SetType(i int8) *ScreenplayUpdate {
	su.mutation.ResetType()
	su.mutation.SetType(i)
	return su
}

// SetNillableType sets the "type" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableType(i *int8) *ScreenplayUpdate {
	if i != nil {
		su.SetType(*i)
	}
	return su
}

// AddType adds i to the "type" field.
func (su *ScreenplayUpdate) AddType(i int8) *ScreenplayUpdate {
	su.mutation.AddType(i)
	return su
}

// SetStatus sets the "status" field.
func (su *ScreenplayUpdate) SetStatus(i int8) *ScreenplayUpdate {
	su.mutation.ResetStatus()
	su.mutation.SetStatus(i)
	return su
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (su *ScreenplayUpdate) SetNillableStatus(i *int8) *ScreenplayUpdate {
	if i != nil {
		su.SetStatus(*i)
	}
	return su
}

// AddStatus adds i to the "status" field.
func (su *ScreenplayUpdate) AddStatus(i int8) *ScreenplayUpdate {
	su.mutation.AddStatus(i)
	return su
}

// Mutation returns the ScreenplayMutation object of the builder.
func (su *ScreenplayUpdate) Mutation() *ScreenplayMutation {
	return su.mutation
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (su *ScreenplayUpdate) Save(ctx context.Context) (int, error) {
	su.defaults()
	return withHooks(ctx, su.sqlSave, su.mutation, su.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (su *ScreenplayUpdate) SaveX(ctx context.Context) int {
	affected, err := su.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (su *ScreenplayUpdate) Exec(ctx context.Context) error {
	_, err := su.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (su *ScreenplayUpdate) ExecX(ctx context.Context) {
	if err := su.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (su *ScreenplayUpdate) defaults() {
	if _, ok := su.mutation.UpdatedAt(); !ok {
		v := screenplay.UpdateDefaultUpdatedAt()
		su.mutation.SetUpdatedAt(v)
	}
}

func (su *ScreenplayUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(screenplay.Table, screenplay.Columns, sqlgraph.NewFieldSpec(screenplay.FieldID, field.TypeInt))
	if ps := su.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := su.mutation.UpdatedAt(); ok {
		_spec.SetField(screenplay.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := su.mutation.UserID(); ok {
		_spec.SetField(screenplay.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := su.mutation.AddedUserID(); ok {
		_spec.AddField(screenplay.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := su.mutation.ProjectID(); ok {
		_spec.SetField(screenplay.FieldProjectID, field.TypeInt64, value)
	}
	if value, ok := su.mutation.AddedProjectID(); ok {
		_spec.AddField(screenplay.FieldProjectID, field.TypeInt64, value)
	}
	if value, ok := su.mutation.Title(); ok {
		_spec.SetField(screenplay.FieldTitle, field.TypeString, value)
	}
	if value, ok := su.mutation.Content(); ok {
		_spec.SetField(screenplay.FieldContent, field.TypeString, value)
	}
	if value, ok := su.mutation.GetType(); ok {
		_spec.SetField(screenplay.FieldType, field.TypeInt8, value)
	}
	if value, ok := su.mutation.AddedType(); ok {
		_spec.AddField(screenplay.FieldType, field.TypeInt8, value)
	}
	if value, ok := su.mutation.Status(); ok {
		_spec.SetField(screenplay.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := su.mutation.AddedStatus(); ok {
		_spec.AddField(screenplay.FieldStatus, field.TypeInt8, value)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, su.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{screenplay.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	su.mutation.done = true
	return n, nil
}

// ScreenplayUpdateOne is the builder for updating a single Screenplay entity.
type ScreenplayUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ScreenplayMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (suo *ScreenplayUpdateOne) SetUpdatedAt(t time.Time) *ScreenplayUpdateOne {
	suo.mutation.SetUpdatedAt(t)
	return suo
}

// SetUserID sets the "user_id" field.
func (suo *ScreenplayUpdateOne) SetUserID(i int64) *ScreenplayUpdateOne {
	suo.mutation.ResetUserID()
	suo.mutation.SetUserID(i)
	return suo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableUserID(i *int64) *ScreenplayUpdateOne {
	if i != nil {
		suo.SetUserID(*i)
	}
	return suo
}

// AddUserID adds i to the "user_id" field.
func (suo *ScreenplayUpdateOne) AddUserID(i int64) *ScreenplayUpdateOne {
	suo.mutation.AddUserID(i)
	return suo
}

// SetProjectID sets the "project_id" field.
func (suo *ScreenplayUpdateOne) SetProjectID(i int64) *ScreenplayUpdateOne {
	suo.mutation.ResetProjectID()
	suo.mutation.SetProjectID(i)
	return suo
}

// SetNillableProjectID sets the "project_id" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableProjectID(i *int64) *ScreenplayUpdateOne {
	if i != nil {
		suo.SetProjectID(*i)
	}
	return suo
}

// AddProjectID adds i to the "project_id" field.
func (suo *ScreenplayUpdateOne) AddProjectID(i int64) *ScreenplayUpdateOne {
	suo.mutation.AddProjectID(i)
	return suo
}

// SetTitle sets the "title" field.
func (suo *ScreenplayUpdateOne) SetTitle(s string) *ScreenplayUpdateOne {
	suo.mutation.SetTitle(s)
	return suo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableTitle(s *string) *ScreenplayUpdateOne {
	if s != nil {
		suo.SetTitle(*s)
	}
	return suo
}

// SetContent sets the "content" field.
func (suo *ScreenplayUpdateOne) SetContent(s string) *ScreenplayUpdateOne {
	suo.mutation.SetContent(s)
	return suo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableContent(s *string) *ScreenplayUpdateOne {
	if s != nil {
		suo.SetContent(*s)
	}
	return suo
}

// SetType sets the "type" field.
func (suo *ScreenplayUpdateOne) SetType(i int8) *ScreenplayUpdateOne {
	suo.mutation.ResetType()
	suo.mutation.SetType(i)
	return suo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableType(i *int8) *ScreenplayUpdateOne {
	if i != nil {
		suo.SetType(*i)
	}
	return suo
}

// AddType adds i to the "type" field.
func (suo *ScreenplayUpdateOne) AddType(i int8) *ScreenplayUpdateOne {
	suo.mutation.AddType(i)
	return suo
}

// SetStatus sets the "status" field.
func (suo *ScreenplayUpdateOne) SetStatus(i int8) *ScreenplayUpdateOne {
	suo.mutation.ResetStatus()
	suo.mutation.SetStatus(i)
	return suo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (suo *ScreenplayUpdateOne) SetNillableStatus(i *int8) *ScreenplayUpdateOne {
	if i != nil {
		suo.SetStatus(*i)
	}
	return suo
}

// AddStatus adds i to the "status" field.
func (suo *ScreenplayUpdateOne) AddStatus(i int8) *ScreenplayUpdateOne {
	suo.mutation.AddStatus(i)
	return suo
}

// Mutation returns the ScreenplayMutation object of the builder.
func (suo *ScreenplayUpdateOne) Mutation() *ScreenplayMutation {
	return suo.mutation
}

// Where appends a list predicates to the ScreenplayUpdate builder.
func (suo *ScreenplayUpdateOne) Where(ps ...predicate.Screenplay) *ScreenplayUpdateOne {
	suo.mutation.Where(ps...)
	return suo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (suo *ScreenplayUpdateOne) Select(field string, fields ...string) *ScreenplayUpdateOne {
	suo.fields = append([]string{field}, fields...)
	return suo
}

// Save executes the query and returns the updated Screenplay entity.
func (suo *ScreenplayUpdateOne) Save(ctx context.Context) (*Screenplay, error) {
	suo.defaults()
	return withHooks(ctx, suo.sqlSave, suo.mutation, suo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (suo *ScreenplayUpdateOne) SaveX(ctx context.Context) *Screenplay {
	node, err := suo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (suo *ScreenplayUpdateOne) Exec(ctx context.Context) error {
	_, err := suo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (suo *ScreenplayUpdateOne) ExecX(ctx context.Context) {
	if err := suo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (suo *ScreenplayUpdateOne) defaults() {
	if _, ok := suo.mutation.UpdatedAt(); !ok {
		v := screenplay.UpdateDefaultUpdatedAt()
		suo.mutation.SetUpdatedAt(v)
	}
}

func (suo *ScreenplayUpdateOne) sqlSave(ctx context.Context) (_node *Screenplay, err error) {
	_spec := sqlgraph.NewUpdateSpec(screenplay.Table, screenplay.Columns, sqlgraph.NewFieldSpec(screenplay.FieldID, field.TypeInt))
	id, ok := suo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Screenplay.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := suo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, screenplay.FieldID)
		for _, f := range fields {
			if !screenplay.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != screenplay.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := suo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := suo.mutation.UpdatedAt(); ok {
		_spec.SetField(screenplay.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := suo.mutation.UserID(); ok {
		_spec.SetField(screenplay.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.AddedUserID(); ok {
		_spec.AddField(screenplay.FieldUserID, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.ProjectID(); ok {
		_spec.SetField(screenplay.FieldProjectID, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.AddedProjectID(); ok {
		_spec.AddField(screenplay.FieldProjectID, field.TypeInt64, value)
	}
	if value, ok := suo.mutation.Title(); ok {
		_spec.SetField(screenplay.FieldTitle, field.TypeString, value)
	}
	if value, ok := suo.mutation.Content(); ok {
		_spec.SetField(screenplay.FieldContent, field.TypeString, value)
	}
	if value, ok := suo.mutation.GetType(); ok {
		_spec.SetField(screenplay.FieldType, field.TypeInt8, value)
	}
	if value, ok := suo.mutation.AddedType(); ok {
		_spec.AddField(screenplay.FieldType, field.TypeInt8, value)
	}
	if value, ok := suo.mutation.Status(); ok {
		_spec.SetField(screenplay.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := suo.mutation.AddedStatus(); ok {
		_spec.AddField(screenplay.FieldStatus, field.TypeInt8, value)
	}
	_node = &Screenplay{config: suo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, suo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{screenplay.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	suo.mutation.done = true
	return _node, nil
}
