// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserInternalMessageQuery is the builder for querying UserInternalMessage entities.
type UserInternalMessageQuery struct {
	config
	ctx         *QueryContext
	order       []userinternalmessage.OrderOption
	inters      []Interceptor
	predicates  []predicate.UserInternalMessage
	withMessage *InternalMessageQuery
	withUser    *UserQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the UserInternalMessageQuery builder.
func (uimq *UserInternalMessageQuery) Where(ps ...predicate.UserInternalMessage) *UserInternalMessageQuery {
	uimq.predicates = append(uimq.predicates, ps...)
	return uimq
}

// Limit the number of records to be returned by this query.
func (uimq *UserInternalMessageQuery) Limit(limit int) *UserInternalMessageQuery {
	uimq.ctx.Limit = &limit
	return uimq
}

// Offset to start from.
func (uimq *UserInternalMessageQuery) Offset(offset int) *UserInternalMessageQuery {
	uimq.ctx.Offset = &offset
	return uimq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (uimq *UserInternalMessageQuery) Unique(unique bool) *UserInternalMessageQuery {
	uimq.ctx.Unique = &unique
	return uimq
}

// Order specifies how the records should be ordered.
func (uimq *UserInternalMessageQuery) Order(o ...userinternalmessage.OrderOption) *UserInternalMessageQuery {
	uimq.order = append(uimq.order, o...)
	return uimq
}

// QueryMessage chains the current query on the "message" edge.
func (uimq *UserInternalMessageQuery) QueryMessage() *InternalMessageQuery {
	query := (&InternalMessageClient{config: uimq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uimq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uimq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(userinternalmessage.Table, userinternalmessage.FieldID, selector),
			sqlgraph.To(internalmessage.Table, internalmessage.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, userinternalmessage.MessageTable, userinternalmessage.MessageColumn),
		)
		fromU = sqlgraph.SetNeighbors(uimq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (uimq *UserInternalMessageQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: uimq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := uimq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := uimq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(userinternalmessage.Table, userinternalmessage.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, userinternalmessage.UserTable, userinternalmessage.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(uimq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first UserInternalMessage entity from the query.
// Returns a *NotFoundError when no UserInternalMessage was found.
func (uimq *UserInternalMessageQuery) First(ctx context.Context) (*UserInternalMessage, error) {
	nodes, err := uimq.Limit(1).All(setContextOp(ctx, uimq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{userinternalmessage.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) FirstX(ctx context.Context) *UserInternalMessage {
	node, err := uimq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first UserInternalMessage ID from the query.
// Returns a *NotFoundError when no UserInternalMessage ID was found.
func (uimq *UserInternalMessageQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uimq.Limit(1).IDs(setContextOp(ctx, uimq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{userinternalmessage.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) FirstIDX(ctx context.Context) int {
	id, err := uimq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single UserInternalMessage entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one UserInternalMessage entity is found.
// Returns a *NotFoundError when no UserInternalMessage entities are found.
func (uimq *UserInternalMessageQuery) Only(ctx context.Context) (*UserInternalMessage, error) {
	nodes, err := uimq.Limit(2).All(setContextOp(ctx, uimq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{userinternalmessage.Label}
	default:
		return nil, &NotSingularError{userinternalmessage.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) OnlyX(ctx context.Context) *UserInternalMessage {
	node, err := uimq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only UserInternalMessage ID in the query.
// Returns a *NotSingularError when more than one UserInternalMessage ID is found.
// Returns a *NotFoundError when no entities are found.
func (uimq *UserInternalMessageQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = uimq.Limit(2).IDs(setContextOp(ctx, uimq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{userinternalmessage.Label}
	default:
		err = &NotSingularError{userinternalmessage.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) OnlyIDX(ctx context.Context) int {
	id, err := uimq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of UserInternalMessages.
func (uimq *UserInternalMessageQuery) All(ctx context.Context) ([]*UserInternalMessage, error) {
	ctx = setContextOp(ctx, uimq.ctx, ent.OpQueryAll)
	if err := uimq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*UserInternalMessage, *UserInternalMessageQuery]()
	return withInterceptors[[]*UserInternalMessage](ctx, uimq, qr, uimq.inters)
}

// AllX is like All, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) AllX(ctx context.Context) []*UserInternalMessage {
	nodes, err := uimq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of UserInternalMessage IDs.
func (uimq *UserInternalMessageQuery) IDs(ctx context.Context) (ids []int, err error) {
	if uimq.ctx.Unique == nil && uimq.path != nil {
		uimq.Unique(true)
	}
	ctx = setContextOp(ctx, uimq.ctx, ent.OpQueryIDs)
	if err = uimq.Select(userinternalmessage.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) IDsX(ctx context.Context) []int {
	ids, err := uimq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (uimq *UserInternalMessageQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, uimq.ctx, ent.OpQueryCount)
	if err := uimq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, uimq, querierCount[*UserInternalMessageQuery](), uimq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) CountX(ctx context.Context) int {
	count, err := uimq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (uimq *UserInternalMessageQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, uimq.ctx, ent.OpQueryExist)
	switch _, err := uimq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (uimq *UserInternalMessageQuery) ExistX(ctx context.Context) bool {
	exist, err := uimq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the UserInternalMessageQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (uimq *UserInternalMessageQuery) Clone() *UserInternalMessageQuery {
	if uimq == nil {
		return nil
	}
	return &UserInternalMessageQuery{
		config:      uimq.config,
		ctx:         uimq.ctx.Clone(),
		order:       append([]userinternalmessage.OrderOption{}, uimq.order...),
		inters:      append([]Interceptor{}, uimq.inters...),
		predicates:  append([]predicate.UserInternalMessage{}, uimq.predicates...),
		withMessage: uimq.withMessage.Clone(),
		withUser:    uimq.withUser.Clone(),
		// clone intermediate query.
		sql:  uimq.sql.Clone(),
		path: uimq.path,
	}
}

// WithMessage tells the query-builder to eager-load the nodes that are connected to
// the "message" edge. The optional arguments are used to configure the query builder of the edge.
func (uimq *UserInternalMessageQuery) WithMessage(opts ...func(*InternalMessageQuery)) *UserInternalMessageQuery {
	query := (&InternalMessageClient{config: uimq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uimq.withMessage = query
	return uimq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (uimq *UserInternalMessageQuery) WithUser(opts ...func(*UserQuery)) *UserInternalMessageQuery {
	query := (&UserClient{config: uimq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	uimq.withUser = query
	return uimq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.UserInternalMessage.Query().
//		GroupBy(userinternalmessage.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (uimq *UserInternalMessageQuery) GroupBy(field string, fields ...string) *UserInternalMessageGroupBy {
	uimq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &UserInternalMessageGroupBy{build: uimq}
	grbuild.flds = &uimq.ctx.Fields
	grbuild.label = userinternalmessage.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.UserInternalMessage.Query().
//		Select(userinternalmessage.FieldCreatedAt).
//		Scan(ctx, &v)
func (uimq *UserInternalMessageQuery) Select(fields ...string) *UserInternalMessageSelect {
	uimq.ctx.Fields = append(uimq.ctx.Fields, fields...)
	sbuild := &UserInternalMessageSelect{UserInternalMessageQuery: uimq}
	sbuild.label = userinternalmessage.Label
	sbuild.flds, sbuild.scan = &uimq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a UserInternalMessageSelect configured with the given aggregations.
func (uimq *UserInternalMessageQuery) Aggregate(fns ...AggregateFunc) *UserInternalMessageSelect {
	return uimq.Select().Aggregate(fns...)
}

func (uimq *UserInternalMessageQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range uimq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, uimq); err != nil {
				return err
			}
		}
	}
	for _, f := range uimq.ctx.Fields {
		if !userinternalmessage.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if uimq.path != nil {
		prev, err := uimq.path(ctx)
		if err != nil {
			return err
		}
		uimq.sql = prev
	}
	return nil
}

func (uimq *UserInternalMessageQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*UserInternalMessage, error) {
	var (
		nodes       = []*UserInternalMessage{}
		_spec       = uimq.querySpec()
		loadedTypes = [2]bool{
			uimq.withMessage != nil,
			uimq.withUser != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*UserInternalMessage).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &UserInternalMessage{config: uimq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, uimq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := uimq.withMessage; query != nil {
		if err := uimq.loadMessage(ctx, query, nodes, nil,
			func(n *UserInternalMessage, e *InternalMessage) { n.Edges.Message = e }); err != nil {
			return nil, err
		}
	}
	if query := uimq.withUser; query != nil {
		if err := uimq.loadUser(ctx, query, nodes, nil,
			func(n *UserInternalMessage, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (uimq *UserInternalMessageQuery) loadMessage(ctx context.Context, query *InternalMessageQuery, nodes []*UserInternalMessage, init func(*UserInternalMessage), assign func(*UserInternalMessage, *InternalMessage)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*UserInternalMessage)
	for i := range nodes {
		fk := nodes[i].InternalMessageID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(internalmessage.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "internal_message_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (uimq *UserInternalMessageQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*UserInternalMessage, init func(*UserInternalMessage), assign func(*UserInternalMessage, *User)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*UserInternalMessage)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (uimq *UserInternalMessageQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := uimq.querySpec()
	_spec.Node.Columns = uimq.ctx.Fields
	if len(uimq.ctx.Fields) > 0 {
		_spec.Unique = uimq.ctx.Unique != nil && *uimq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, uimq.driver, _spec)
}

func (uimq *UserInternalMessageQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(userinternalmessage.Table, userinternalmessage.Columns, sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt))
	_spec.From = uimq.sql
	if unique := uimq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if uimq.path != nil {
		_spec.Unique = true
	}
	if fields := uimq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, userinternalmessage.FieldID)
		for i := range fields {
			if fields[i] != userinternalmessage.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if uimq.withMessage != nil {
			_spec.Node.AddColumnOnce(userinternalmessage.FieldInternalMessageID)
		}
		if uimq.withUser != nil {
			_spec.Node.AddColumnOnce(userinternalmessage.FieldUserID)
		}
	}
	if ps := uimq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := uimq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := uimq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := uimq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (uimq *UserInternalMessageQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(uimq.driver.Dialect())
	t1 := builder.Table(userinternalmessage.Table)
	columns := uimq.ctx.Fields
	if len(columns) == 0 {
		columns = userinternalmessage.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if uimq.sql != nil {
		selector = uimq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if uimq.ctx.Unique != nil && *uimq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range uimq.predicates {
		p(selector)
	}
	for _, p := range uimq.order {
		p(selector)
	}
	if offset := uimq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := uimq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// UserInternalMessageGroupBy is the group-by builder for UserInternalMessage entities.
type UserInternalMessageGroupBy struct {
	selector
	build *UserInternalMessageQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (uimgb *UserInternalMessageGroupBy) Aggregate(fns ...AggregateFunc) *UserInternalMessageGroupBy {
	uimgb.fns = append(uimgb.fns, fns...)
	return uimgb
}

// Scan applies the selector query and scans the result into the given value.
func (uimgb *UserInternalMessageGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uimgb.build.ctx, ent.OpQueryGroupBy)
	if err := uimgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserInternalMessageQuery, *UserInternalMessageGroupBy](ctx, uimgb.build, uimgb, uimgb.build.inters, v)
}

func (uimgb *UserInternalMessageGroupBy) sqlScan(ctx context.Context, root *UserInternalMessageQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(uimgb.fns))
	for _, fn := range uimgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*uimgb.flds)+len(uimgb.fns))
		for _, f := range *uimgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*uimgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uimgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// UserInternalMessageSelect is the builder for selecting fields of UserInternalMessage entities.
type UserInternalMessageSelect struct {
	*UserInternalMessageQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (uims *UserInternalMessageSelect) Aggregate(fns ...AggregateFunc) *UserInternalMessageSelect {
	uims.fns = append(uims.fns, fns...)
	return uims
}

// Scan applies the selector query and scans the result into the given value.
func (uims *UserInternalMessageSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, uims.ctx, ent.OpQuerySelect)
	if err := uims.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*UserInternalMessageQuery, *UserInternalMessageSelect](ctx, uims.UserInternalMessageQuery, uims, uims.inters, v)
}

func (uims *UserInternalMessageSelect) sqlScan(ctx context.Context, root *UserInternalMessageQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(uims.fns))
	for _, fn := range uims.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*uims.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := uims.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
