package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// Batch holds the schema definition for the Batch entity.
type Batch struct {
	ent.Schema
}

func (Batch) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (Batch) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("批次表"),
	}
}

// Fields of the Batch.
func (Batch) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Unique().Comment("批次名称"),
		field.Int64("num").Comment("测试码生成数量"),
		field.Time("expired_at").SchemaType(map[string]string{
			dialect.MySQL: "DATETIME", // Override MySQL.
		}).Optional().Comment("过期时间"),
	}
}

// Edges of the Batch.
func (Batch) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("codes", TestCode.Type),
		edge.To("users", User.Type),
	}
}
