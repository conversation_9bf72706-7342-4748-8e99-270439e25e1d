package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// TestCode holds the schema definition for the TestCode entity.
type TestCode struct {
	ent.Schema
}

func (TestCode) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (TestCode) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("测试码表"),
	}
}

// Fields of the TestCode.
func (TestCode) Fields() []ent.Field {
	return []ent.Field{
		field.Int("batch_id").Comment("批次id"),
		field.String("code").SchemaType(map[string]string{
			dialect.MySQL: "varchar(10)", // Override MySQL.
		}).Unique().Comment("测试码"),
		field.String("device").SchemaType(map[string]string{
			dialect.MySQL: "varchar(32)", // Override MySQL.
		}).Unique().Optional().Comment("设备id"),
		field.Int8("status").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("状态:1默认；2已领取 3 已使用"),
		field.Time("expired_at").SchemaType(map[string]string{
			dialect.MySQL: "DATETIME", // Override MySQL.
		}).Optional().Comment("过期时间"),
	}
}

// Edges of the TestCode.
func (TestCode) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("user", User.Type).Unique(),
		edge.From("batch", Batch.Type).Ref("codes").Field("batch_id").Required().Unique(),
	}

}
