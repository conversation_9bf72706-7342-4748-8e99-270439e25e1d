package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/schema/field"
)

// FrequentAskQuestion holds the schema definition for the FrequentAskQuestion entity.
type FrequentAskQuestion struct {
	ent.Schema
}

func (FrequentAskQuestion) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
	}
}

// Fields of the FrequentAskQuestion.
func (FrequentAskQuestion) Fields() []ent.Field {
	return []ent.Field{
		field.String("content").SchemaType(map[string]string{
			dialect.MySQL: "longtext", // Override MySQL.
		}).Comment("内容"),
	}
}

// Edges of the FrequentAskQuestion.
func (FrequentAskQuestion) Edges() []ent.Edge {
	return nil
}
