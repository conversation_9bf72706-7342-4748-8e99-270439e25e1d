package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// AiAgent holds the schema definition for the AiAgent entity.
type AiAgent struct {
	ent.Schema
}

func (AiAgent) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (AiAgent) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("AI智能体表"),
	}
}

type AiAgentParam struct {
	Field       string `json:"field"`
	Description string `json:"description"`
	Type        string `json:"type"`
}

// Fields of the AiAgent.
func (AiAgent) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Unique().Comment("名称"),
		field.String("icon").SchemaType(map[string]string{
			dialect.MySQL: "longtext", // Override MySQL.
		}).Optional().Comment("图标"),
		field.String("description").Optional().Comment("描述"),
		field.String("target").Comment("target"),
		field.String("guide").Optional().Comment("引导"),
		field.String("secret").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Comment("secret"),
		field.String("method").SchemaType(map[string]string{
			dialect.MySQL: "varchar(20)", // Override MySQL.
		}).Comment("http method"),
		field.JSON("inputs", []AiAgentParam{}).Optional().Comment("inputs参数"),
		field.Int8("status").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("状态:1默认；2下线"),
	}
}

// Edges of the AiAgent.
func (AiAgent) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("conversations", Conversation.Type),
	}
}
