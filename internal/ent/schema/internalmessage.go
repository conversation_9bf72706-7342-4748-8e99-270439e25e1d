package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// InternalMessage holds the schema definition for the InternalMessage entity.
type InternalMessage struct {
	ent.Schema
}

func (InternalMessage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (InternalMessage) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("站内信"),
	}
}

// Fields of the InternalMessage.
func (InternalMessage) Fields() []ent.Field {
	return []ent.Field{
		field.String("title").SchemaType(map[string]string{
			dialect.MySQL: "varchar(32)", // Override MySQL.
		}).Unique().Optional().Comment("标题"),
		field.String("content").SchemaType(map[string]string{
			dialect.MySQL: "text", // Override MySQL.
		}).Comment("内容"),
	}
}

// Edges of the InternalMessage.
func (InternalMessage) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("users", UserInternalMessage.Type),
	}
}
