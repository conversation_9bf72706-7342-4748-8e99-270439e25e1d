package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

// Screenplay holds the schema definition for the Screenplay entity.
type Screenplay struct {
	ent.Schema
}

func (Screenplay) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (Screenplay) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("剧本表"),
	}
}

// Fields of the Project.
func (Screenplay) Fields() []ent.Field {
	return []ent.Field{
		field.Int64("user_id").SchemaType(map[string]string{
			dialect.MySQL: "int(10)unsigned", // Override MySQL.
		}).Comment("用户id"),
		field.Int64("project_id").SchemaType(map[string]string{
			dialect.MySQL: "int(10)unsigned", // Override MySQL.
		}).Comment("用户id"),
		field.String("title").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Comment("名称"),
		field.Text("content").SchemaType(map[string]string{
			dialect.MySQL: "longtext", // Override MySQL.
		}).Comment("内容").StructTag(`json:"content"`),
		field.Int8("type").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("类型 1 大纲 2 剧本 3 最终剧本"),
		field.Int8("status").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("状态:1默认；2发布 3 废弃"),
	}
}

// Edges of the Project.
func (Screenplay) Edges() []ent.Edge {
	return nil
}
