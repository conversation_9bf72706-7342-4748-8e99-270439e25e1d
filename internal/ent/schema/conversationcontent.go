package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// ConversationContent holds the schema definition for the ConversationContent entity.
type ConversationContent struct {
	ent.Schema
}

func (ConversationContent) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
	}
}

func (ConversationContent) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("智能体会话详情表"),
	}
}

// Fields of the ConversationContent.
func (ConversationContent) Fields() []ent.Field {
	return []ent.Field{
		field.Int("conversation_id").Comment("conversations表id"),
		field.String("content").SchemaType(map[string]string{
			dialect.MySQL: "text", // Override MySQL.
		}).Comment("会话内容"),
		field.Int8("type").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("类型:1 用户; 2 agent"),
	}
}

func (ConversationContent) Indexes() []ent.Index {
	return []ent.Index{
		// non-unique index.
		index.Fields("conversation_id"),
	}
}

// Edges of the ConversationContent.
func (ConversationContent) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("conversation", Conversation.Type).
			Ref("contents").
			Field("conversation_id").
			Unique().
			Required(),
	}
}
