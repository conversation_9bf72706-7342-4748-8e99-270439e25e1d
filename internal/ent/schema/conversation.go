package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
)

// Conversation holds the schema definition for the Conversation entity.
type Conversation struct {
	ent.Schema
}

func (Conversation) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (Conversation) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("智能体会话表"),
	}
}

// Fields of the Conversation.
func (Conversation) Fields() []ent.Field {
	return []ent.Field{
		field.Int("ai_agent_id").Comment("智能体id"),
		field.Int("user_id").Comment("用户id"),
		field.String("ai_agent_conversation_id").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Comment("智能体会话id"),
		field.String("name").SchemaType(map[string]string{
			dialect.MySQL: "varchar(100)", // Override MySQL.
		}).Comment("会话记录名称"),
	}
}

func (Conversation) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("ai_agent_id"),
		index.Fields("user_id"),
		index.Fields("ai_agent_conversation_id"),
	}
}

// Edges of the Conversation.
func (Conversation) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("contents", ConversationContent.Type),
		edge.From("ai_agent", AiAgent.Type).
			Ref("conversations").
			Field("ai_agent_id").
			Required().
			Unique(),
	}
}
