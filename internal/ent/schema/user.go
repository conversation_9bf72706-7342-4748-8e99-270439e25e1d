package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

func (User) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (User) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("用户表"),
	}
}

// User holds the schema definition for the User entity.
type User struct {
	ent.Schema
}

// Fields of the User.
func (User) Fields() []ent.Field {
	return []ent.Field{
		field.Int("batch_id"),
		field.Int("test_code_id"),
		field.String("username").SchemaType(map[string]string{
			dialect.MySQL: "varchar(11)", // Override MySQL.
		}).Unique().Comment("账号"),
		field.String("password").Annotations(entsql.Annotation{
			Size: 100,
		}).Sensitive().Comment("密码"),
		field.String("email").Annotations(entsql.Annotation{
			Size: 50,
		}).Comment("邮箱").Optional(),
		field.Int8("status").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("状态:1默认；2 禁用"),
	}
}

// Edges of the User.
func (User) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("code", TestCode.Type).Field("test_code_id").Ref("user").Required().Unique(),
		edge.From("batch", Batch.Type).Field("batch_id").Ref("users").Required().Unique(),
		edge.To("messages", UserInternalMessage.Type),
	}
}
