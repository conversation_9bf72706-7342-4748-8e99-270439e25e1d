package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

// UserInternalMessage holds the schema definition for the UserInternalMessage entity.
type UserInternalMessage struct {
	ent.Schema
}

func (UserInternalMessage) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (UserInternalMessage) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("站内信"),
	}
}

// Fields of the UserInternalMessage.
func (UserInternalMessage) Fields() []ent.Field {
	return []ent.Field{
		field.Int("internal_message_id").Comment("站内信id"),
		field.Int("user_id").Comment("用户id"),
		field.Int8("status").SchemaType(map[string]string{
			dialect.MySQL: "tinyint(4)", // Override MySQL.
		}).Default(1).Comment("状态:1未读；2 已读"),
	}
}

// Edges of the UserInternalMessage.
func (UserInternalMessage) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("message", InternalMessage.Type).
			Ref("users").
			Field("internal_message_id").
			Unique().
			Required(),

		edge.From("user", User.Type).
			Ref("messages").
			Field("user_id").
			Unique().
			Required(),
	}
}
