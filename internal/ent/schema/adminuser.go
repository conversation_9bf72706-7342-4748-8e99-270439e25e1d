package schema

import (
	"bole-ai/internal/ent/mixin"
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

// AdminUser holds the schema definition for the AdminUser entity.
type AdminUser struct {
	ent.Schema
}

func (AdminUser) Mixin() []ent.Mixin {
	return []ent.Mixin{
		mixin.TimeMixin{},
		mixin.SoftDeleteMixin{},
	}
}

func (AdminUser) Annotations() []schema.Annotation {
	return []schema.Annotation{
		// Adding this annotation to the schema enables
		// comments for the table and all its fields.
		entsql.WithComments(true),
		schema.Comment("管理后台用户表"),
	}
}

// Fields of the AdminUser.
func (AdminUser) Fields() []ent.Field {
	return []ent.Field{
		field.String("username").SchemaType(map[string]string{
			dialect.MySQL: "varchar(11)", // Override MySQL.
		}).Unique().Comment("账号"),
		field.String("password").Annotations(entsql.Annotation{
			Size: 100,
		}).Sensitive().Comment("密码"),
	}
}

// Edges of the AdminUser.
func (AdminUser) Edges() []ent.Edge {
	return nil
}
