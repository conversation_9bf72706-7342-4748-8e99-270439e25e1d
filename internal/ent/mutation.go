// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/adminuser"
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/schema"
	"bole-ai/internal/ent/screenplay"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

const (
	// Operation types.
	OpCreate    = ent.OpCreate
	OpDelete    = ent.OpDelete
	OpDeleteOne = ent.OpDeleteOne
	OpUpdate    = ent.OpUpdate
	OpUpdateOne = ent.OpUpdateOne

	// Node types.
	TypeAdminUser           = "AdminUser"
	TypeAiAgent             = "AiAgent"
	TypeBatch               = "Batch"
	TypeConversation        = "Conversation"
	TypeConversationContent = "ConversationContent"
	TypeFrequentAskQuestion = "FrequentAskQuestion"
	TypeInternalMessage     = "InternalMessage"
	TypeProject             = "Project"
	TypeScreenplay          = "Screenplay"
	TypeTestCode            = "TestCode"
	TypeUser                = "User"
	TypeUserInternalMessage = "UserInternalMessage"
)

// AdminUserMutation represents an operation that mutates the AdminUser nodes in the graph.
type AdminUserMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	username      *string
	password      *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*AdminUser, error)
	predicates    []predicate.AdminUser
}

var _ ent.Mutation = (*AdminUserMutation)(nil)

// adminuserOption allows management of the mutation configuration using functional options.
type adminuserOption func(*AdminUserMutation)

// newAdminUserMutation creates new mutation for the AdminUser entity.
func newAdminUserMutation(c config, op Op, opts ...adminuserOption) *AdminUserMutation {
	m := &AdminUserMutation{
		config:        c,
		op:            op,
		typ:           TypeAdminUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAdminUserID sets the ID field of the mutation.
func withAdminUserID(id int) adminuserOption {
	return func(m *AdminUserMutation) {
		var (
			err   error
			once  sync.Once
			value *AdminUser
		)
		m.oldValue = func(ctx context.Context) (*AdminUser, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AdminUser.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAdminUser sets the old AdminUser of the mutation.
func withAdminUser(node *AdminUser) adminuserOption {
	return func(m *AdminUserMutation) {
		m.oldValue = func(context.Context) (*AdminUser, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AdminUserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AdminUserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AdminUserMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AdminUserMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AdminUser.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AdminUserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AdminUserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AdminUser entity.
// If the AdminUser object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdminUserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AdminUserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AdminUserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AdminUserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the AdminUser entity.
// If the AdminUser object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdminUserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AdminUserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetUsername sets the "username" field.
func (m *AdminUserMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *AdminUserMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the AdminUser entity.
// If the AdminUser object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdminUserMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ResetUsername resets all changes to the "username" field.
func (m *AdminUserMutation) ResetUsername() {
	m.username = nil
}

// SetPassword sets the "password" field.
func (m *AdminUserMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *AdminUserMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the AdminUser entity.
// If the AdminUser object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AdminUserMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *AdminUserMutation) ResetPassword() {
	m.password = nil
}

// Where appends a list predicates to the AdminUserMutation builder.
func (m *AdminUserMutation) Where(ps ...predicate.AdminUser) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AdminUserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AdminUserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AdminUser, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AdminUserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AdminUserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AdminUser).
func (m *AdminUserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AdminUserMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.created_at != nil {
		fields = append(fields, adminuser.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, adminuser.FieldUpdatedAt)
	}
	if m.username != nil {
		fields = append(fields, adminuser.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, adminuser.FieldPassword)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AdminUserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case adminuser.FieldCreatedAt:
		return m.CreatedAt()
	case adminuser.FieldUpdatedAt:
		return m.UpdatedAt()
	case adminuser.FieldUsername:
		return m.Username()
	case adminuser.FieldPassword:
		return m.Password()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AdminUserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case adminuser.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case adminuser.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case adminuser.FieldUsername:
		return m.OldUsername(ctx)
	case adminuser.FieldPassword:
		return m.OldPassword(ctx)
	}
	return nil, fmt.Errorf("unknown AdminUser field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AdminUserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case adminuser.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case adminuser.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case adminuser.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case adminuser.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	}
	return fmt.Errorf("unknown AdminUser field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AdminUserMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AdminUserMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AdminUserMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown AdminUser numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AdminUserMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AdminUserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AdminUserMutation) ClearField(name string) error {
	return fmt.Errorf("unknown AdminUser nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AdminUserMutation) ResetField(name string) error {
	switch name {
	case adminuser.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case adminuser.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case adminuser.FieldUsername:
		m.ResetUsername()
		return nil
	case adminuser.FieldPassword:
		m.ResetPassword()
		return nil
	}
	return fmt.Errorf("unknown AdminUser field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AdminUserMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AdminUserMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AdminUserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AdminUserMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AdminUserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AdminUserMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AdminUserMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown AdminUser unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AdminUserMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown AdminUser edge %s", name)
}

// AiAgentMutation represents an operation that mutates the AiAgent nodes in the graph.
type AiAgentMutation struct {
	config
	op                   Op
	typ                  string
	id                   *int
	created_at           *time.Time
	updated_at           *time.Time
	name                 *string
	icon                 *string
	description          *string
	target               *string
	guide                *string
	secret               *string
	method               *string
	inputs               *[]schema.AiAgentParam
	appendinputs         []schema.AiAgentParam
	status               *int8
	addstatus            *int8
	clearedFields        map[string]struct{}
	conversations        map[int]struct{}
	removedconversations map[int]struct{}
	clearedconversations bool
	done                 bool
	oldValue             func(context.Context) (*AiAgent, error)
	predicates           []predicate.AiAgent
}

var _ ent.Mutation = (*AiAgentMutation)(nil)

// aiagentOption allows management of the mutation configuration using functional options.
type aiagentOption func(*AiAgentMutation)

// newAiAgentMutation creates new mutation for the AiAgent entity.
func newAiAgentMutation(c config, op Op, opts ...aiagentOption) *AiAgentMutation {
	m := &AiAgentMutation{
		config:        c,
		op:            op,
		typ:           TypeAiAgent,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withAiAgentID sets the ID field of the mutation.
func withAiAgentID(id int) aiagentOption {
	return func(m *AiAgentMutation) {
		var (
			err   error
			once  sync.Once
			value *AiAgent
		)
		m.oldValue = func(ctx context.Context) (*AiAgent, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().AiAgent.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withAiAgent sets the old AiAgent of the mutation.
func withAiAgent(node *AiAgent) aiagentOption {
	return func(m *AiAgentMutation) {
		m.oldValue = func(context.Context) (*AiAgent, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m AiAgentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m AiAgentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *AiAgentMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *AiAgentMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().AiAgent.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *AiAgentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *AiAgentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *AiAgentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *AiAgentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *AiAgentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *AiAgentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetName sets the "name" field.
func (m *AiAgentMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *AiAgentMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *AiAgentMutation) ResetName() {
	m.name = nil
}

// SetIcon sets the "icon" field.
func (m *AiAgentMutation) SetIcon(s string) {
	m.icon = &s
}

// Icon returns the value of the "icon" field in the mutation.
func (m *AiAgentMutation) Icon() (r string, exists bool) {
	v := m.icon
	if v == nil {
		return
	}
	return *v, true
}

// OldIcon returns the old "icon" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldIcon(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldIcon is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldIcon requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldIcon: %w", err)
	}
	return oldValue.Icon, nil
}

// ClearIcon clears the value of the "icon" field.
func (m *AiAgentMutation) ClearIcon() {
	m.icon = nil
	m.clearedFields[aiagent.FieldIcon] = struct{}{}
}

// IconCleared returns if the "icon" field was cleared in this mutation.
func (m *AiAgentMutation) IconCleared() bool {
	_, ok := m.clearedFields[aiagent.FieldIcon]
	return ok
}

// ResetIcon resets all changes to the "icon" field.
func (m *AiAgentMutation) ResetIcon() {
	m.icon = nil
	delete(m.clearedFields, aiagent.FieldIcon)
}

// SetDescription sets the "description" field.
func (m *AiAgentMutation) SetDescription(s string) {
	m.description = &s
}

// Description returns the value of the "description" field in the mutation.
func (m *AiAgentMutation) Description() (r string, exists bool) {
	v := m.description
	if v == nil {
		return
	}
	return *v, true
}

// OldDescription returns the old "description" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldDescription(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDescription is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDescription requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDescription: %w", err)
	}
	return oldValue.Description, nil
}

// ClearDescription clears the value of the "description" field.
func (m *AiAgentMutation) ClearDescription() {
	m.description = nil
	m.clearedFields[aiagent.FieldDescription] = struct{}{}
}

// DescriptionCleared returns if the "description" field was cleared in this mutation.
func (m *AiAgentMutation) DescriptionCleared() bool {
	_, ok := m.clearedFields[aiagent.FieldDescription]
	return ok
}

// ResetDescription resets all changes to the "description" field.
func (m *AiAgentMutation) ResetDescription() {
	m.description = nil
	delete(m.clearedFields, aiagent.FieldDescription)
}

// SetTarget sets the "target" field.
func (m *AiAgentMutation) SetTarget(s string) {
	m.target = &s
}

// Target returns the value of the "target" field in the mutation.
func (m *AiAgentMutation) Target() (r string, exists bool) {
	v := m.target
	if v == nil {
		return
	}
	return *v, true
}

// OldTarget returns the old "target" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldTarget(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTarget is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTarget requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTarget: %w", err)
	}
	return oldValue.Target, nil
}

// ResetTarget resets all changes to the "target" field.
func (m *AiAgentMutation) ResetTarget() {
	m.target = nil
}

// SetGuide sets the "guide" field.
func (m *AiAgentMutation) SetGuide(s string) {
	m.guide = &s
}

// Guide returns the value of the "guide" field in the mutation.
func (m *AiAgentMutation) Guide() (r string, exists bool) {
	v := m.guide
	if v == nil {
		return
	}
	return *v, true
}

// OldGuide returns the old "guide" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldGuide(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldGuide is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldGuide requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldGuide: %w", err)
	}
	return oldValue.Guide, nil
}

// ClearGuide clears the value of the "guide" field.
func (m *AiAgentMutation) ClearGuide() {
	m.guide = nil
	m.clearedFields[aiagent.FieldGuide] = struct{}{}
}

// GuideCleared returns if the "guide" field was cleared in this mutation.
func (m *AiAgentMutation) GuideCleared() bool {
	_, ok := m.clearedFields[aiagent.FieldGuide]
	return ok
}

// ResetGuide resets all changes to the "guide" field.
func (m *AiAgentMutation) ResetGuide() {
	m.guide = nil
	delete(m.clearedFields, aiagent.FieldGuide)
}

// SetSecret sets the "secret" field.
func (m *AiAgentMutation) SetSecret(s string) {
	m.secret = &s
}

// Secret returns the value of the "secret" field in the mutation.
func (m *AiAgentMutation) Secret() (r string, exists bool) {
	v := m.secret
	if v == nil {
		return
	}
	return *v, true
}

// OldSecret returns the old "secret" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldSecret(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldSecret is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldSecret requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldSecret: %w", err)
	}
	return oldValue.Secret, nil
}

// ResetSecret resets all changes to the "secret" field.
func (m *AiAgentMutation) ResetSecret() {
	m.secret = nil
}

// SetMethod sets the "method" field.
func (m *AiAgentMutation) SetMethod(s string) {
	m.method = &s
}

// Method returns the value of the "method" field in the mutation.
func (m *AiAgentMutation) Method() (r string, exists bool) {
	v := m.method
	if v == nil {
		return
	}
	return *v, true
}

// OldMethod returns the old "method" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldMethod(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldMethod is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldMethod requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldMethod: %w", err)
	}
	return oldValue.Method, nil
}

// ResetMethod resets all changes to the "method" field.
func (m *AiAgentMutation) ResetMethod() {
	m.method = nil
}

// SetInputs sets the "inputs" field.
func (m *AiAgentMutation) SetInputs(sap []schema.AiAgentParam) {
	m.inputs = &sap
	m.appendinputs = nil
}

// Inputs returns the value of the "inputs" field in the mutation.
func (m *AiAgentMutation) Inputs() (r []schema.AiAgentParam, exists bool) {
	v := m.inputs
	if v == nil {
		return
	}
	return *v, true
}

// OldInputs returns the old "inputs" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldInputs(ctx context.Context) (v []schema.AiAgentParam, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInputs is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInputs requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInputs: %w", err)
	}
	return oldValue.Inputs, nil
}

// AppendInputs adds sap to the "inputs" field.
func (m *AiAgentMutation) AppendInputs(sap []schema.AiAgentParam) {
	m.appendinputs = append(m.appendinputs, sap...)
}

// AppendedInputs returns the list of values that were appended to the "inputs" field in this mutation.
func (m *AiAgentMutation) AppendedInputs() ([]schema.AiAgentParam, bool) {
	if len(m.appendinputs) == 0 {
		return nil, false
	}
	return m.appendinputs, true
}

// ClearInputs clears the value of the "inputs" field.
func (m *AiAgentMutation) ClearInputs() {
	m.inputs = nil
	m.appendinputs = nil
	m.clearedFields[aiagent.FieldInputs] = struct{}{}
}

// InputsCleared returns if the "inputs" field was cleared in this mutation.
func (m *AiAgentMutation) InputsCleared() bool {
	_, ok := m.clearedFields[aiagent.FieldInputs]
	return ok
}

// ResetInputs resets all changes to the "inputs" field.
func (m *AiAgentMutation) ResetInputs() {
	m.inputs = nil
	m.appendinputs = nil
	delete(m.clearedFields, aiagent.FieldInputs)
}

// SetStatus sets the "status" field.
func (m *AiAgentMutation) SetStatus(i int8) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *AiAgentMutation) Status() (r int8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the AiAgent entity.
// If the AiAgent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *AiAgentMutation) OldStatus(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *AiAgentMutation) AddStatus(i int8) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *AiAgentMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *AiAgentMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// AddConversationIDs adds the "conversations" edge to the Conversation entity by ids.
func (m *AiAgentMutation) AddConversationIDs(ids ...int) {
	if m.conversations == nil {
		m.conversations = make(map[int]struct{})
	}
	for i := range ids {
		m.conversations[ids[i]] = struct{}{}
	}
}

// ClearConversations clears the "conversations" edge to the Conversation entity.
func (m *AiAgentMutation) ClearConversations() {
	m.clearedconversations = true
}

// ConversationsCleared reports if the "conversations" edge to the Conversation entity was cleared.
func (m *AiAgentMutation) ConversationsCleared() bool {
	return m.clearedconversations
}

// RemoveConversationIDs removes the "conversations" edge to the Conversation entity by IDs.
func (m *AiAgentMutation) RemoveConversationIDs(ids ...int) {
	if m.removedconversations == nil {
		m.removedconversations = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.conversations, ids[i])
		m.removedconversations[ids[i]] = struct{}{}
	}
}

// RemovedConversations returns the removed IDs of the "conversations" edge to the Conversation entity.
func (m *AiAgentMutation) RemovedConversationsIDs() (ids []int) {
	for id := range m.removedconversations {
		ids = append(ids, id)
	}
	return
}

// ConversationsIDs returns the "conversations" edge IDs in the mutation.
func (m *AiAgentMutation) ConversationsIDs() (ids []int) {
	for id := range m.conversations {
		ids = append(ids, id)
	}
	return
}

// ResetConversations resets all changes to the "conversations" edge.
func (m *AiAgentMutation) ResetConversations() {
	m.conversations = nil
	m.clearedconversations = false
	m.removedconversations = nil
}

// Where appends a list predicates to the AiAgentMutation builder.
func (m *AiAgentMutation) Where(ps ...predicate.AiAgent) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the AiAgentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *AiAgentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.AiAgent, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *AiAgentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *AiAgentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (AiAgent).
func (m *AiAgentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *AiAgentMutation) Fields() []string {
	fields := make([]string, 0, 11)
	if m.created_at != nil {
		fields = append(fields, aiagent.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, aiagent.FieldUpdatedAt)
	}
	if m.name != nil {
		fields = append(fields, aiagent.FieldName)
	}
	if m.icon != nil {
		fields = append(fields, aiagent.FieldIcon)
	}
	if m.description != nil {
		fields = append(fields, aiagent.FieldDescription)
	}
	if m.target != nil {
		fields = append(fields, aiagent.FieldTarget)
	}
	if m.guide != nil {
		fields = append(fields, aiagent.FieldGuide)
	}
	if m.secret != nil {
		fields = append(fields, aiagent.FieldSecret)
	}
	if m.method != nil {
		fields = append(fields, aiagent.FieldMethod)
	}
	if m.inputs != nil {
		fields = append(fields, aiagent.FieldInputs)
	}
	if m.status != nil {
		fields = append(fields, aiagent.FieldStatus)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *AiAgentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case aiagent.FieldCreatedAt:
		return m.CreatedAt()
	case aiagent.FieldUpdatedAt:
		return m.UpdatedAt()
	case aiagent.FieldName:
		return m.Name()
	case aiagent.FieldIcon:
		return m.Icon()
	case aiagent.FieldDescription:
		return m.Description()
	case aiagent.FieldTarget:
		return m.Target()
	case aiagent.FieldGuide:
		return m.Guide()
	case aiagent.FieldSecret:
		return m.Secret()
	case aiagent.FieldMethod:
		return m.Method()
	case aiagent.FieldInputs:
		return m.Inputs()
	case aiagent.FieldStatus:
		return m.Status()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *AiAgentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case aiagent.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case aiagent.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case aiagent.FieldName:
		return m.OldName(ctx)
	case aiagent.FieldIcon:
		return m.OldIcon(ctx)
	case aiagent.FieldDescription:
		return m.OldDescription(ctx)
	case aiagent.FieldTarget:
		return m.OldTarget(ctx)
	case aiagent.FieldGuide:
		return m.OldGuide(ctx)
	case aiagent.FieldSecret:
		return m.OldSecret(ctx)
	case aiagent.FieldMethod:
		return m.OldMethod(ctx)
	case aiagent.FieldInputs:
		return m.OldInputs(ctx)
	case aiagent.FieldStatus:
		return m.OldStatus(ctx)
	}
	return nil, fmt.Errorf("unknown AiAgent field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AiAgentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case aiagent.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case aiagent.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case aiagent.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case aiagent.FieldIcon:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetIcon(v)
		return nil
	case aiagent.FieldDescription:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDescription(v)
		return nil
	case aiagent.FieldTarget:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTarget(v)
		return nil
	case aiagent.FieldGuide:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetGuide(v)
		return nil
	case aiagent.FieldSecret:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetSecret(v)
		return nil
	case aiagent.FieldMethod:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetMethod(v)
		return nil
	case aiagent.FieldInputs:
		v, ok := value.([]schema.AiAgentParam)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInputs(v)
		return nil
	case aiagent.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	}
	return fmt.Errorf("unknown AiAgent field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *AiAgentMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, aiagent.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *AiAgentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case aiagent.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *AiAgentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case aiagent.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown AiAgent numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *AiAgentMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(aiagent.FieldIcon) {
		fields = append(fields, aiagent.FieldIcon)
	}
	if m.FieldCleared(aiagent.FieldDescription) {
		fields = append(fields, aiagent.FieldDescription)
	}
	if m.FieldCleared(aiagent.FieldGuide) {
		fields = append(fields, aiagent.FieldGuide)
	}
	if m.FieldCleared(aiagent.FieldInputs) {
		fields = append(fields, aiagent.FieldInputs)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *AiAgentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *AiAgentMutation) ClearField(name string) error {
	switch name {
	case aiagent.FieldIcon:
		m.ClearIcon()
		return nil
	case aiagent.FieldDescription:
		m.ClearDescription()
		return nil
	case aiagent.FieldGuide:
		m.ClearGuide()
		return nil
	case aiagent.FieldInputs:
		m.ClearInputs()
		return nil
	}
	return fmt.Errorf("unknown AiAgent nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *AiAgentMutation) ResetField(name string) error {
	switch name {
	case aiagent.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case aiagent.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case aiagent.FieldName:
		m.ResetName()
		return nil
	case aiagent.FieldIcon:
		m.ResetIcon()
		return nil
	case aiagent.FieldDescription:
		m.ResetDescription()
		return nil
	case aiagent.FieldTarget:
		m.ResetTarget()
		return nil
	case aiagent.FieldGuide:
		m.ResetGuide()
		return nil
	case aiagent.FieldSecret:
		m.ResetSecret()
		return nil
	case aiagent.FieldMethod:
		m.ResetMethod()
		return nil
	case aiagent.FieldInputs:
		m.ResetInputs()
		return nil
	case aiagent.FieldStatus:
		m.ResetStatus()
		return nil
	}
	return fmt.Errorf("unknown AiAgent field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *AiAgentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.conversations != nil {
		edges = append(edges, aiagent.EdgeConversations)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *AiAgentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case aiagent.EdgeConversations:
		ids := make([]ent.Value, 0, len(m.conversations))
		for id := range m.conversations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *AiAgentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedconversations != nil {
		edges = append(edges, aiagent.EdgeConversations)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *AiAgentMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case aiagent.EdgeConversations:
		ids := make([]ent.Value, 0, len(m.removedconversations))
		for id := range m.removedconversations {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *AiAgentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedconversations {
		edges = append(edges, aiagent.EdgeConversations)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *AiAgentMutation) EdgeCleared(name string) bool {
	switch name {
	case aiagent.EdgeConversations:
		return m.clearedconversations
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *AiAgentMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown AiAgent unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *AiAgentMutation) ResetEdge(name string) error {
	switch name {
	case aiagent.EdgeConversations:
		m.ResetConversations()
		return nil
	}
	return fmt.Errorf("unknown AiAgent edge %s", name)
}

// BatchMutation represents an operation that mutates the Batch nodes in the graph.
type BatchMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	name          *string
	num           *int64
	addnum        *int64
	expired_at    *time.Time
	clearedFields map[string]struct{}
	codes         map[int]struct{}
	removedcodes  map[int]struct{}
	clearedcodes  bool
	users         map[int]struct{}
	removedusers  map[int]struct{}
	clearedusers  bool
	done          bool
	oldValue      func(context.Context) (*Batch, error)
	predicates    []predicate.Batch
}

var _ ent.Mutation = (*BatchMutation)(nil)

// batchOption allows management of the mutation configuration using functional options.
type batchOption func(*BatchMutation)

// newBatchMutation creates new mutation for the Batch entity.
func newBatchMutation(c config, op Op, opts ...batchOption) *BatchMutation {
	m := &BatchMutation{
		config:        c,
		op:            op,
		typ:           TypeBatch,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withBatchID sets the ID field of the mutation.
func withBatchID(id int) batchOption {
	return func(m *BatchMutation) {
		var (
			err   error
			once  sync.Once
			value *Batch
		)
		m.oldValue = func(ctx context.Context) (*Batch, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Batch.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withBatch sets the old Batch of the mutation.
func withBatch(node *Batch) batchOption {
	return func(m *BatchMutation) {
		m.oldValue = func(context.Context) (*Batch, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m BatchMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m BatchMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *BatchMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *BatchMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Batch.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *BatchMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *BatchMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Batch entity.
// If the Batch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BatchMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *BatchMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *BatchMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *BatchMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Batch entity.
// If the Batch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BatchMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *BatchMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetName sets the "name" field.
func (m *BatchMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *BatchMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Batch entity.
// If the Batch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BatchMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *BatchMutation) ResetName() {
	m.name = nil
}

// SetNum sets the "num" field.
func (m *BatchMutation) SetNum(i int64) {
	m.num = &i
	m.addnum = nil
}

// Num returns the value of the "num" field in the mutation.
func (m *BatchMutation) Num() (r int64, exists bool) {
	v := m.num
	if v == nil {
		return
	}
	return *v, true
}

// OldNum returns the old "num" field's value of the Batch entity.
// If the Batch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BatchMutation) OldNum(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldNum is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldNum requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldNum: %w", err)
	}
	return oldValue.Num, nil
}

// AddNum adds i to the "num" field.
func (m *BatchMutation) AddNum(i int64) {
	if m.addnum != nil {
		*m.addnum += i
	} else {
		m.addnum = &i
	}
}

// AddedNum returns the value that was added to the "num" field in this mutation.
func (m *BatchMutation) AddedNum() (r int64, exists bool) {
	v := m.addnum
	if v == nil {
		return
	}
	return *v, true
}

// ResetNum resets all changes to the "num" field.
func (m *BatchMutation) ResetNum() {
	m.num = nil
	m.addnum = nil
}

// SetExpiredAt sets the "expired_at" field.
func (m *BatchMutation) SetExpiredAt(t time.Time) {
	m.expired_at = &t
}

// ExpiredAt returns the value of the "expired_at" field in the mutation.
func (m *BatchMutation) ExpiredAt() (r time.Time, exists bool) {
	v := m.expired_at
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiredAt returns the old "expired_at" field's value of the Batch entity.
// If the Batch object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *BatchMutation) OldExpiredAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiredAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiredAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiredAt: %w", err)
	}
	return oldValue.ExpiredAt, nil
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (m *BatchMutation) ClearExpiredAt() {
	m.expired_at = nil
	m.clearedFields[batch.FieldExpiredAt] = struct{}{}
}

// ExpiredAtCleared returns if the "expired_at" field was cleared in this mutation.
func (m *BatchMutation) ExpiredAtCleared() bool {
	_, ok := m.clearedFields[batch.FieldExpiredAt]
	return ok
}

// ResetExpiredAt resets all changes to the "expired_at" field.
func (m *BatchMutation) ResetExpiredAt() {
	m.expired_at = nil
	delete(m.clearedFields, batch.FieldExpiredAt)
}

// AddCodeIDs adds the "codes" edge to the TestCode entity by ids.
func (m *BatchMutation) AddCodeIDs(ids ...int) {
	if m.codes == nil {
		m.codes = make(map[int]struct{})
	}
	for i := range ids {
		m.codes[ids[i]] = struct{}{}
	}
}

// ClearCodes clears the "codes" edge to the TestCode entity.
func (m *BatchMutation) ClearCodes() {
	m.clearedcodes = true
}

// CodesCleared reports if the "codes" edge to the TestCode entity was cleared.
func (m *BatchMutation) CodesCleared() bool {
	return m.clearedcodes
}

// RemoveCodeIDs removes the "codes" edge to the TestCode entity by IDs.
func (m *BatchMutation) RemoveCodeIDs(ids ...int) {
	if m.removedcodes == nil {
		m.removedcodes = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.codes, ids[i])
		m.removedcodes[ids[i]] = struct{}{}
	}
}

// RemovedCodes returns the removed IDs of the "codes" edge to the TestCode entity.
func (m *BatchMutation) RemovedCodesIDs() (ids []int) {
	for id := range m.removedcodes {
		ids = append(ids, id)
	}
	return
}

// CodesIDs returns the "codes" edge IDs in the mutation.
func (m *BatchMutation) CodesIDs() (ids []int) {
	for id := range m.codes {
		ids = append(ids, id)
	}
	return
}

// ResetCodes resets all changes to the "codes" edge.
func (m *BatchMutation) ResetCodes() {
	m.codes = nil
	m.clearedcodes = false
	m.removedcodes = nil
}

// AddUserIDs adds the "users" edge to the User entity by ids.
func (m *BatchMutation) AddUserIDs(ids ...int) {
	if m.users == nil {
		m.users = make(map[int]struct{})
	}
	for i := range ids {
		m.users[ids[i]] = struct{}{}
	}
}

// ClearUsers clears the "users" edge to the User entity.
func (m *BatchMutation) ClearUsers() {
	m.clearedusers = true
}

// UsersCleared reports if the "users" edge to the User entity was cleared.
func (m *BatchMutation) UsersCleared() bool {
	return m.clearedusers
}

// RemoveUserIDs removes the "users" edge to the User entity by IDs.
func (m *BatchMutation) RemoveUserIDs(ids ...int) {
	if m.removedusers == nil {
		m.removedusers = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.users, ids[i])
		m.removedusers[ids[i]] = struct{}{}
	}
}

// RemovedUsers returns the removed IDs of the "users" edge to the User entity.
func (m *BatchMutation) RemovedUsersIDs() (ids []int) {
	for id := range m.removedusers {
		ids = append(ids, id)
	}
	return
}

// UsersIDs returns the "users" edge IDs in the mutation.
func (m *BatchMutation) UsersIDs() (ids []int) {
	for id := range m.users {
		ids = append(ids, id)
	}
	return
}

// ResetUsers resets all changes to the "users" edge.
func (m *BatchMutation) ResetUsers() {
	m.users = nil
	m.clearedusers = false
	m.removedusers = nil
}

// Where appends a list predicates to the BatchMutation builder.
func (m *BatchMutation) Where(ps ...predicate.Batch) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the BatchMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *BatchMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Batch, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *BatchMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *BatchMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Batch).
func (m *BatchMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *BatchMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.created_at != nil {
		fields = append(fields, batch.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, batch.FieldUpdatedAt)
	}
	if m.name != nil {
		fields = append(fields, batch.FieldName)
	}
	if m.num != nil {
		fields = append(fields, batch.FieldNum)
	}
	if m.expired_at != nil {
		fields = append(fields, batch.FieldExpiredAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *BatchMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case batch.FieldCreatedAt:
		return m.CreatedAt()
	case batch.FieldUpdatedAt:
		return m.UpdatedAt()
	case batch.FieldName:
		return m.Name()
	case batch.FieldNum:
		return m.Num()
	case batch.FieldExpiredAt:
		return m.ExpiredAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *BatchMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case batch.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case batch.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case batch.FieldName:
		return m.OldName(ctx)
	case batch.FieldNum:
		return m.OldNum(ctx)
	case batch.FieldExpiredAt:
		return m.OldExpiredAt(ctx)
	}
	return nil, fmt.Errorf("unknown Batch field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BatchMutation) SetField(name string, value ent.Value) error {
	switch name {
	case batch.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case batch.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case batch.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	case batch.FieldNum:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetNum(v)
		return nil
	case batch.FieldExpiredAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiredAt(v)
		return nil
	}
	return fmt.Errorf("unknown Batch field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *BatchMutation) AddedFields() []string {
	var fields []string
	if m.addnum != nil {
		fields = append(fields, batch.FieldNum)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *BatchMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case batch.FieldNum:
		return m.AddedNum()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *BatchMutation) AddField(name string, value ent.Value) error {
	switch name {
	case batch.FieldNum:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddNum(v)
		return nil
	}
	return fmt.Errorf("unknown Batch numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *BatchMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(batch.FieldExpiredAt) {
		fields = append(fields, batch.FieldExpiredAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *BatchMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *BatchMutation) ClearField(name string) error {
	switch name {
	case batch.FieldExpiredAt:
		m.ClearExpiredAt()
		return nil
	}
	return fmt.Errorf("unknown Batch nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *BatchMutation) ResetField(name string) error {
	switch name {
	case batch.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case batch.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case batch.FieldName:
		m.ResetName()
		return nil
	case batch.FieldNum:
		m.ResetNum()
		return nil
	case batch.FieldExpiredAt:
		m.ResetExpiredAt()
		return nil
	}
	return fmt.Errorf("unknown Batch field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *BatchMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.codes != nil {
		edges = append(edges, batch.EdgeCodes)
	}
	if m.users != nil {
		edges = append(edges, batch.EdgeUsers)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *BatchMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case batch.EdgeCodes:
		ids := make([]ent.Value, 0, len(m.codes))
		for id := range m.codes {
			ids = append(ids, id)
		}
		return ids
	case batch.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.users))
		for id := range m.users {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *BatchMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedcodes != nil {
		edges = append(edges, batch.EdgeCodes)
	}
	if m.removedusers != nil {
		edges = append(edges, batch.EdgeUsers)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *BatchMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case batch.EdgeCodes:
		ids := make([]ent.Value, 0, len(m.removedcodes))
		for id := range m.removedcodes {
			ids = append(ids, id)
		}
		return ids
	case batch.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.removedusers))
		for id := range m.removedusers {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *BatchMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedcodes {
		edges = append(edges, batch.EdgeCodes)
	}
	if m.clearedusers {
		edges = append(edges, batch.EdgeUsers)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *BatchMutation) EdgeCleared(name string) bool {
	switch name {
	case batch.EdgeCodes:
		return m.clearedcodes
	case batch.EdgeUsers:
		return m.clearedusers
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *BatchMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown Batch unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *BatchMutation) ResetEdge(name string) error {
	switch name {
	case batch.EdgeCodes:
		m.ResetCodes()
		return nil
	case batch.EdgeUsers:
		m.ResetUsers()
		return nil
	}
	return fmt.Errorf("unknown Batch edge %s", name)
}

// ConversationMutation represents an operation that mutates the Conversation nodes in the graph.
type ConversationMutation struct {
	config
	op                       Op
	typ                      string
	id                       *int
	created_at               *time.Time
	updated_at               *time.Time
	user_id                  *int
	adduser_id               *int
	ai_agent_conversation_id *string
	name                     *string
	clearedFields            map[string]struct{}
	contents                 map[int]struct{}
	removedcontents          map[int]struct{}
	clearedcontents          bool
	ai_agent                 *int
	clearedai_agent          bool
	done                     bool
	oldValue                 func(context.Context) (*Conversation, error)
	predicates               []predicate.Conversation
}

var _ ent.Mutation = (*ConversationMutation)(nil)

// conversationOption allows management of the mutation configuration using functional options.
type conversationOption func(*ConversationMutation)

// newConversationMutation creates new mutation for the Conversation entity.
func newConversationMutation(c config, op Op, opts ...conversationOption) *ConversationMutation {
	m := &ConversationMutation{
		config:        c,
		op:            op,
		typ:           TypeConversation,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withConversationID sets the ID field of the mutation.
func withConversationID(id int) conversationOption {
	return func(m *ConversationMutation) {
		var (
			err   error
			once  sync.Once
			value *Conversation
		)
		m.oldValue = func(ctx context.Context) (*Conversation, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Conversation.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withConversation sets the old Conversation of the mutation.
func withConversation(node *Conversation) conversationOption {
	return func(m *ConversationMutation) {
		m.oldValue = func(context.Context) (*Conversation, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ConversationMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ConversationMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ConversationMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ConversationMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Conversation.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *ConversationMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ConversationMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ConversationMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ConversationMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ConversationMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ConversationMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetAiAgentID sets the "ai_agent_id" field.
func (m *ConversationMutation) SetAiAgentID(i int) {
	m.ai_agent = &i
}

// AiAgentID returns the value of the "ai_agent_id" field in the mutation.
func (m *ConversationMutation) AiAgentID() (r int, exists bool) {
	v := m.ai_agent
	if v == nil {
		return
	}
	return *v, true
}

// OldAiAgentID returns the old "ai_agent_id" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldAiAgentID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAiAgentID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAiAgentID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAiAgentID: %w", err)
	}
	return oldValue.AiAgentID, nil
}

// ResetAiAgentID resets all changes to the "ai_agent_id" field.
func (m *ConversationMutation) ResetAiAgentID() {
	m.ai_agent = nil
}

// SetUserID sets the "user_id" field.
func (m *ConversationMutation) SetUserID(i int) {
	m.user_id = &i
	m.adduser_id = nil
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *ConversationMutation) UserID() (r int, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldUserID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// AddUserID adds i to the "user_id" field.
func (m *ConversationMutation) AddUserID(i int) {
	if m.adduser_id != nil {
		*m.adduser_id += i
	} else {
		m.adduser_id = &i
	}
}

// AddedUserID returns the value that was added to the "user_id" field in this mutation.
func (m *ConversationMutation) AddedUserID() (r int, exists bool) {
	v := m.adduser_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetUserID resets all changes to the "user_id" field.
func (m *ConversationMutation) ResetUserID() {
	m.user_id = nil
	m.adduser_id = nil
}

// SetAiAgentConversationID sets the "ai_agent_conversation_id" field.
func (m *ConversationMutation) SetAiAgentConversationID(s string) {
	m.ai_agent_conversation_id = &s
}

// AiAgentConversationID returns the value of the "ai_agent_conversation_id" field in the mutation.
func (m *ConversationMutation) AiAgentConversationID() (r string, exists bool) {
	v := m.ai_agent_conversation_id
	if v == nil {
		return
	}
	return *v, true
}

// OldAiAgentConversationID returns the old "ai_agent_conversation_id" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldAiAgentConversationID(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldAiAgentConversationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldAiAgentConversationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldAiAgentConversationID: %w", err)
	}
	return oldValue.AiAgentConversationID, nil
}

// ResetAiAgentConversationID resets all changes to the "ai_agent_conversation_id" field.
func (m *ConversationMutation) ResetAiAgentConversationID() {
	m.ai_agent_conversation_id = nil
}

// SetName sets the "name" field.
func (m *ConversationMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *ConversationMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Conversation entity.
// If the Conversation object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *ConversationMutation) ResetName() {
	m.name = nil
}

// AddContentIDs adds the "contents" edge to the ConversationContent entity by ids.
func (m *ConversationMutation) AddContentIDs(ids ...int) {
	if m.contents == nil {
		m.contents = make(map[int]struct{})
	}
	for i := range ids {
		m.contents[ids[i]] = struct{}{}
	}
}

// ClearContents clears the "contents" edge to the ConversationContent entity.
func (m *ConversationMutation) ClearContents() {
	m.clearedcontents = true
}

// ContentsCleared reports if the "contents" edge to the ConversationContent entity was cleared.
func (m *ConversationMutation) ContentsCleared() bool {
	return m.clearedcontents
}

// RemoveContentIDs removes the "contents" edge to the ConversationContent entity by IDs.
func (m *ConversationMutation) RemoveContentIDs(ids ...int) {
	if m.removedcontents == nil {
		m.removedcontents = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.contents, ids[i])
		m.removedcontents[ids[i]] = struct{}{}
	}
}

// RemovedContents returns the removed IDs of the "contents" edge to the ConversationContent entity.
func (m *ConversationMutation) RemovedContentsIDs() (ids []int) {
	for id := range m.removedcontents {
		ids = append(ids, id)
	}
	return
}

// ContentsIDs returns the "contents" edge IDs in the mutation.
func (m *ConversationMutation) ContentsIDs() (ids []int) {
	for id := range m.contents {
		ids = append(ids, id)
	}
	return
}

// ResetContents resets all changes to the "contents" edge.
func (m *ConversationMutation) ResetContents() {
	m.contents = nil
	m.clearedcontents = false
	m.removedcontents = nil
}

// ClearAiAgent clears the "ai_agent" edge to the AiAgent entity.
func (m *ConversationMutation) ClearAiAgent() {
	m.clearedai_agent = true
	m.clearedFields[conversation.FieldAiAgentID] = struct{}{}
}

// AiAgentCleared reports if the "ai_agent" edge to the AiAgent entity was cleared.
func (m *ConversationMutation) AiAgentCleared() bool {
	return m.clearedai_agent
}

// AiAgentIDs returns the "ai_agent" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// AiAgentID instead. It exists only for internal usage by the builders.
func (m *ConversationMutation) AiAgentIDs() (ids []int) {
	if id := m.ai_agent; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetAiAgent resets all changes to the "ai_agent" edge.
func (m *ConversationMutation) ResetAiAgent() {
	m.ai_agent = nil
	m.clearedai_agent = false
}

// Where appends a list predicates to the ConversationMutation builder.
func (m *ConversationMutation) Where(ps ...predicate.Conversation) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ConversationMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ConversationMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Conversation, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ConversationMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ConversationMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Conversation).
func (m *ConversationMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ConversationMutation) Fields() []string {
	fields := make([]string, 0, 6)
	if m.created_at != nil {
		fields = append(fields, conversation.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, conversation.FieldUpdatedAt)
	}
	if m.ai_agent != nil {
		fields = append(fields, conversation.FieldAiAgentID)
	}
	if m.user_id != nil {
		fields = append(fields, conversation.FieldUserID)
	}
	if m.ai_agent_conversation_id != nil {
		fields = append(fields, conversation.FieldAiAgentConversationID)
	}
	if m.name != nil {
		fields = append(fields, conversation.FieldName)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ConversationMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case conversation.FieldCreatedAt:
		return m.CreatedAt()
	case conversation.FieldUpdatedAt:
		return m.UpdatedAt()
	case conversation.FieldAiAgentID:
		return m.AiAgentID()
	case conversation.FieldUserID:
		return m.UserID()
	case conversation.FieldAiAgentConversationID:
		return m.AiAgentConversationID()
	case conversation.FieldName:
		return m.Name()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ConversationMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case conversation.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case conversation.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case conversation.FieldAiAgentID:
		return m.OldAiAgentID(ctx)
	case conversation.FieldUserID:
		return m.OldUserID(ctx)
	case conversation.FieldAiAgentConversationID:
		return m.OldAiAgentConversationID(ctx)
	case conversation.FieldName:
		return m.OldName(ctx)
	}
	return nil, fmt.Errorf("unknown Conversation field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConversationMutation) SetField(name string, value ent.Value) error {
	switch name {
	case conversation.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case conversation.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case conversation.FieldAiAgentID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAiAgentID(v)
		return nil
	case conversation.FieldUserID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case conversation.FieldAiAgentConversationID:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetAiAgentConversationID(v)
		return nil
	case conversation.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	}
	return fmt.Errorf("unknown Conversation field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ConversationMutation) AddedFields() []string {
	var fields []string
	if m.adduser_id != nil {
		fields = append(fields, conversation.FieldUserID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ConversationMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case conversation.FieldUserID:
		return m.AddedUserID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConversationMutation) AddField(name string, value ent.Value) error {
	switch name {
	case conversation.FieldUserID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUserID(v)
		return nil
	}
	return fmt.Errorf("unknown Conversation numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ConversationMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ConversationMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ConversationMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Conversation nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ConversationMutation) ResetField(name string) error {
	switch name {
	case conversation.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case conversation.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case conversation.FieldAiAgentID:
		m.ResetAiAgentID()
		return nil
	case conversation.FieldUserID:
		m.ResetUserID()
		return nil
	case conversation.FieldAiAgentConversationID:
		m.ResetAiAgentConversationID()
		return nil
	case conversation.FieldName:
		m.ResetName()
		return nil
	}
	return fmt.Errorf("unknown Conversation field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ConversationMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.contents != nil {
		edges = append(edges, conversation.EdgeContents)
	}
	if m.ai_agent != nil {
		edges = append(edges, conversation.EdgeAiAgent)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ConversationMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case conversation.EdgeContents:
		ids := make([]ent.Value, 0, len(m.contents))
		for id := range m.contents {
			ids = append(ids, id)
		}
		return ids
	case conversation.EdgeAiAgent:
		if id := m.ai_agent; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ConversationMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	if m.removedcontents != nil {
		edges = append(edges, conversation.EdgeContents)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ConversationMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case conversation.EdgeContents:
		ids := make([]ent.Value, 0, len(m.removedcontents))
		for id := range m.removedcontents {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ConversationMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedcontents {
		edges = append(edges, conversation.EdgeContents)
	}
	if m.clearedai_agent {
		edges = append(edges, conversation.EdgeAiAgent)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ConversationMutation) EdgeCleared(name string) bool {
	switch name {
	case conversation.EdgeContents:
		return m.clearedcontents
	case conversation.EdgeAiAgent:
		return m.clearedai_agent
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ConversationMutation) ClearEdge(name string) error {
	switch name {
	case conversation.EdgeAiAgent:
		m.ClearAiAgent()
		return nil
	}
	return fmt.Errorf("unknown Conversation unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ConversationMutation) ResetEdge(name string) error {
	switch name {
	case conversation.EdgeContents:
		m.ResetContents()
		return nil
	case conversation.EdgeAiAgent:
		m.ResetAiAgent()
		return nil
	}
	return fmt.Errorf("unknown Conversation edge %s", name)
}

// ConversationContentMutation represents an operation that mutates the ConversationContent nodes in the graph.
type ConversationContentMutation struct {
	config
	op                  Op
	typ                 string
	id                  *int
	created_at          *time.Time
	updated_at          *time.Time
	content             *string
	_type               *int8
	add_type            *int8
	clearedFields       map[string]struct{}
	conversation        *int
	clearedconversation bool
	done                bool
	oldValue            func(context.Context) (*ConversationContent, error)
	predicates          []predicate.ConversationContent
}

var _ ent.Mutation = (*ConversationContentMutation)(nil)

// conversationcontentOption allows management of the mutation configuration using functional options.
type conversationcontentOption func(*ConversationContentMutation)

// newConversationContentMutation creates new mutation for the ConversationContent entity.
func newConversationContentMutation(c config, op Op, opts ...conversationcontentOption) *ConversationContentMutation {
	m := &ConversationContentMutation{
		config:        c,
		op:            op,
		typ:           TypeConversationContent,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withConversationContentID sets the ID field of the mutation.
func withConversationContentID(id int) conversationcontentOption {
	return func(m *ConversationContentMutation) {
		var (
			err   error
			once  sync.Once
			value *ConversationContent
		)
		m.oldValue = func(ctx context.Context) (*ConversationContent, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().ConversationContent.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withConversationContent sets the old ConversationContent of the mutation.
func withConversationContent(node *ConversationContent) conversationcontentOption {
	return func(m *ConversationContentMutation) {
		m.oldValue = func(context.Context) (*ConversationContent, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ConversationContentMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ConversationContentMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ConversationContentMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ConversationContentMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().ConversationContent.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *ConversationContentMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ConversationContentMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the ConversationContent entity.
// If the ConversationContent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationContentMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ConversationContentMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ConversationContentMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ConversationContentMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the ConversationContent entity.
// If the ConversationContent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationContentMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ConversationContentMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetConversationID sets the "conversation_id" field.
func (m *ConversationContentMutation) SetConversationID(i int) {
	m.conversation = &i
}

// ConversationID returns the value of the "conversation_id" field in the mutation.
func (m *ConversationContentMutation) ConversationID() (r int, exists bool) {
	v := m.conversation
	if v == nil {
		return
	}
	return *v, true
}

// OldConversationID returns the old "conversation_id" field's value of the ConversationContent entity.
// If the ConversationContent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationContentMutation) OldConversationID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldConversationID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldConversationID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldConversationID: %w", err)
	}
	return oldValue.ConversationID, nil
}

// ResetConversationID resets all changes to the "conversation_id" field.
func (m *ConversationContentMutation) ResetConversationID() {
	m.conversation = nil
}

// SetContent sets the "content" field.
func (m *ConversationContentMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *ConversationContentMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the ConversationContent entity.
// If the ConversationContent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationContentMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *ConversationContentMutation) ResetContent() {
	m.content = nil
}

// SetType sets the "type" field.
func (m *ConversationContentMutation) SetType(i int8) {
	m._type = &i
	m.add_type = nil
}

// GetType returns the value of the "type" field in the mutation.
func (m *ConversationContentMutation) GetType() (r int8, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the ConversationContent entity.
// If the ConversationContent object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ConversationContentMutation) OldType(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// AddType adds i to the "type" field.
func (m *ConversationContentMutation) AddType(i int8) {
	if m.add_type != nil {
		*m.add_type += i
	} else {
		m.add_type = &i
	}
}

// AddedType returns the value that was added to the "type" field in this mutation.
func (m *ConversationContentMutation) AddedType() (r int8, exists bool) {
	v := m.add_type
	if v == nil {
		return
	}
	return *v, true
}

// ResetType resets all changes to the "type" field.
func (m *ConversationContentMutation) ResetType() {
	m._type = nil
	m.add_type = nil
}

// ClearConversation clears the "conversation" edge to the Conversation entity.
func (m *ConversationContentMutation) ClearConversation() {
	m.clearedconversation = true
	m.clearedFields[conversationcontent.FieldConversationID] = struct{}{}
}

// ConversationCleared reports if the "conversation" edge to the Conversation entity was cleared.
func (m *ConversationContentMutation) ConversationCleared() bool {
	return m.clearedconversation
}

// ConversationIDs returns the "conversation" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// ConversationID instead. It exists only for internal usage by the builders.
func (m *ConversationContentMutation) ConversationIDs() (ids []int) {
	if id := m.conversation; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetConversation resets all changes to the "conversation" edge.
func (m *ConversationContentMutation) ResetConversation() {
	m.conversation = nil
	m.clearedconversation = false
}

// Where appends a list predicates to the ConversationContentMutation builder.
func (m *ConversationContentMutation) Where(ps ...predicate.ConversationContent) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ConversationContentMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ConversationContentMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.ConversationContent, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ConversationContentMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ConversationContentMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (ConversationContent).
func (m *ConversationContentMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ConversationContentMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.created_at != nil {
		fields = append(fields, conversationcontent.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, conversationcontent.FieldUpdatedAt)
	}
	if m.conversation != nil {
		fields = append(fields, conversationcontent.FieldConversationID)
	}
	if m.content != nil {
		fields = append(fields, conversationcontent.FieldContent)
	}
	if m._type != nil {
		fields = append(fields, conversationcontent.FieldType)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ConversationContentMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case conversationcontent.FieldCreatedAt:
		return m.CreatedAt()
	case conversationcontent.FieldUpdatedAt:
		return m.UpdatedAt()
	case conversationcontent.FieldConversationID:
		return m.ConversationID()
	case conversationcontent.FieldContent:
		return m.Content()
	case conversationcontent.FieldType:
		return m.GetType()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ConversationContentMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case conversationcontent.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case conversationcontent.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case conversationcontent.FieldConversationID:
		return m.OldConversationID(ctx)
	case conversationcontent.FieldContent:
		return m.OldContent(ctx)
	case conversationcontent.FieldType:
		return m.OldType(ctx)
	}
	return nil, fmt.Errorf("unknown ConversationContent field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConversationContentMutation) SetField(name string, value ent.Value) error {
	switch name {
	case conversationcontent.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case conversationcontent.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case conversationcontent.FieldConversationID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetConversationID(v)
		return nil
	case conversationcontent.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case conversationcontent.FieldType:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	}
	return fmt.Errorf("unknown ConversationContent field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ConversationContentMutation) AddedFields() []string {
	var fields []string
	if m.add_type != nil {
		fields = append(fields, conversationcontent.FieldType)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ConversationContentMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case conversationcontent.FieldType:
		return m.AddedType()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ConversationContentMutation) AddField(name string, value ent.Value) error {
	switch name {
	case conversationcontent.FieldType:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddType(v)
		return nil
	}
	return fmt.Errorf("unknown ConversationContent numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ConversationContentMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ConversationContentMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ConversationContentMutation) ClearField(name string) error {
	return fmt.Errorf("unknown ConversationContent nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ConversationContentMutation) ResetField(name string) error {
	switch name {
	case conversationcontent.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case conversationcontent.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case conversationcontent.FieldConversationID:
		m.ResetConversationID()
		return nil
	case conversationcontent.FieldContent:
		m.ResetContent()
		return nil
	case conversationcontent.FieldType:
		m.ResetType()
		return nil
	}
	return fmt.Errorf("unknown ConversationContent field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ConversationContentMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.conversation != nil {
		edges = append(edges, conversationcontent.EdgeConversation)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ConversationContentMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case conversationcontent.EdgeConversation:
		if id := m.conversation; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ConversationContentMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ConversationContentMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ConversationContentMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedconversation {
		edges = append(edges, conversationcontent.EdgeConversation)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ConversationContentMutation) EdgeCleared(name string) bool {
	switch name {
	case conversationcontent.EdgeConversation:
		return m.clearedconversation
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ConversationContentMutation) ClearEdge(name string) error {
	switch name {
	case conversationcontent.EdgeConversation:
		m.ClearConversation()
		return nil
	}
	return fmt.Errorf("unknown ConversationContent unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ConversationContentMutation) ResetEdge(name string) error {
	switch name {
	case conversationcontent.EdgeConversation:
		m.ResetConversation()
		return nil
	}
	return fmt.Errorf("unknown ConversationContent edge %s", name)
}

// FrequentAskQuestionMutation represents an operation that mutates the FrequentAskQuestion nodes in the graph.
type FrequentAskQuestionMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	content       *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*FrequentAskQuestion, error)
	predicates    []predicate.FrequentAskQuestion
}

var _ ent.Mutation = (*FrequentAskQuestionMutation)(nil)

// frequentaskquestionOption allows management of the mutation configuration using functional options.
type frequentaskquestionOption func(*FrequentAskQuestionMutation)

// newFrequentAskQuestionMutation creates new mutation for the FrequentAskQuestion entity.
func newFrequentAskQuestionMutation(c config, op Op, opts ...frequentaskquestionOption) *FrequentAskQuestionMutation {
	m := &FrequentAskQuestionMutation{
		config:        c,
		op:            op,
		typ:           TypeFrequentAskQuestion,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withFrequentAskQuestionID sets the ID field of the mutation.
func withFrequentAskQuestionID(id int) frequentaskquestionOption {
	return func(m *FrequentAskQuestionMutation) {
		var (
			err   error
			once  sync.Once
			value *FrequentAskQuestion
		)
		m.oldValue = func(ctx context.Context) (*FrequentAskQuestion, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().FrequentAskQuestion.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withFrequentAskQuestion sets the old FrequentAskQuestion of the mutation.
func withFrequentAskQuestion(node *FrequentAskQuestion) frequentaskquestionOption {
	return func(m *FrequentAskQuestionMutation) {
		m.oldValue = func(context.Context) (*FrequentAskQuestion, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m FrequentAskQuestionMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m FrequentAskQuestionMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *FrequentAskQuestionMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *FrequentAskQuestionMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().FrequentAskQuestion.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *FrequentAskQuestionMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *FrequentAskQuestionMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the FrequentAskQuestion entity.
// If the FrequentAskQuestion object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrequentAskQuestionMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *FrequentAskQuestionMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *FrequentAskQuestionMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *FrequentAskQuestionMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the FrequentAskQuestion entity.
// If the FrequentAskQuestion object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrequentAskQuestionMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *FrequentAskQuestionMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetContent sets the "content" field.
func (m *FrequentAskQuestionMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *FrequentAskQuestionMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the FrequentAskQuestion entity.
// If the FrequentAskQuestion object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *FrequentAskQuestionMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *FrequentAskQuestionMutation) ResetContent() {
	m.content = nil
}

// Where appends a list predicates to the FrequentAskQuestionMutation builder.
func (m *FrequentAskQuestionMutation) Where(ps ...predicate.FrequentAskQuestion) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the FrequentAskQuestionMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *FrequentAskQuestionMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.FrequentAskQuestion, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *FrequentAskQuestionMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *FrequentAskQuestionMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (FrequentAskQuestion).
func (m *FrequentAskQuestionMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *FrequentAskQuestionMutation) Fields() []string {
	fields := make([]string, 0, 3)
	if m.created_at != nil {
		fields = append(fields, frequentaskquestion.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, frequentaskquestion.FieldUpdatedAt)
	}
	if m.content != nil {
		fields = append(fields, frequentaskquestion.FieldContent)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *FrequentAskQuestionMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case frequentaskquestion.FieldCreatedAt:
		return m.CreatedAt()
	case frequentaskquestion.FieldUpdatedAt:
		return m.UpdatedAt()
	case frequentaskquestion.FieldContent:
		return m.Content()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *FrequentAskQuestionMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case frequentaskquestion.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case frequentaskquestion.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case frequentaskquestion.FieldContent:
		return m.OldContent(ctx)
	}
	return nil, fmt.Errorf("unknown FrequentAskQuestion field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FrequentAskQuestionMutation) SetField(name string, value ent.Value) error {
	switch name {
	case frequentaskquestion.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case frequentaskquestion.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case frequentaskquestion.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	}
	return fmt.Errorf("unknown FrequentAskQuestion field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *FrequentAskQuestionMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *FrequentAskQuestionMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *FrequentAskQuestionMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown FrequentAskQuestion numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *FrequentAskQuestionMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *FrequentAskQuestionMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *FrequentAskQuestionMutation) ClearField(name string) error {
	return fmt.Errorf("unknown FrequentAskQuestion nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *FrequentAskQuestionMutation) ResetField(name string) error {
	switch name {
	case frequentaskquestion.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case frequentaskquestion.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case frequentaskquestion.FieldContent:
		m.ResetContent()
		return nil
	}
	return fmt.Errorf("unknown FrequentAskQuestion field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *FrequentAskQuestionMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *FrequentAskQuestionMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *FrequentAskQuestionMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *FrequentAskQuestionMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *FrequentAskQuestionMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *FrequentAskQuestionMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *FrequentAskQuestionMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown FrequentAskQuestion unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *FrequentAskQuestionMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown FrequentAskQuestion edge %s", name)
}

// InternalMessageMutation represents an operation that mutates the InternalMessage nodes in the graph.
type InternalMessageMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	title         *string
	content       *string
	clearedFields map[string]struct{}
	users         map[int]struct{}
	removedusers  map[int]struct{}
	clearedusers  bool
	done          bool
	oldValue      func(context.Context) (*InternalMessage, error)
	predicates    []predicate.InternalMessage
}

var _ ent.Mutation = (*InternalMessageMutation)(nil)

// internalmessageOption allows management of the mutation configuration using functional options.
type internalmessageOption func(*InternalMessageMutation)

// newInternalMessageMutation creates new mutation for the InternalMessage entity.
func newInternalMessageMutation(c config, op Op, opts ...internalmessageOption) *InternalMessageMutation {
	m := &InternalMessageMutation{
		config:        c,
		op:            op,
		typ:           TypeInternalMessage,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withInternalMessageID sets the ID field of the mutation.
func withInternalMessageID(id int) internalmessageOption {
	return func(m *InternalMessageMutation) {
		var (
			err   error
			once  sync.Once
			value *InternalMessage
		)
		m.oldValue = func(ctx context.Context) (*InternalMessage, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().InternalMessage.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withInternalMessage sets the old InternalMessage of the mutation.
func withInternalMessage(node *InternalMessage) internalmessageOption {
	return func(m *InternalMessageMutation) {
		m.oldValue = func(context.Context) (*InternalMessage, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m InternalMessageMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m InternalMessageMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *InternalMessageMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *InternalMessageMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().InternalMessage.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *InternalMessageMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *InternalMessageMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the InternalMessage entity.
// If the InternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InternalMessageMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *InternalMessageMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *InternalMessageMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *InternalMessageMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the InternalMessage entity.
// If the InternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InternalMessageMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *InternalMessageMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetTitle sets the "title" field.
func (m *InternalMessageMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *InternalMessageMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the InternalMessage entity.
// If the InternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InternalMessageMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ClearTitle clears the value of the "title" field.
func (m *InternalMessageMutation) ClearTitle() {
	m.title = nil
	m.clearedFields[internalmessage.FieldTitle] = struct{}{}
}

// TitleCleared returns if the "title" field was cleared in this mutation.
func (m *InternalMessageMutation) TitleCleared() bool {
	_, ok := m.clearedFields[internalmessage.FieldTitle]
	return ok
}

// ResetTitle resets all changes to the "title" field.
func (m *InternalMessageMutation) ResetTitle() {
	m.title = nil
	delete(m.clearedFields, internalmessage.FieldTitle)
}

// SetContent sets the "content" field.
func (m *InternalMessageMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *InternalMessageMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the InternalMessage entity.
// If the InternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *InternalMessageMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *InternalMessageMutation) ResetContent() {
	m.content = nil
}

// AddUserIDs adds the "users" edge to the UserInternalMessage entity by ids.
func (m *InternalMessageMutation) AddUserIDs(ids ...int) {
	if m.users == nil {
		m.users = make(map[int]struct{})
	}
	for i := range ids {
		m.users[ids[i]] = struct{}{}
	}
}

// ClearUsers clears the "users" edge to the UserInternalMessage entity.
func (m *InternalMessageMutation) ClearUsers() {
	m.clearedusers = true
}

// UsersCleared reports if the "users" edge to the UserInternalMessage entity was cleared.
func (m *InternalMessageMutation) UsersCleared() bool {
	return m.clearedusers
}

// RemoveUserIDs removes the "users" edge to the UserInternalMessage entity by IDs.
func (m *InternalMessageMutation) RemoveUserIDs(ids ...int) {
	if m.removedusers == nil {
		m.removedusers = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.users, ids[i])
		m.removedusers[ids[i]] = struct{}{}
	}
}

// RemovedUsers returns the removed IDs of the "users" edge to the UserInternalMessage entity.
func (m *InternalMessageMutation) RemovedUsersIDs() (ids []int) {
	for id := range m.removedusers {
		ids = append(ids, id)
	}
	return
}

// UsersIDs returns the "users" edge IDs in the mutation.
func (m *InternalMessageMutation) UsersIDs() (ids []int) {
	for id := range m.users {
		ids = append(ids, id)
	}
	return
}

// ResetUsers resets all changes to the "users" edge.
func (m *InternalMessageMutation) ResetUsers() {
	m.users = nil
	m.clearedusers = false
	m.removedusers = nil
}

// Where appends a list predicates to the InternalMessageMutation builder.
func (m *InternalMessageMutation) Where(ps ...predicate.InternalMessage) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the InternalMessageMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *InternalMessageMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.InternalMessage, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *InternalMessageMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *InternalMessageMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (InternalMessage).
func (m *InternalMessageMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *InternalMessageMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.created_at != nil {
		fields = append(fields, internalmessage.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, internalmessage.FieldUpdatedAt)
	}
	if m.title != nil {
		fields = append(fields, internalmessage.FieldTitle)
	}
	if m.content != nil {
		fields = append(fields, internalmessage.FieldContent)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *InternalMessageMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case internalmessage.FieldCreatedAt:
		return m.CreatedAt()
	case internalmessage.FieldUpdatedAt:
		return m.UpdatedAt()
	case internalmessage.FieldTitle:
		return m.Title()
	case internalmessage.FieldContent:
		return m.Content()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *InternalMessageMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case internalmessage.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case internalmessage.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case internalmessage.FieldTitle:
		return m.OldTitle(ctx)
	case internalmessage.FieldContent:
		return m.OldContent(ctx)
	}
	return nil, fmt.Errorf("unknown InternalMessage field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *InternalMessageMutation) SetField(name string, value ent.Value) error {
	switch name {
	case internalmessage.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case internalmessage.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case internalmessage.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case internalmessage.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	}
	return fmt.Errorf("unknown InternalMessage field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *InternalMessageMutation) AddedFields() []string {
	return nil
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *InternalMessageMutation) AddedField(name string) (ent.Value, bool) {
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *InternalMessageMutation) AddField(name string, value ent.Value) error {
	switch name {
	}
	return fmt.Errorf("unknown InternalMessage numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *InternalMessageMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(internalmessage.FieldTitle) {
		fields = append(fields, internalmessage.FieldTitle)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *InternalMessageMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *InternalMessageMutation) ClearField(name string) error {
	switch name {
	case internalmessage.FieldTitle:
		m.ClearTitle()
		return nil
	}
	return fmt.Errorf("unknown InternalMessage nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *InternalMessageMutation) ResetField(name string) error {
	switch name {
	case internalmessage.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case internalmessage.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case internalmessage.FieldTitle:
		m.ResetTitle()
		return nil
	case internalmessage.FieldContent:
		m.ResetContent()
		return nil
	}
	return fmt.Errorf("unknown InternalMessage field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *InternalMessageMutation) AddedEdges() []string {
	edges := make([]string, 0, 1)
	if m.users != nil {
		edges = append(edges, internalmessage.EdgeUsers)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *InternalMessageMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case internalmessage.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.users))
		for id := range m.users {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *InternalMessageMutation) RemovedEdges() []string {
	edges := make([]string, 0, 1)
	if m.removedusers != nil {
		edges = append(edges, internalmessage.EdgeUsers)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *InternalMessageMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case internalmessage.EdgeUsers:
		ids := make([]ent.Value, 0, len(m.removedusers))
		for id := range m.removedusers {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *InternalMessageMutation) ClearedEdges() []string {
	edges := make([]string, 0, 1)
	if m.clearedusers {
		edges = append(edges, internalmessage.EdgeUsers)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *InternalMessageMutation) EdgeCleared(name string) bool {
	switch name {
	case internalmessage.EdgeUsers:
		return m.clearedusers
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *InternalMessageMutation) ClearEdge(name string) error {
	switch name {
	}
	return fmt.Errorf("unknown InternalMessage unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *InternalMessageMutation) ResetEdge(name string) error {
	switch name {
	case internalmessage.EdgeUsers:
		m.ResetUsers()
		return nil
	}
	return fmt.Errorf("unknown InternalMessage edge %s", name)
}

// ProjectMutation represents an operation that mutates the Project nodes in the graph.
type ProjectMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	user_id       *int64
	adduser_id    *int64
	name          *string
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Project, error)
	predicates    []predicate.Project
}

var _ ent.Mutation = (*ProjectMutation)(nil)

// projectOption allows management of the mutation configuration using functional options.
type projectOption func(*ProjectMutation)

// newProjectMutation creates new mutation for the Project entity.
func newProjectMutation(c config, op Op, opts ...projectOption) *ProjectMutation {
	m := &ProjectMutation{
		config:        c,
		op:            op,
		typ:           TypeProject,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withProjectID sets the ID field of the mutation.
func withProjectID(id int) projectOption {
	return func(m *ProjectMutation) {
		var (
			err   error
			once  sync.Once
			value *Project
		)
		m.oldValue = func(ctx context.Context) (*Project, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Project.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withProject sets the old Project of the mutation.
func withProject(node *Project) projectOption {
	return func(m *ProjectMutation) {
		m.oldValue = func(context.Context) (*Project, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ProjectMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ProjectMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ProjectMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ProjectMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Project.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *ProjectMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ProjectMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Project entity.
// If the Project object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProjectMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ProjectMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ProjectMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ProjectMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Project entity.
// If the Project object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProjectMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ProjectMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetUserID sets the "user_id" field.
func (m *ProjectMutation) SetUserID(i int64) {
	m.user_id = &i
	m.adduser_id = nil
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *ProjectMutation) UserID() (r int64, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Project entity.
// If the Project object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProjectMutation) OldUserID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// AddUserID adds i to the "user_id" field.
func (m *ProjectMutation) AddUserID(i int64) {
	if m.adduser_id != nil {
		*m.adduser_id += i
	} else {
		m.adduser_id = &i
	}
}

// AddedUserID returns the value that was added to the "user_id" field in this mutation.
func (m *ProjectMutation) AddedUserID() (r int64, exists bool) {
	v := m.adduser_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetUserID resets all changes to the "user_id" field.
func (m *ProjectMutation) ResetUserID() {
	m.user_id = nil
	m.adduser_id = nil
}

// SetName sets the "name" field.
func (m *ProjectMutation) SetName(s string) {
	m.name = &s
}

// Name returns the value of the "name" field in the mutation.
func (m *ProjectMutation) Name() (r string, exists bool) {
	v := m.name
	if v == nil {
		return
	}
	return *v, true
}

// OldName returns the old "name" field's value of the Project entity.
// If the Project object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ProjectMutation) OldName(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldName is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldName requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldName: %w", err)
	}
	return oldValue.Name, nil
}

// ResetName resets all changes to the "name" field.
func (m *ProjectMutation) ResetName() {
	m.name = nil
}

// Where appends a list predicates to the ProjectMutation builder.
func (m *ProjectMutation) Where(ps ...predicate.Project) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ProjectMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ProjectMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Project, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ProjectMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ProjectMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Project).
func (m *ProjectMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ProjectMutation) Fields() []string {
	fields := make([]string, 0, 4)
	if m.created_at != nil {
		fields = append(fields, project.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, project.FieldUpdatedAt)
	}
	if m.user_id != nil {
		fields = append(fields, project.FieldUserID)
	}
	if m.name != nil {
		fields = append(fields, project.FieldName)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ProjectMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case project.FieldCreatedAt:
		return m.CreatedAt()
	case project.FieldUpdatedAt:
		return m.UpdatedAt()
	case project.FieldUserID:
		return m.UserID()
	case project.FieldName:
		return m.Name()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ProjectMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case project.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case project.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case project.FieldUserID:
		return m.OldUserID(ctx)
	case project.FieldName:
		return m.OldName(ctx)
	}
	return nil, fmt.Errorf("unknown Project field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProjectMutation) SetField(name string, value ent.Value) error {
	switch name {
	case project.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case project.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case project.FieldUserID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case project.FieldName:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetName(v)
		return nil
	}
	return fmt.Errorf("unknown Project field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ProjectMutation) AddedFields() []string {
	var fields []string
	if m.adduser_id != nil {
		fields = append(fields, project.FieldUserID)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ProjectMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case project.FieldUserID:
		return m.AddedUserID()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ProjectMutation) AddField(name string, value ent.Value) error {
	switch name {
	case project.FieldUserID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUserID(v)
		return nil
	}
	return fmt.Errorf("unknown Project numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ProjectMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ProjectMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ProjectMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Project nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ProjectMutation) ResetField(name string) error {
	switch name {
	case project.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case project.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case project.FieldUserID:
		m.ResetUserID()
		return nil
	case project.FieldName:
		m.ResetName()
		return nil
	}
	return fmt.Errorf("unknown Project field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ProjectMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ProjectMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ProjectMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ProjectMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ProjectMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ProjectMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ProjectMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Project unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ProjectMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Project edge %s", name)
}

// ScreenplayMutation represents an operation that mutates the Screenplay nodes in the graph.
type ScreenplayMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	user_id       *int64
	adduser_id    *int64
	project_id    *int64
	addproject_id *int64
	title         *string
	content       *string
	_type         *int8
	add_type      *int8
	status        *int8
	addstatus     *int8
	clearedFields map[string]struct{}
	done          bool
	oldValue      func(context.Context) (*Screenplay, error)
	predicates    []predicate.Screenplay
}

var _ ent.Mutation = (*ScreenplayMutation)(nil)

// screenplayOption allows management of the mutation configuration using functional options.
type screenplayOption func(*ScreenplayMutation)

// newScreenplayMutation creates new mutation for the Screenplay entity.
func newScreenplayMutation(c config, op Op, opts ...screenplayOption) *ScreenplayMutation {
	m := &ScreenplayMutation{
		config:        c,
		op:            op,
		typ:           TypeScreenplay,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withScreenplayID sets the ID field of the mutation.
func withScreenplayID(id int) screenplayOption {
	return func(m *ScreenplayMutation) {
		var (
			err   error
			once  sync.Once
			value *Screenplay
		)
		m.oldValue = func(ctx context.Context) (*Screenplay, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().Screenplay.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withScreenplay sets the old Screenplay of the mutation.
func withScreenplay(node *Screenplay) screenplayOption {
	return func(m *ScreenplayMutation) {
		m.oldValue = func(context.Context) (*Screenplay, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m ScreenplayMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m ScreenplayMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *ScreenplayMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *ScreenplayMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().Screenplay.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *ScreenplayMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *ScreenplayMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *ScreenplayMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *ScreenplayMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *ScreenplayMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *ScreenplayMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetUserID sets the "user_id" field.
func (m *ScreenplayMutation) SetUserID(i int64) {
	m.user_id = &i
	m.adduser_id = nil
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *ScreenplayMutation) UserID() (r int64, exists bool) {
	v := m.user_id
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldUserID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// AddUserID adds i to the "user_id" field.
func (m *ScreenplayMutation) AddUserID(i int64) {
	if m.adduser_id != nil {
		*m.adduser_id += i
	} else {
		m.adduser_id = &i
	}
}

// AddedUserID returns the value that was added to the "user_id" field in this mutation.
func (m *ScreenplayMutation) AddedUserID() (r int64, exists bool) {
	v := m.adduser_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetUserID resets all changes to the "user_id" field.
func (m *ScreenplayMutation) ResetUserID() {
	m.user_id = nil
	m.adduser_id = nil
}

// SetProjectID sets the "project_id" field.
func (m *ScreenplayMutation) SetProjectID(i int64) {
	m.project_id = &i
	m.addproject_id = nil
}

// ProjectID returns the value of the "project_id" field in the mutation.
func (m *ScreenplayMutation) ProjectID() (r int64, exists bool) {
	v := m.project_id
	if v == nil {
		return
	}
	return *v, true
}

// OldProjectID returns the old "project_id" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldProjectID(ctx context.Context) (v int64, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldProjectID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldProjectID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldProjectID: %w", err)
	}
	return oldValue.ProjectID, nil
}

// AddProjectID adds i to the "project_id" field.
func (m *ScreenplayMutation) AddProjectID(i int64) {
	if m.addproject_id != nil {
		*m.addproject_id += i
	} else {
		m.addproject_id = &i
	}
}

// AddedProjectID returns the value that was added to the "project_id" field in this mutation.
func (m *ScreenplayMutation) AddedProjectID() (r int64, exists bool) {
	v := m.addproject_id
	if v == nil {
		return
	}
	return *v, true
}

// ResetProjectID resets all changes to the "project_id" field.
func (m *ScreenplayMutation) ResetProjectID() {
	m.project_id = nil
	m.addproject_id = nil
}

// SetTitle sets the "title" field.
func (m *ScreenplayMutation) SetTitle(s string) {
	m.title = &s
}

// Title returns the value of the "title" field in the mutation.
func (m *ScreenplayMutation) Title() (r string, exists bool) {
	v := m.title
	if v == nil {
		return
	}
	return *v, true
}

// OldTitle returns the old "title" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldTitle(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTitle is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTitle requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTitle: %w", err)
	}
	return oldValue.Title, nil
}

// ResetTitle resets all changes to the "title" field.
func (m *ScreenplayMutation) ResetTitle() {
	m.title = nil
}

// SetContent sets the "content" field.
func (m *ScreenplayMutation) SetContent(s string) {
	m.content = &s
}

// Content returns the value of the "content" field in the mutation.
func (m *ScreenplayMutation) Content() (r string, exists bool) {
	v := m.content
	if v == nil {
		return
	}
	return *v, true
}

// OldContent returns the old "content" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldContent(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldContent is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldContent requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldContent: %w", err)
	}
	return oldValue.Content, nil
}

// ResetContent resets all changes to the "content" field.
func (m *ScreenplayMutation) ResetContent() {
	m.content = nil
}

// SetType sets the "type" field.
func (m *ScreenplayMutation) SetType(i int8) {
	m._type = &i
	m.add_type = nil
}

// GetType returns the value of the "type" field in the mutation.
func (m *ScreenplayMutation) GetType() (r int8, exists bool) {
	v := m._type
	if v == nil {
		return
	}
	return *v, true
}

// OldType returns the old "type" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldType(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldType is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldType requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldType: %w", err)
	}
	return oldValue.Type, nil
}

// AddType adds i to the "type" field.
func (m *ScreenplayMutation) AddType(i int8) {
	if m.add_type != nil {
		*m.add_type += i
	} else {
		m.add_type = &i
	}
}

// AddedType returns the value that was added to the "type" field in this mutation.
func (m *ScreenplayMutation) AddedType() (r int8, exists bool) {
	v := m.add_type
	if v == nil {
		return
	}
	return *v, true
}

// ResetType resets all changes to the "type" field.
func (m *ScreenplayMutation) ResetType() {
	m._type = nil
	m.add_type = nil
}

// SetStatus sets the "status" field.
func (m *ScreenplayMutation) SetStatus(i int8) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *ScreenplayMutation) Status() (r int8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the Screenplay entity.
// If the Screenplay object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *ScreenplayMutation) OldStatus(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *ScreenplayMutation) AddStatus(i int8) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *ScreenplayMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *ScreenplayMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// Where appends a list predicates to the ScreenplayMutation builder.
func (m *ScreenplayMutation) Where(ps ...predicate.Screenplay) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the ScreenplayMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *ScreenplayMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.Screenplay, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *ScreenplayMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *ScreenplayMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (Screenplay).
func (m *ScreenplayMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *ScreenplayMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_at != nil {
		fields = append(fields, screenplay.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, screenplay.FieldUpdatedAt)
	}
	if m.user_id != nil {
		fields = append(fields, screenplay.FieldUserID)
	}
	if m.project_id != nil {
		fields = append(fields, screenplay.FieldProjectID)
	}
	if m.title != nil {
		fields = append(fields, screenplay.FieldTitle)
	}
	if m.content != nil {
		fields = append(fields, screenplay.FieldContent)
	}
	if m._type != nil {
		fields = append(fields, screenplay.FieldType)
	}
	if m.status != nil {
		fields = append(fields, screenplay.FieldStatus)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *ScreenplayMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case screenplay.FieldCreatedAt:
		return m.CreatedAt()
	case screenplay.FieldUpdatedAt:
		return m.UpdatedAt()
	case screenplay.FieldUserID:
		return m.UserID()
	case screenplay.FieldProjectID:
		return m.ProjectID()
	case screenplay.FieldTitle:
		return m.Title()
	case screenplay.FieldContent:
		return m.Content()
	case screenplay.FieldType:
		return m.GetType()
	case screenplay.FieldStatus:
		return m.Status()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *ScreenplayMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case screenplay.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case screenplay.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case screenplay.FieldUserID:
		return m.OldUserID(ctx)
	case screenplay.FieldProjectID:
		return m.OldProjectID(ctx)
	case screenplay.FieldTitle:
		return m.OldTitle(ctx)
	case screenplay.FieldContent:
		return m.OldContent(ctx)
	case screenplay.FieldType:
		return m.OldType(ctx)
	case screenplay.FieldStatus:
		return m.OldStatus(ctx)
	}
	return nil, fmt.Errorf("unknown Screenplay field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ScreenplayMutation) SetField(name string, value ent.Value) error {
	switch name {
	case screenplay.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case screenplay.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case screenplay.FieldUserID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case screenplay.FieldProjectID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetProjectID(v)
		return nil
	case screenplay.FieldTitle:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTitle(v)
		return nil
	case screenplay.FieldContent:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetContent(v)
		return nil
	case screenplay.FieldType:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetType(v)
		return nil
	case screenplay.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	}
	return fmt.Errorf("unknown Screenplay field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *ScreenplayMutation) AddedFields() []string {
	var fields []string
	if m.adduser_id != nil {
		fields = append(fields, screenplay.FieldUserID)
	}
	if m.addproject_id != nil {
		fields = append(fields, screenplay.FieldProjectID)
	}
	if m.add_type != nil {
		fields = append(fields, screenplay.FieldType)
	}
	if m.addstatus != nil {
		fields = append(fields, screenplay.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *ScreenplayMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case screenplay.FieldUserID:
		return m.AddedUserID()
	case screenplay.FieldProjectID:
		return m.AddedProjectID()
	case screenplay.FieldType:
		return m.AddedType()
	case screenplay.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *ScreenplayMutation) AddField(name string, value ent.Value) error {
	switch name {
	case screenplay.FieldUserID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddUserID(v)
		return nil
	case screenplay.FieldProjectID:
		v, ok := value.(int64)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddProjectID(v)
		return nil
	case screenplay.FieldType:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddType(v)
		return nil
	case screenplay.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown Screenplay numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *ScreenplayMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *ScreenplayMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *ScreenplayMutation) ClearField(name string) error {
	return fmt.Errorf("unknown Screenplay nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *ScreenplayMutation) ResetField(name string) error {
	switch name {
	case screenplay.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case screenplay.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case screenplay.FieldUserID:
		m.ResetUserID()
		return nil
	case screenplay.FieldProjectID:
		m.ResetProjectID()
		return nil
	case screenplay.FieldTitle:
		m.ResetTitle()
		return nil
	case screenplay.FieldContent:
		m.ResetContent()
		return nil
	case screenplay.FieldType:
		m.ResetType()
		return nil
	case screenplay.FieldStatus:
		m.ResetStatus()
		return nil
	}
	return fmt.Errorf("unknown Screenplay field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *ScreenplayMutation) AddedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *ScreenplayMutation) AddedIDs(name string) []ent.Value {
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *ScreenplayMutation) RemovedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *ScreenplayMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *ScreenplayMutation) ClearedEdges() []string {
	edges := make([]string, 0, 0)
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *ScreenplayMutation) EdgeCleared(name string) bool {
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *ScreenplayMutation) ClearEdge(name string) error {
	return fmt.Errorf("unknown Screenplay unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *ScreenplayMutation) ResetEdge(name string) error {
	return fmt.Errorf("unknown Screenplay edge %s", name)
}

// TestCodeMutation represents an operation that mutates the TestCode nodes in the graph.
type TestCodeMutation struct {
	config
	op            Op
	typ           string
	id            *int
	created_at    *time.Time
	updated_at    *time.Time
	code          *string
	device        *string
	status        *int8
	addstatus     *int8
	expired_at    *time.Time
	clearedFields map[string]struct{}
	user          *int
	cleareduser   bool
	batch         *int
	clearedbatch  bool
	done          bool
	oldValue      func(context.Context) (*TestCode, error)
	predicates    []predicate.TestCode
}

var _ ent.Mutation = (*TestCodeMutation)(nil)

// testcodeOption allows management of the mutation configuration using functional options.
type testcodeOption func(*TestCodeMutation)

// newTestCodeMutation creates new mutation for the TestCode entity.
func newTestCodeMutation(c config, op Op, opts ...testcodeOption) *TestCodeMutation {
	m := &TestCodeMutation{
		config:        c,
		op:            op,
		typ:           TypeTestCode,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withTestCodeID sets the ID field of the mutation.
func withTestCodeID(id int) testcodeOption {
	return func(m *TestCodeMutation) {
		var (
			err   error
			once  sync.Once
			value *TestCode
		)
		m.oldValue = func(ctx context.Context) (*TestCode, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().TestCode.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withTestCode sets the old TestCode of the mutation.
func withTestCode(node *TestCode) testcodeOption {
	return func(m *TestCodeMutation) {
		m.oldValue = func(context.Context) (*TestCode, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m TestCodeMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m TestCodeMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *TestCodeMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *TestCodeMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().TestCode.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *TestCodeMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *TestCodeMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *TestCodeMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *TestCodeMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *TestCodeMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *TestCodeMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetBatchID sets the "batch_id" field.
func (m *TestCodeMutation) SetBatchID(i int) {
	m.batch = &i
}

// BatchID returns the value of the "batch_id" field in the mutation.
func (m *TestCodeMutation) BatchID() (r int, exists bool) {
	v := m.batch
	if v == nil {
		return
	}
	return *v, true
}

// OldBatchID returns the old "batch_id" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldBatchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBatchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBatchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBatchID: %w", err)
	}
	return oldValue.BatchID, nil
}

// ResetBatchID resets all changes to the "batch_id" field.
func (m *TestCodeMutation) ResetBatchID() {
	m.batch = nil
}

// SetCode sets the "code" field.
func (m *TestCodeMutation) SetCode(s string) {
	m.code = &s
}

// Code returns the value of the "code" field in the mutation.
func (m *TestCodeMutation) Code() (r string, exists bool) {
	v := m.code
	if v == nil {
		return
	}
	return *v, true
}

// OldCode returns the old "code" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldCode(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCode is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCode requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCode: %w", err)
	}
	return oldValue.Code, nil
}

// ResetCode resets all changes to the "code" field.
func (m *TestCodeMutation) ResetCode() {
	m.code = nil
}

// SetDevice sets the "device" field.
func (m *TestCodeMutation) SetDevice(s string) {
	m.device = &s
}

// Device returns the value of the "device" field in the mutation.
func (m *TestCodeMutation) Device() (r string, exists bool) {
	v := m.device
	if v == nil {
		return
	}
	return *v, true
}

// OldDevice returns the old "device" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldDevice(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldDevice is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldDevice requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldDevice: %w", err)
	}
	return oldValue.Device, nil
}

// ClearDevice clears the value of the "device" field.
func (m *TestCodeMutation) ClearDevice() {
	m.device = nil
	m.clearedFields[testcode.FieldDevice] = struct{}{}
}

// DeviceCleared returns if the "device" field was cleared in this mutation.
func (m *TestCodeMutation) DeviceCleared() bool {
	_, ok := m.clearedFields[testcode.FieldDevice]
	return ok
}

// ResetDevice resets all changes to the "device" field.
func (m *TestCodeMutation) ResetDevice() {
	m.device = nil
	delete(m.clearedFields, testcode.FieldDevice)
}

// SetStatus sets the "status" field.
func (m *TestCodeMutation) SetStatus(i int8) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *TestCodeMutation) Status() (r int8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldStatus(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *TestCodeMutation) AddStatus(i int8) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *TestCodeMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *TestCodeMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// SetExpiredAt sets the "expired_at" field.
func (m *TestCodeMutation) SetExpiredAt(t time.Time) {
	m.expired_at = &t
}

// ExpiredAt returns the value of the "expired_at" field in the mutation.
func (m *TestCodeMutation) ExpiredAt() (r time.Time, exists bool) {
	v := m.expired_at
	if v == nil {
		return
	}
	return *v, true
}

// OldExpiredAt returns the old "expired_at" field's value of the TestCode entity.
// If the TestCode object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *TestCodeMutation) OldExpiredAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldExpiredAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldExpiredAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldExpiredAt: %w", err)
	}
	return oldValue.ExpiredAt, nil
}

// ClearExpiredAt clears the value of the "expired_at" field.
func (m *TestCodeMutation) ClearExpiredAt() {
	m.expired_at = nil
	m.clearedFields[testcode.FieldExpiredAt] = struct{}{}
}

// ExpiredAtCleared returns if the "expired_at" field was cleared in this mutation.
func (m *TestCodeMutation) ExpiredAtCleared() bool {
	_, ok := m.clearedFields[testcode.FieldExpiredAt]
	return ok
}

// ResetExpiredAt resets all changes to the "expired_at" field.
func (m *TestCodeMutation) ResetExpiredAt() {
	m.expired_at = nil
	delete(m.clearedFields, testcode.FieldExpiredAt)
}

// SetUserID sets the "user" edge to the User entity by id.
func (m *TestCodeMutation) SetUserID(id int) {
	m.user = &id
}

// ClearUser clears the "user" edge to the User entity.
func (m *TestCodeMutation) ClearUser() {
	m.cleareduser = true
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *TestCodeMutation) UserCleared() bool {
	return m.cleareduser
}

// UserID returns the "user" edge ID in the mutation.
func (m *TestCodeMutation) UserID() (id int, exists bool) {
	if m.user != nil {
		return *m.user, true
	}
	return
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *TestCodeMutation) UserIDs() (ids []int) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *TestCodeMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// ClearBatch clears the "batch" edge to the Batch entity.
func (m *TestCodeMutation) ClearBatch() {
	m.clearedbatch = true
	m.clearedFields[testcode.FieldBatchID] = struct{}{}
}

// BatchCleared reports if the "batch" edge to the Batch entity was cleared.
func (m *TestCodeMutation) BatchCleared() bool {
	return m.clearedbatch
}

// BatchIDs returns the "batch" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// BatchID instead. It exists only for internal usage by the builders.
func (m *TestCodeMutation) BatchIDs() (ids []int) {
	if id := m.batch; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetBatch resets all changes to the "batch" edge.
func (m *TestCodeMutation) ResetBatch() {
	m.batch = nil
	m.clearedbatch = false
}

// Where appends a list predicates to the TestCodeMutation builder.
func (m *TestCodeMutation) Where(ps ...predicate.TestCode) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the TestCodeMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *TestCodeMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.TestCode, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *TestCodeMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *TestCodeMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (TestCode).
func (m *TestCodeMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *TestCodeMutation) Fields() []string {
	fields := make([]string, 0, 7)
	if m.created_at != nil {
		fields = append(fields, testcode.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, testcode.FieldUpdatedAt)
	}
	if m.batch != nil {
		fields = append(fields, testcode.FieldBatchID)
	}
	if m.code != nil {
		fields = append(fields, testcode.FieldCode)
	}
	if m.device != nil {
		fields = append(fields, testcode.FieldDevice)
	}
	if m.status != nil {
		fields = append(fields, testcode.FieldStatus)
	}
	if m.expired_at != nil {
		fields = append(fields, testcode.FieldExpiredAt)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *TestCodeMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case testcode.FieldCreatedAt:
		return m.CreatedAt()
	case testcode.FieldUpdatedAt:
		return m.UpdatedAt()
	case testcode.FieldBatchID:
		return m.BatchID()
	case testcode.FieldCode:
		return m.Code()
	case testcode.FieldDevice:
		return m.Device()
	case testcode.FieldStatus:
		return m.Status()
	case testcode.FieldExpiredAt:
		return m.ExpiredAt()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *TestCodeMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case testcode.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case testcode.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case testcode.FieldBatchID:
		return m.OldBatchID(ctx)
	case testcode.FieldCode:
		return m.OldCode(ctx)
	case testcode.FieldDevice:
		return m.OldDevice(ctx)
	case testcode.FieldStatus:
		return m.OldStatus(ctx)
	case testcode.FieldExpiredAt:
		return m.OldExpiredAt(ctx)
	}
	return nil, fmt.Errorf("unknown TestCode field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestCodeMutation) SetField(name string, value ent.Value) error {
	switch name {
	case testcode.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case testcode.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case testcode.FieldBatchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBatchID(v)
		return nil
	case testcode.FieldCode:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCode(v)
		return nil
	case testcode.FieldDevice:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetDevice(v)
		return nil
	case testcode.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	case testcode.FieldExpiredAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetExpiredAt(v)
		return nil
	}
	return fmt.Errorf("unknown TestCode field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *TestCodeMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, testcode.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *TestCodeMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case testcode.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *TestCodeMutation) AddField(name string, value ent.Value) error {
	switch name {
	case testcode.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown TestCode numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *TestCodeMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(testcode.FieldDevice) {
		fields = append(fields, testcode.FieldDevice)
	}
	if m.FieldCleared(testcode.FieldExpiredAt) {
		fields = append(fields, testcode.FieldExpiredAt)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *TestCodeMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *TestCodeMutation) ClearField(name string) error {
	switch name {
	case testcode.FieldDevice:
		m.ClearDevice()
		return nil
	case testcode.FieldExpiredAt:
		m.ClearExpiredAt()
		return nil
	}
	return fmt.Errorf("unknown TestCode nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *TestCodeMutation) ResetField(name string) error {
	switch name {
	case testcode.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case testcode.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case testcode.FieldBatchID:
		m.ResetBatchID()
		return nil
	case testcode.FieldCode:
		m.ResetCode()
		return nil
	case testcode.FieldDevice:
		m.ResetDevice()
		return nil
	case testcode.FieldStatus:
		m.ResetStatus()
		return nil
	case testcode.FieldExpiredAt:
		m.ResetExpiredAt()
		return nil
	}
	return fmt.Errorf("unknown TestCode field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *TestCodeMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.user != nil {
		edges = append(edges, testcode.EdgeUser)
	}
	if m.batch != nil {
		edges = append(edges, testcode.EdgeBatch)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *TestCodeMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case testcode.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	case testcode.EdgeBatch:
		if id := m.batch; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *TestCodeMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *TestCodeMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *TestCodeMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.cleareduser {
		edges = append(edges, testcode.EdgeUser)
	}
	if m.clearedbatch {
		edges = append(edges, testcode.EdgeBatch)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *TestCodeMutation) EdgeCleared(name string) bool {
	switch name {
	case testcode.EdgeUser:
		return m.cleareduser
	case testcode.EdgeBatch:
		return m.clearedbatch
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *TestCodeMutation) ClearEdge(name string) error {
	switch name {
	case testcode.EdgeUser:
		m.ClearUser()
		return nil
	case testcode.EdgeBatch:
		m.ClearBatch()
		return nil
	}
	return fmt.Errorf("unknown TestCode unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *TestCodeMutation) ResetEdge(name string) error {
	switch name {
	case testcode.EdgeUser:
		m.ResetUser()
		return nil
	case testcode.EdgeBatch:
		m.ResetBatch()
		return nil
	}
	return fmt.Errorf("unknown TestCode edge %s", name)
}

// UserMutation represents an operation that mutates the User nodes in the graph.
type UserMutation struct {
	config
	op              Op
	typ             string
	id              *int
	created_at      *time.Time
	updated_at      *time.Time
	username        *string
	password        *string
	email           *string
	status          *int8
	addstatus       *int8
	clearedFields   map[string]struct{}
	code            *int
	clearedcode     bool
	batch           *int
	clearedbatch    bool
	messages        map[int]struct{}
	removedmessages map[int]struct{}
	clearedmessages bool
	done            bool
	oldValue        func(context.Context) (*User, error)
	predicates      []predicate.User
}

var _ ent.Mutation = (*UserMutation)(nil)

// userOption allows management of the mutation configuration using functional options.
type userOption func(*UserMutation)

// newUserMutation creates new mutation for the User entity.
func newUserMutation(c config, op Op, opts ...userOption) *UserMutation {
	m := &UserMutation{
		config:        c,
		op:            op,
		typ:           TypeUser,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserID sets the ID field of the mutation.
func withUserID(id int) userOption {
	return func(m *UserMutation) {
		var (
			err   error
			once  sync.Once
			value *User
		)
		m.oldValue = func(ctx context.Context) (*User, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().User.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUser sets the old User of the mutation.
func withUser(node *User) userOption {
	return func(m *UserMutation) {
		m.oldValue = func(context.Context) (*User, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().User.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *UserMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetBatchID sets the "batch_id" field.
func (m *UserMutation) SetBatchID(i int) {
	m.batch = &i
}

// BatchID returns the value of the "batch_id" field in the mutation.
func (m *UserMutation) BatchID() (r int, exists bool) {
	v := m.batch
	if v == nil {
		return
	}
	return *v, true
}

// OldBatchID returns the old "batch_id" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldBatchID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldBatchID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldBatchID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldBatchID: %w", err)
	}
	return oldValue.BatchID, nil
}

// ResetBatchID resets all changes to the "batch_id" field.
func (m *UserMutation) ResetBatchID() {
	m.batch = nil
}

// SetTestCodeID sets the "test_code_id" field.
func (m *UserMutation) SetTestCodeID(i int) {
	m.code = &i
}

// TestCodeID returns the value of the "test_code_id" field in the mutation.
func (m *UserMutation) TestCodeID() (r int, exists bool) {
	v := m.code
	if v == nil {
		return
	}
	return *v, true
}

// OldTestCodeID returns the old "test_code_id" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldTestCodeID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldTestCodeID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldTestCodeID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldTestCodeID: %w", err)
	}
	return oldValue.TestCodeID, nil
}

// ResetTestCodeID resets all changes to the "test_code_id" field.
func (m *UserMutation) ResetTestCodeID() {
	m.code = nil
}

// SetUsername sets the "username" field.
func (m *UserMutation) SetUsername(s string) {
	m.username = &s
}

// Username returns the value of the "username" field in the mutation.
func (m *UserMutation) Username() (r string, exists bool) {
	v := m.username
	if v == nil {
		return
	}
	return *v, true
}

// OldUsername returns the old "username" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldUsername(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUsername is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUsername requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUsername: %w", err)
	}
	return oldValue.Username, nil
}

// ResetUsername resets all changes to the "username" field.
func (m *UserMutation) ResetUsername() {
	m.username = nil
}

// SetPassword sets the "password" field.
func (m *UserMutation) SetPassword(s string) {
	m.password = &s
}

// Password returns the value of the "password" field in the mutation.
func (m *UserMutation) Password() (r string, exists bool) {
	v := m.password
	if v == nil {
		return
	}
	return *v, true
}

// OldPassword returns the old "password" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldPassword(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldPassword is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldPassword requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldPassword: %w", err)
	}
	return oldValue.Password, nil
}

// ResetPassword resets all changes to the "password" field.
func (m *UserMutation) ResetPassword() {
	m.password = nil
}

// SetEmail sets the "email" field.
func (m *UserMutation) SetEmail(s string) {
	m.email = &s
}

// Email returns the value of the "email" field in the mutation.
func (m *UserMutation) Email() (r string, exists bool) {
	v := m.email
	if v == nil {
		return
	}
	return *v, true
}

// OldEmail returns the old "email" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldEmail(ctx context.Context) (v string, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldEmail is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldEmail requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldEmail: %w", err)
	}
	return oldValue.Email, nil
}

// ClearEmail clears the value of the "email" field.
func (m *UserMutation) ClearEmail() {
	m.email = nil
	m.clearedFields[user.FieldEmail] = struct{}{}
}

// EmailCleared returns if the "email" field was cleared in this mutation.
func (m *UserMutation) EmailCleared() bool {
	_, ok := m.clearedFields[user.FieldEmail]
	return ok
}

// ResetEmail resets all changes to the "email" field.
func (m *UserMutation) ResetEmail() {
	m.email = nil
	delete(m.clearedFields, user.FieldEmail)
}

// SetStatus sets the "status" field.
func (m *UserMutation) SetStatus(i int8) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *UserMutation) Status() (r int8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the User entity.
// If the User object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserMutation) OldStatus(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *UserMutation) AddStatus(i int8) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *UserMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *UserMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// SetCodeID sets the "code" edge to the TestCode entity by id.
func (m *UserMutation) SetCodeID(id int) {
	m.code = &id
}

// ClearCode clears the "code" edge to the TestCode entity.
func (m *UserMutation) ClearCode() {
	m.clearedcode = true
	m.clearedFields[user.FieldTestCodeID] = struct{}{}
}

// CodeCleared reports if the "code" edge to the TestCode entity was cleared.
func (m *UserMutation) CodeCleared() bool {
	return m.clearedcode
}

// CodeID returns the "code" edge ID in the mutation.
func (m *UserMutation) CodeID() (id int, exists bool) {
	if m.code != nil {
		return *m.code, true
	}
	return
}

// CodeIDs returns the "code" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// CodeID instead. It exists only for internal usage by the builders.
func (m *UserMutation) CodeIDs() (ids []int) {
	if id := m.code; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetCode resets all changes to the "code" edge.
func (m *UserMutation) ResetCode() {
	m.code = nil
	m.clearedcode = false
}

// ClearBatch clears the "batch" edge to the Batch entity.
func (m *UserMutation) ClearBatch() {
	m.clearedbatch = true
	m.clearedFields[user.FieldBatchID] = struct{}{}
}

// BatchCleared reports if the "batch" edge to the Batch entity was cleared.
func (m *UserMutation) BatchCleared() bool {
	return m.clearedbatch
}

// BatchIDs returns the "batch" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// BatchID instead. It exists only for internal usage by the builders.
func (m *UserMutation) BatchIDs() (ids []int) {
	if id := m.batch; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetBatch resets all changes to the "batch" edge.
func (m *UserMutation) ResetBatch() {
	m.batch = nil
	m.clearedbatch = false
}

// AddMessageIDs adds the "messages" edge to the UserInternalMessage entity by ids.
func (m *UserMutation) AddMessageIDs(ids ...int) {
	if m.messages == nil {
		m.messages = make(map[int]struct{})
	}
	for i := range ids {
		m.messages[ids[i]] = struct{}{}
	}
}

// ClearMessages clears the "messages" edge to the UserInternalMessage entity.
func (m *UserMutation) ClearMessages() {
	m.clearedmessages = true
}

// MessagesCleared reports if the "messages" edge to the UserInternalMessage entity was cleared.
func (m *UserMutation) MessagesCleared() bool {
	return m.clearedmessages
}

// RemoveMessageIDs removes the "messages" edge to the UserInternalMessage entity by IDs.
func (m *UserMutation) RemoveMessageIDs(ids ...int) {
	if m.removedmessages == nil {
		m.removedmessages = make(map[int]struct{})
	}
	for i := range ids {
		delete(m.messages, ids[i])
		m.removedmessages[ids[i]] = struct{}{}
	}
}

// RemovedMessages returns the removed IDs of the "messages" edge to the UserInternalMessage entity.
func (m *UserMutation) RemovedMessagesIDs() (ids []int) {
	for id := range m.removedmessages {
		ids = append(ids, id)
	}
	return
}

// MessagesIDs returns the "messages" edge IDs in the mutation.
func (m *UserMutation) MessagesIDs() (ids []int) {
	for id := range m.messages {
		ids = append(ids, id)
	}
	return
}

// ResetMessages resets all changes to the "messages" edge.
func (m *UserMutation) ResetMessages() {
	m.messages = nil
	m.clearedmessages = false
	m.removedmessages = nil
}

// Where appends a list predicates to the UserMutation builder.
func (m *UserMutation) Where(ps ...predicate.User) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.User, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (User).
func (m *UserMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserMutation) Fields() []string {
	fields := make([]string, 0, 8)
	if m.created_at != nil {
		fields = append(fields, user.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, user.FieldUpdatedAt)
	}
	if m.batch != nil {
		fields = append(fields, user.FieldBatchID)
	}
	if m.code != nil {
		fields = append(fields, user.FieldTestCodeID)
	}
	if m.username != nil {
		fields = append(fields, user.FieldUsername)
	}
	if m.password != nil {
		fields = append(fields, user.FieldPassword)
	}
	if m.email != nil {
		fields = append(fields, user.FieldEmail)
	}
	if m.status != nil {
		fields = append(fields, user.FieldStatus)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case user.FieldCreatedAt:
		return m.CreatedAt()
	case user.FieldUpdatedAt:
		return m.UpdatedAt()
	case user.FieldBatchID:
		return m.BatchID()
	case user.FieldTestCodeID:
		return m.TestCodeID()
	case user.FieldUsername:
		return m.Username()
	case user.FieldPassword:
		return m.Password()
	case user.FieldEmail:
		return m.Email()
	case user.FieldStatus:
		return m.Status()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case user.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case user.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case user.FieldBatchID:
		return m.OldBatchID(ctx)
	case user.FieldTestCodeID:
		return m.OldTestCodeID(ctx)
	case user.FieldUsername:
		return m.OldUsername(ctx)
	case user.FieldPassword:
		return m.OldPassword(ctx)
	case user.FieldEmail:
		return m.OldEmail(ctx)
	case user.FieldStatus:
		return m.OldStatus(ctx)
	}
	return nil, fmt.Errorf("unknown User field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) SetField(name string, value ent.Value) error {
	switch name {
	case user.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case user.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case user.FieldBatchID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetBatchID(v)
		return nil
	case user.FieldTestCodeID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetTestCodeID(v)
		return nil
	case user.FieldUsername:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUsername(v)
		return nil
	case user.FieldPassword:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetPassword(v)
		return nil
	case user.FieldEmail:
		v, ok := value.(string)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetEmail(v)
		return nil
	case user.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, user.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case user.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserMutation) AddField(name string, value ent.Value) error {
	switch name {
	case user.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown User numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserMutation) ClearedFields() []string {
	var fields []string
	if m.FieldCleared(user.FieldEmail) {
		fields = append(fields, user.FieldEmail)
	}
	return fields
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserMutation) ClearField(name string) error {
	switch name {
	case user.FieldEmail:
		m.ClearEmail()
		return nil
	}
	return fmt.Errorf("unknown User nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserMutation) ResetField(name string) error {
	switch name {
	case user.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case user.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case user.FieldBatchID:
		m.ResetBatchID()
		return nil
	case user.FieldTestCodeID:
		m.ResetTestCodeID()
		return nil
	case user.FieldUsername:
		m.ResetUsername()
		return nil
	case user.FieldPassword:
		m.ResetPassword()
		return nil
	case user.FieldEmail:
		m.ResetEmail()
		return nil
	case user.FieldStatus:
		m.ResetStatus()
		return nil
	}
	return fmt.Errorf("unknown User field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserMutation) AddedEdges() []string {
	edges := make([]string, 0, 3)
	if m.code != nil {
		edges = append(edges, user.EdgeCode)
	}
	if m.batch != nil {
		edges = append(edges, user.EdgeBatch)
	}
	if m.messages != nil {
		edges = append(edges, user.EdgeMessages)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeCode:
		if id := m.code; id != nil {
			return []ent.Value{*id}
		}
	case user.EdgeBatch:
		if id := m.batch; id != nil {
			return []ent.Value{*id}
		}
	case user.EdgeMessages:
		ids := make([]ent.Value, 0, len(m.messages))
		for id := range m.messages {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserMutation) RemovedEdges() []string {
	edges := make([]string, 0, 3)
	if m.removedmessages != nil {
		edges = append(edges, user.EdgeMessages)
	}
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserMutation) RemovedIDs(name string) []ent.Value {
	switch name {
	case user.EdgeMessages:
		ids := make([]ent.Value, 0, len(m.removedmessages))
		for id := range m.removedmessages {
			ids = append(ids, id)
		}
		return ids
	}
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserMutation) ClearedEdges() []string {
	edges := make([]string, 0, 3)
	if m.clearedcode {
		edges = append(edges, user.EdgeCode)
	}
	if m.clearedbatch {
		edges = append(edges, user.EdgeBatch)
	}
	if m.clearedmessages {
		edges = append(edges, user.EdgeMessages)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserMutation) EdgeCleared(name string) bool {
	switch name {
	case user.EdgeCode:
		return m.clearedcode
	case user.EdgeBatch:
		return m.clearedbatch
	case user.EdgeMessages:
		return m.clearedmessages
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserMutation) ClearEdge(name string) error {
	switch name {
	case user.EdgeCode:
		m.ClearCode()
		return nil
	case user.EdgeBatch:
		m.ClearBatch()
		return nil
	}
	return fmt.Errorf("unknown User unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserMutation) ResetEdge(name string) error {
	switch name {
	case user.EdgeCode:
		m.ResetCode()
		return nil
	case user.EdgeBatch:
		m.ResetBatch()
		return nil
	case user.EdgeMessages:
		m.ResetMessages()
		return nil
	}
	return fmt.Errorf("unknown User edge %s", name)
}

// UserInternalMessageMutation represents an operation that mutates the UserInternalMessage nodes in the graph.
type UserInternalMessageMutation struct {
	config
	op             Op
	typ            string
	id             *int
	created_at     *time.Time
	updated_at     *time.Time
	status         *int8
	addstatus      *int8
	clearedFields  map[string]struct{}
	message        *int
	clearedmessage bool
	user           *int
	cleareduser    bool
	done           bool
	oldValue       func(context.Context) (*UserInternalMessage, error)
	predicates     []predicate.UserInternalMessage
}

var _ ent.Mutation = (*UserInternalMessageMutation)(nil)

// userinternalmessageOption allows management of the mutation configuration using functional options.
type userinternalmessageOption func(*UserInternalMessageMutation)

// newUserInternalMessageMutation creates new mutation for the UserInternalMessage entity.
func newUserInternalMessageMutation(c config, op Op, opts ...userinternalmessageOption) *UserInternalMessageMutation {
	m := &UserInternalMessageMutation{
		config:        c,
		op:            op,
		typ:           TypeUserInternalMessage,
		clearedFields: make(map[string]struct{}),
	}
	for _, opt := range opts {
		opt(m)
	}
	return m
}

// withUserInternalMessageID sets the ID field of the mutation.
func withUserInternalMessageID(id int) userinternalmessageOption {
	return func(m *UserInternalMessageMutation) {
		var (
			err   error
			once  sync.Once
			value *UserInternalMessage
		)
		m.oldValue = func(ctx context.Context) (*UserInternalMessage, error) {
			once.Do(func() {
				if m.done {
					err = errors.New("querying old values post mutation is not allowed")
				} else {
					value, err = m.Client().UserInternalMessage.Get(ctx, id)
				}
			})
			return value, err
		}
		m.id = &id
	}
}

// withUserInternalMessage sets the old UserInternalMessage of the mutation.
func withUserInternalMessage(node *UserInternalMessage) userinternalmessageOption {
	return func(m *UserInternalMessageMutation) {
		m.oldValue = func(context.Context) (*UserInternalMessage, error) {
			return node, nil
		}
		m.id = &node.ID
	}
}

// Client returns a new `ent.Client` from the mutation. If the mutation was
// executed in a transaction (ent.Tx), a transactional client is returned.
func (m UserInternalMessageMutation) Client() *Client {
	client := &Client{config: m.config}
	client.init()
	return client
}

// Tx returns an `ent.Tx` for mutations that were executed in transactions;
// it returns an error otherwise.
func (m UserInternalMessageMutation) Tx() (*Tx, error) {
	if _, ok := m.driver.(*txDriver); !ok {
		return nil, errors.New("ent: mutation is not running in a transaction")
	}
	tx := &Tx{config: m.config}
	tx.init()
	return tx, nil
}

// ID returns the ID value in the mutation. Note that the ID is only available
// if it was provided to the builder or after it was returned from the database.
func (m *UserInternalMessageMutation) ID() (id int, exists bool) {
	if m.id == nil {
		return
	}
	return *m.id, true
}

// IDs queries the database and returns the entity ids that match the mutation's predicate.
// That means, if the mutation is applied within a transaction with an isolation level such
// as sql.LevelSerializable, the returned ids match the ids of the rows that will be updated
// or updated by the mutation.
func (m *UserInternalMessageMutation) IDs(ctx context.Context) ([]int, error) {
	switch {
	case m.op.Is(OpUpdateOne | OpDeleteOne):
		id, exists := m.ID()
		if exists {
			return []int{id}, nil
		}
		fallthrough
	case m.op.Is(OpUpdate | OpDelete):
		return m.Client().UserInternalMessage.Query().Where(m.predicates...).IDs(ctx)
	default:
		return nil, fmt.Errorf("IDs is not allowed on %s operations", m.op)
	}
}

// SetCreatedAt sets the "created_at" field.
func (m *UserInternalMessageMutation) SetCreatedAt(t time.Time) {
	m.created_at = &t
}

// CreatedAt returns the value of the "created_at" field in the mutation.
func (m *UserInternalMessageMutation) CreatedAt() (r time.Time, exists bool) {
	v := m.created_at
	if v == nil {
		return
	}
	return *v, true
}

// OldCreatedAt returns the old "created_at" field's value of the UserInternalMessage entity.
// If the UserInternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserInternalMessageMutation) OldCreatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldCreatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldCreatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldCreatedAt: %w", err)
	}
	return oldValue.CreatedAt, nil
}

// ResetCreatedAt resets all changes to the "created_at" field.
func (m *UserInternalMessageMutation) ResetCreatedAt() {
	m.created_at = nil
}

// SetUpdatedAt sets the "updated_at" field.
func (m *UserInternalMessageMutation) SetUpdatedAt(t time.Time) {
	m.updated_at = &t
}

// UpdatedAt returns the value of the "updated_at" field in the mutation.
func (m *UserInternalMessageMutation) UpdatedAt() (r time.Time, exists bool) {
	v := m.updated_at
	if v == nil {
		return
	}
	return *v, true
}

// OldUpdatedAt returns the old "updated_at" field's value of the UserInternalMessage entity.
// If the UserInternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserInternalMessageMutation) OldUpdatedAt(ctx context.Context) (v time.Time, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUpdatedAt is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUpdatedAt requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUpdatedAt: %w", err)
	}
	return oldValue.UpdatedAt, nil
}

// ResetUpdatedAt resets all changes to the "updated_at" field.
func (m *UserInternalMessageMutation) ResetUpdatedAt() {
	m.updated_at = nil
}

// SetInternalMessageID sets the "internal_message_id" field.
func (m *UserInternalMessageMutation) SetInternalMessageID(i int) {
	m.message = &i
}

// InternalMessageID returns the value of the "internal_message_id" field in the mutation.
func (m *UserInternalMessageMutation) InternalMessageID() (r int, exists bool) {
	v := m.message
	if v == nil {
		return
	}
	return *v, true
}

// OldInternalMessageID returns the old "internal_message_id" field's value of the UserInternalMessage entity.
// If the UserInternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserInternalMessageMutation) OldInternalMessageID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldInternalMessageID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldInternalMessageID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldInternalMessageID: %w", err)
	}
	return oldValue.InternalMessageID, nil
}

// ResetInternalMessageID resets all changes to the "internal_message_id" field.
func (m *UserInternalMessageMutation) ResetInternalMessageID() {
	m.message = nil
}

// SetUserID sets the "user_id" field.
func (m *UserInternalMessageMutation) SetUserID(i int) {
	m.user = &i
}

// UserID returns the value of the "user_id" field in the mutation.
func (m *UserInternalMessageMutation) UserID() (r int, exists bool) {
	v := m.user
	if v == nil {
		return
	}
	return *v, true
}

// OldUserID returns the old "user_id" field's value of the UserInternalMessage entity.
// If the UserInternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserInternalMessageMutation) OldUserID(ctx context.Context) (v int, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldUserID is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldUserID requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldUserID: %w", err)
	}
	return oldValue.UserID, nil
}

// ResetUserID resets all changes to the "user_id" field.
func (m *UserInternalMessageMutation) ResetUserID() {
	m.user = nil
}

// SetStatus sets the "status" field.
func (m *UserInternalMessageMutation) SetStatus(i int8) {
	m.status = &i
	m.addstatus = nil
}

// Status returns the value of the "status" field in the mutation.
func (m *UserInternalMessageMutation) Status() (r int8, exists bool) {
	v := m.status
	if v == nil {
		return
	}
	return *v, true
}

// OldStatus returns the old "status" field's value of the UserInternalMessage entity.
// If the UserInternalMessage object wasn't provided to the builder, the object is fetched from the database.
// An error is returned if the mutation operation is not UpdateOne, or the database query fails.
func (m *UserInternalMessageMutation) OldStatus(ctx context.Context) (v int8, err error) {
	if !m.op.Is(OpUpdateOne) {
		return v, errors.New("OldStatus is only allowed on UpdateOne operations")
	}
	if m.id == nil || m.oldValue == nil {
		return v, errors.New("OldStatus requires an ID field in the mutation")
	}
	oldValue, err := m.oldValue(ctx)
	if err != nil {
		return v, fmt.Errorf("querying old value for OldStatus: %w", err)
	}
	return oldValue.Status, nil
}

// AddStatus adds i to the "status" field.
func (m *UserInternalMessageMutation) AddStatus(i int8) {
	if m.addstatus != nil {
		*m.addstatus += i
	} else {
		m.addstatus = &i
	}
}

// AddedStatus returns the value that was added to the "status" field in this mutation.
func (m *UserInternalMessageMutation) AddedStatus() (r int8, exists bool) {
	v := m.addstatus
	if v == nil {
		return
	}
	return *v, true
}

// ResetStatus resets all changes to the "status" field.
func (m *UserInternalMessageMutation) ResetStatus() {
	m.status = nil
	m.addstatus = nil
}

// SetMessageID sets the "message" edge to the InternalMessage entity by id.
func (m *UserInternalMessageMutation) SetMessageID(id int) {
	m.message = &id
}

// ClearMessage clears the "message" edge to the InternalMessage entity.
func (m *UserInternalMessageMutation) ClearMessage() {
	m.clearedmessage = true
	m.clearedFields[userinternalmessage.FieldInternalMessageID] = struct{}{}
}

// MessageCleared reports if the "message" edge to the InternalMessage entity was cleared.
func (m *UserInternalMessageMutation) MessageCleared() bool {
	return m.clearedmessage
}

// MessageID returns the "message" edge ID in the mutation.
func (m *UserInternalMessageMutation) MessageID() (id int, exists bool) {
	if m.message != nil {
		return *m.message, true
	}
	return
}

// MessageIDs returns the "message" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// MessageID instead. It exists only for internal usage by the builders.
func (m *UserInternalMessageMutation) MessageIDs() (ids []int) {
	if id := m.message; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetMessage resets all changes to the "message" edge.
func (m *UserInternalMessageMutation) ResetMessage() {
	m.message = nil
	m.clearedmessage = false
}

// ClearUser clears the "user" edge to the User entity.
func (m *UserInternalMessageMutation) ClearUser() {
	m.cleareduser = true
	m.clearedFields[userinternalmessage.FieldUserID] = struct{}{}
}

// UserCleared reports if the "user" edge to the User entity was cleared.
func (m *UserInternalMessageMutation) UserCleared() bool {
	return m.cleareduser
}

// UserIDs returns the "user" edge IDs in the mutation.
// Note that IDs always returns len(IDs) <= 1 for unique edges, and you should use
// UserID instead. It exists only for internal usage by the builders.
func (m *UserInternalMessageMutation) UserIDs() (ids []int) {
	if id := m.user; id != nil {
		ids = append(ids, *id)
	}
	return
}

// ResetUser resets all changes to the "user" edge.
func (m *UserInternalMessageMutation) ResetUser() {
	m.user = nil
	m.cleareduser = false
}

// Where appends a list predicates to the UserInternalMessageMutation builder.
func (m *UserInternalMessageMutation) Where(ps ...predicate.UserInternalMessage) {
	m.predicates = append(m.predicates, ps...)
}

// WhereP appends storage-level predicates to the UserInternalMessageMutation builder. Using this method,
// users can use type-assertion to append predicates that do not depend on any generated package.
func (m *UserInternalMessageMutation) WhereP(ps ...func(*sql.Selector)) {
	p := make([]predicate.UserInternalMessage, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	m.Where(p...)
}

// Op returns the operation name.
func (m *UserInternalMessageMutation) Op() Op {
	return m.op
}

// SetOp allows setting the mutation operation.
func (m *UserInternalMessageMutation) SetOp(op Op) {
	m.op = op
}

// Type returns the node type of this mutation (UserInternalMessage).
func (m *UserInternalMessageMutation) Type() string {
	return m.typ
}

// Fields returns all fields that were changed during this mutation. Note that in
// order to get all numeric fields that were incremented/decremented, call
// AddedFields().
func (m *UserInternalMessageMutation) Fields() []string {
	fields := make([]string, 0, 5)
	if m.created_at != nil {
		fields = append(fields, userinternalmessage.FieldCreatedAt)
	}
	if m.updated_at != nil {
		fields = append(fields, userinternalmessage.FieldUpdatedAt)
	}
	if m.message != nil {
		fields = append(fields, userinternalmessage.FieldInternalMessageID)
	}
	if m.user != nil {
		fields = append(fields, userinternalmessage.FieldUserID)
	}
	if m.status != nil {
		fields = append(fields, userinternalmessage.FieldStatus)
	}
	return fields
}

// Field returns the value of a field with the given name. The second boolean
// return value indicates that this field was not set, or was not defined in the
// schema.
func (m *UserInternalMessageMutation) Field(name string) (ent.Value, bool) {
	switch name {
	case userinternalmessage.FieldCreatedAt:
		return m.CreatedAt()
	case userinternalmessage.FieldUpdatedAt:
		return m.UpdatedAt()
	case userinternalmessage.FieldInternalMessageID:
		return m.InternalMessageID()
	case userinternalmessage.FieldUserID:
		return m.UserID()
	case userinternalmessage.FieldStatus:
		return m.Status()
	}
	return nil, false
}

// OldField returns the old value of the field from the database. An error is
// returned if the mutation operation is not UpdateOne, or the query to the
// database failed.
func (m *UserInternalMessageMutation) OldField(ctx context.Context, name string) (ent.Value, error) {
	switch name {
	case userinternalmessage.FieldCreatedAt:
		return m.OldCreatedAt(ctx)
	case userinternalmessage.FieldUpdatedAt:
		return m.OldUpdatedAt(ctx)
	case userinternalmessage.FieldInternalMessageID:
		return m.OldInternalMessageID(ctx)
	case userinternalmessage.FieldUserID:
		return m.OldUserID(ctx)
	case userinternalmessage.FieldStatus:
		return m.OldStatus(ctx)
	}
	return nil, fmt.Errorf("unknown UserInternalMessage field %s", name)
}

// SetField sets the value of a field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserInternalMessageMutation) SetField(name string, value ent.Value) error {
	switch name {
	case userinternalmessage.FieldCreatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetCreatedAt(v)
		return nil
	case userinternalmessage.FieldUpdatedAt:
		v, ok := value.(time.Time)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUpdatedAt(v)
		return nil
	case userinternalmessage.FieldInternalMessageID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetInternalMessageID(v)
		return nil
	case userinternalmessage.FieldUserID:
		v, ok := value.(int)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetUserID(v)
		return nil
	case userinternalmessage.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.SetStatus(v)
		return nil
	}
	return fmt.Errorf("unknown UserInternalMessage field %s", name)
}

// AddedFields returns all numeric fields that were incremented/decremented during
// this mutation.
func (m *UserInternalMessageMutation) AddedFields() []string {
	var fields []string
	if m.addstatus != nil {
		fields = append(fields, userinternalmessage.FieldStatus)
	}
	return fields
}

// AddedField returns the numeric value that was incremented/decremented on a field
// with the given name. The second boolean return value indicates that this field
// was not set, or was not defined in the schema.
func (m *UserInternalMessageMutation) AddedField(name string) (ent.Value, bool) {
	switch name {
	case userinternalmessage.FieldStatus:
		return m.AddedStatus()
	}
	return nil, false
}

// AddField adds the value to the field with the given name. It returns an error if
// the field is not defined in the schema, or if the type mismatched the field
// type.
func (m *UserInternalMessageMutation) AddField(name string, value ent.Value) error {
	switch name {
	case userinternalmessage.FieldStatus:
		v, ok := value.(int8)
		if !ok {
			return fmt.Errorf("unexpected type %T for field %s", value, name)
		}
		m.AddStatus(v)
		return nil
	}
	return fmt.Errorf("unknown UserInternalMessage numeric field %s", name)
}

// ClearedFields returns all nullable fields that were cleared during this
// mutation.
func (m *UserInternalMessageMutation) ClearedFields() []string {
	return nil
}

// FieldCleared returns a boolean indicating if a field with the given name was
// cleared in this mutation.
func (m *UserInternalMessageMutation) FieldCleared(name string) bool {
	_, ok := m.clearedFields[name]
	return ok
}

// ClearField clears the value of the field with the given name. It returns an
// error if the field is not defined in the schema.
func (m *UserInternalMessageMutation) ClearField(name string) error {
	return fmt.Errorf("unknown UserInternalMessage nullable field %s", name)
}

// ResetField resets all changes in the mutation for the field with the given name.
// It returns an error if the field is not defined in the schema.
func (m *UserInternalMessageMutation) ResetField(name string) error {
	switch name {
	case userinternalmessage.FieldCreatedAt:
		m.ResetCreatedAt()
		return nil
	case userinternalmessage.FieldUpdatedAt:
		m.ResetUpdatedAt()
		return nil
	case userinternalmessage.FieldInternalMessageID:
		m.ResetInternalMessageID()
		return nil
	case userinternalmessage.FieldUserID:
		m.ResetUserID()
		return nil
	case userinternalmessage.FieldStatus:
		m.ResetStatus()
		return nil
	}
	return fmt.Errorf("unknown UserInternalMessage field %s", name)
}

// AddedEdges returns all edge names that were set/added in this mutation.
func (m *UserInternalMessageMutation) AddedEdges() []string {
	edges := make([]string, 0, 2)
	if m.message != nil {
		edges = append(edges, userinternalmessage.EdgeMessage)
	}
	if m.user != nil {
		edges = append(edges, userinternalmessage.EdgeUser)
	}
	return edges
}

// AddedIDs returns all IDs (to other nodes) that were added for the given edge
// name in this mutation.
func (m *UserInternalMessageMutation) AddedIDs(name string) []ent.Value {
	switch name {
	case userinternalmessage.EdgeMessage:
		if id := m.message; id != nil {
			return []ent.Value{*id}
		}
	case userinternalmessage.EdgeUser:
		if id := m.user; id != nil {
			return []ent.Value{*id}
		}
	}
	return nil
}

// RemovedEdges returns all edge names that were removed in this mutation.
func (m *UserInternalMessageMutation) RemovedEdges() []string {
	edges := make([]string, 0, 2)
	return edges
}

// RemovedIDs returns all IDs (to other nodes) that were removed for the edge with
// the given name in this mutation.
func (m *UserInternalMessageMutation) RemovedIDs(name string) []ent.Value {
	return nil
}

// ClearedEdges returns all edge names that were cleared in this mutation.
func (m *UserInternalMessageMutation) ClearedEdges() []string {
	edges := make([]string, 0, 2)
	if m.clearedmessage {
		edges = append(edges, userinternalmessage.EdgeMessage)
	}
	if m.cleareduser {
		edges = append(edges, userinternalmessage.EdgeUser)
	}
	return edges
}

// EdgeCleared returns a boolean which indicates if the edge with the given name
// was cleared in this mutation.
func (m *UserInternalMessageMutation) EdgeCleared(name string) bool {
	switch name {
	case userinternalmessage.EdgeMessage:
		return m.clearedmessage
	case userinternalmessage.EdgeUser:
		return m.cleareduser
	}
	return false
}

// ClearEdge clears the value of the edge with the given name. It returns an error
// if that edge is not defined in the schema.
func (m *UserInternalMessageMutation) ClearEdge(name string) error {
	switch name {
	case userinternalmessage.EdgeMessage:
		m.ClearMessage()
		return nil
	case userinternalmessage.EdgeUser:
		m.ClearUser()
		return nil
	}
	return fmt.Errorf("unknown UserInternalMessage unique edge %s", name)
}

// ResetEdge resets all changes to the edge with the given name in this mutation.
// It returns an error if the edge is not defined in the schema.
func (m *UserInternalMessageMutation) ResetEdge(name string) error {
	switch name {
	case userinternalmessage.EdgeMessage:
		m.ResetMessage()
		return nil
	case userinternalmessage.EdgeUser:
		m.ResetUser()
		return nil
	}
	return fmt.Errorf("unknown UserInternalMessage edge %s", name)
}
