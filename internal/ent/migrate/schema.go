// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// AdminUsersColumns holds the columns for the "admin_users" table.
	AdminUsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "username", Type: field.TypeString, Unique: true, Comment: "账号", SchemaType: map[string]string{"mysql": "varchar(11)"}},
		{Name: "password", Type: field.TypeString, Size: 100, Comment: "密码"},
	}
	// AdminUsersTable holds the schema information for the "admin_users" table.
	AdminUsersTable = &schema.Table{
		Name:       "admin_users",
		Comment:    "管理后台用户表",
		Columns:    AdminUsersColumns,
		PrimaryKey: []*schema.Column{AdminUsersColumns[0]},
	}
	// AiAgentsColumns holds the columns for the "ai_agents" table.
	AiAgentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "名称", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "icon", Type: field.TypeString, Nullable: true, Comment: "图标", SchemaType: map[string]string{"mysql": "longtext"}},
		{Name: "description", Type: field.TypeString, Nullable: true, Comment: "描述"},
		{Name: "target", Type: field.TypeString, Comment: "target"},
		{Name: "guide", Type: field.TypeString, Nullable: true, Comment: "引导"},
		{Name: "secret", Type: field.TypeString, Comment: "secret", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "method", Type: field.TypeString, Comment: "http method", SchemaType: map[string]string{"mysql": "varchar(20)"}},
		{Name: "inputs", Type: field.TypeJSON, Nullable: true, Comment: "inputs参数"},
		{Name: "status", Type: field.TypeInt8, Comment: "状态:1默认；2下线", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
	}
	// AiAgentsTable holds the schema information for the "ai_agents" table.
	AiAgentsTable = &schema.Table{
		Name:       "ai_agents",
		Comment:    "AI智能体表",
		Columns:    AiAgentsColumns,
		PrimaryKey: []*schema.Column{AiAgentsColumns[0]},
	}
	// BatchesColumns holds the columns for the "batches" table.
	BatchesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "name", Type: field.TypeString, Unique: true, Comment: "批次名称", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "num", Type: field.TypeInt64, Comment: "测试码生成数量"},
		{Name: "expired_at", Type: field.TypeTime, Nullable: true, Comment: "过期时间", SchemaType: map[string]string{"mysql": "DATETIME"}},
	}
	// BatchesTable holds the schema information for the "batches" table.
	BatchesTable = &schema.Table{
		Name:       "batches",
		Comment:    "批次表",
		Columns:    BatchesColumns,
		PrimaryKey: []*schema.Column{BatchesColumns[0]},
	}
	// ConversationsColumns holds the columns for the "conversations" table.
	ConversationsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeInt, Comment: "用户id"},
		{Name: "ai_agent_conversation_id", Type: field.TypeString, Comment: "智能体会话id", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "name", Type: field.TypeString, Comment: "会话记录名称", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "ai_agent_id", Type: field.TypeInt, Comment: "智能体id"},
	}
	// ConversationsTable holds the schema information for the "conversations" table.
	ConversationsTable = &schema.Table{
		Name:       "conversations",
		Comment:    "智能体会话表",
		Columns:    ConversationsColumns,
		PrimaryKey: []*schema.Column{ConversationsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "conversations_ai_agents_conversations",
				Columns:    []*schema.Column{ConversationsColumns[6]},
				RefColumns: []*schema.Column{AiAgentsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "conversation_ai_agent_id",
				Unique:  false,
				Columns: []*schema.Column{ConversationsColumns[6]},
			},
			{
				Name:    "conversation_user_id",
				Unique:  false,
				Columns: []*schema.Column{ConversationsColumns[3]},
			},
			{
				Name:    "conversation_ai_agent_conversation_id",
				Unique:  false,
				Columns: []*schema.Column{ConversationsColumns[4]},
			},
		},
	}
	// ConversationContentsColumns holds the columns for the "conversation_contents" table.
	ConversationContentsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "content", Type: field.TypeString, Comment: "会话内容", SchemaType: map[string]string{"mysql": "text"}},
		{Name: "type", Type: field.TypeInt8, Comment: "类型:1 用户; 2 agent", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
		{Name: "conversation_id", Type: field.TypeInt, Comment: "conversations表id"},
	}
	// ConversationContentsTable holds the schema information for the "conversation_contents" table.
	ConversationContentsTable = &schema.Table{
		Name:       "conversation_contents",
		Comment:    "智能体会话详情表",
		Columns:    ConversationContentsColumns,
		PrimaryKey: []*schema.Column{ConversationContentsColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "conversation_contents_conversations_contents",
				Columns:    []*schema.Column{ConversationContentsColumns[5]},
				RefColumns: []*schema.Column{ConversationsColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "conversationcontent_conversation_id",
				Unique:  false,
				Columns: []*schema.Column{ConversationContentsColumns[5]},
			},
		},
	}
	// FrequentAskQuestionsColumns holds the columns for the "frequent_ask_questions" table.
	FrequentAskQuestionsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "content", Type: field.TypeString, SchemaType: map[string]string{"mysql": "longtext"}},
	}
	// FrequentAskQuestionsTable holds the schema information for the "frequent_ask_questions" table.
	FrequentAskQuestionsTable = &schema.Table{
		Name:       "frequent_ask_questions",
		Columns:    FrequentAskQuestionsColumns,
		PrimaryKey: []*schema.Column{FrequentAskQuestionsColumns[0]},
	}
	// InternalMessagesColumns holds the columns for the "internal_messages" table.
	InternalMessagesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "title", Type: field.TypeString, Unique: true, Nullable: true, Comment: "标题", SchemaType: map[string]string{"mysql": "varchar(32)"}},
		{Name: "content", Type: field.TypeString, Comment: "内容", SchemaType: map[string]string{"mysql": "text"}},
	}
	// InternalMessagesTable holds the schema information for the "internal_messages" table.
	InternalMessagesTable = &schema.Table{
		Name:       "internal_messages",
		Comment:    "站内信",
		Columns:    InternalMessagesColumns,
		PrimaryKey: []*schema.Column{InternalMessagesColumns[0]},
	}
	// ProjectsColumns holds the columns for the "projects" table.
	ProjectsColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeInt64, Comment: "用户id", SchemaType: map[string]string{"mysql": "int(10)unsigned"}},
		{Name: "name", Type: field.TypeString, Comment: "名称", SchemaType: map[string]string{"mysql": "varchar(100)"}},
	}
	// ProjectsTable holds the schema information for the "projects" table.
	ProjectsTable = &schema.Table{
		Name:       "projects",
		Comment:    "项目表",
		Columns:    ProjectsColumns,
		PrimaryKey: []*schema.Column{ProjectsColumns[0]},
	}
	// ScreenplaysColumns holds the columns for the "screenplays" table.
	ScreenplaysColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "user_id", Type: field.TypeInt64, Comment: "用户id", SchemaType: map[string]string{"mysql": "int(10)unsigned"}},
		{Name: "project_id", Type: field.TypeInt64, Comment: "用户id", SchemaType: map[string]string{"mysql": "int(10)unsigned"}},
		{Name: "title", Type: field.TypeString, Comment: "名称", SchemaType: map[string]string{"mysql": "varchar(100)"}},
		{Name: "content", Type: field.TypeString, Size: 2147483647, Comment: "内容", SchemaType: map[string]string{"mysql": "longtext"}},
		{Name: "type", Type: field.TypeInt8, Comment: "类型 1 大纲 2 剧本 3 最终剧本", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
		{Name: "status", Type: field.TypeInt8, Comment: "状态:1默认；2发布 3 废弃", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
	}
	// ScreenplaysTable holds the schema information for the "screenplays" table.
	ScreenplaysTable = &schema.Table{
		Name:       "screenplays",
		Comment:    "剧本表",
		Columns:    ScreenplaysColumns,
		PrimaryKey: []*schema.Column{ScreenplaysColumns[0]},
	}
	// TestCodesColumns holds the columns for the "test_codes" table.
	TestCodesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "测试码", SchemaType: map[string]string{"mysql": "varchar(10)"}},
		{Name: "device", Type: field.TypeString, Unique: true, Nullable: true, Comment: "设备id", SchemaType: map[string]string{"mysql": "varchar(32)"}},
		{Name: "status", Type: field.TypeInt8, Comment: "状态:1默认；2已领取 3 已使用", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
		{Name: "expired_at", Type: field.TypeTime, Nullable: true, Comment: "过期时间", SchemaType: map[string]string{"mysql": "DATETIME"}},
		{Name: "batch_id", Type: field.TypeInt, Comment: "批次id"},
	}
	// TestCodesTable holds the schema information for the "test_codes" table.
	TestCodesTable = &schema.Table{
		Name:       "test_codes",
		Comment:    "测试码表",
		Columns:    TestCodesColumns,
		PrimaryKey: []*schema.Column{TestCodesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "test_codes_batches_codes",
				Columns:    []*schema.Column{TestCodesColumns[7]},
				RefColumns: []*schema.Column{BatchesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// UsersColumns holds the columns for the "users" table.
	UsersColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "username", Type: field.TypeString, Unique: true, Comment: "账号", SchemaType: map[string]string{"mysql": "varchar(11)"}},
		{Name: "password", Type: field.TypeString, Size: 100, Comment: "密码"},
		{Name: "email", Type: field.TypeString, Nullable: true, Size: 50, Comment: "邮箱"},
		{Name: "status", Type: field.TypeInt8, Comment: "状态:1默认；2 禁用", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
		{Name: "batch_id", Type: field.TypeInt},
		{Name: "test_code_id", Type: field.TypeInt, Unique: true},
	}
	// UsersTable holds the schema information for the "users" table.
	UsersTable = &schema.Table{
		Name:       "users",
		Comment:    "用户表",
		Columns:    UsersColumns,
		PrimaryKey: []*schema.Column{UsersColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "users_batches_users",
				Columns:    []*schema.Column{UsersColumns[7]},
				RefColumns: []*schema.Column{BatchesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "users_test_codes_user",
				Columns:    []*schema.Column{UsersColumns[8]},
				RefColumns: []*schema.Column{TestCodesColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// UserInternalMessagesColumns holds the columns for the "user_internal_messages" table.
	UserInternalMessagesColumns = []*schema.Column{
		{Name: "id", Type: field.TypeInt, Increment: true},
		{Name: "created_at", Type: field.TypeTime},
		{Name: "updated_at", Type: field.TypeTime},
		{Name: "status", Type: field.TypeInt8, Comment: "状态:1未读；2 已读", Default: 1, SchemaType: map[string]string{"mysql": "tinyint(4)"}},
		{Name: "internal_message_id", Type: field.TypeInt, Comment: "站内信id"},
		{Name: "user_id", Type: field.TypeInt, Comment: "用户id"},
	}
	// UserInternalMessagesTable holds the schema information for the "user_internal_messages" table.
	UserInternalMessagesTable = &schema.Table{
		Name:       "user_internal_messages",
		Comment:    "站内信",
		Columns:    UserInternalMessagesColumns,
		PrimaryKey: []*schema.Column{UserInternalMessagesColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_internal_messages_internal_messages_users",
				Columns:    []*schema.Column{UserInternalMessagesColumns[4]},
				RefColumns: []*schema.Column{InternalMessagesColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "user_internal_messages_users_messages",
				Columns:    []*schema.Column{UserInternalMessagesColumns[5]},
				RefColumns: []*schema.Column{UsersColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		AdminUsersTable,
		AiAgentsTable,
		BatchesTable,
		ConversationsTable,
		ConversationContentsTable,
		FrequentAskQuestionsTable,
		InternalMessagesTable,
		ProjectsTable,
		ScreenplaysTable,
		TestCodesTable,
		UsersTable,
		UserInternalMessagesTable,
	}
)

func init() {
	ConversationsTable.ForeignKeys[0].RefTable = AiAgentsTable
	ConversationContentsTable.ForeignKeys[0].RefTable = ConversationsTable
	TestCodesTable.ForeignKeys[0].RefTable = BatchesTable
	UsersTable.ForeignKeys[0].RefTable = BatchesTable
	UsersTable.ForeignKeys[1].RefTable = TestCodesTable
	UserInternalMessagesTable.ForeignKeys[0].RefTable = InternalMessagesTable
	UserInternalMessagesTable.ForeignKeys[1].RefTable = UsersTable
}
