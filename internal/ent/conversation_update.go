// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationUpdate is the builder for updating Conversation entities.
type ConversationUpdate struct {
	config
	hooks    []Hook
	mutation *ConversationMutation
}

// Where appends a list predicates to the ConversationUpdate builder.
func (cu *ConversationUpdate) Where(ps ...predicate.Conversation) *ConversationUpdate {
	cu.mutation.Where(ps...)
	return cu
}

// SetUpdatedAt sets the "updated_at" field.
func (cu *ConversationUpdate) SetUpdatedAt(t time.Time) *ConversationUpdate {
	cu.mutation.SetUpdatedAt(t)
	return cu
}

// SetAiAgentID sets the "ai_agent_id" field.
func (cu *ConversationUpdate) SetAiAgentID(i int) *ConversationUpdate {
	cu.mutation.SetAiAgentID(i)
	return cu
}

// SetNillableAiAgentID sets the "ai_agent_id" field if the given value is not nil.
func (cu *ConversationUpdate) SetNillableAiAgentID(i *int) *ConversationUpdate {
	if i != nil {
		cu.SetAiAgentID(*i)
	}
	return cu
}

// SetUserID sets the "user_id" field.
func (cu *ConversationUpdate) SetUserID(i int) *ConversationUpdate {
	cu.mutation.ResetUserID()
	cu.mutation.SetUserID(i)
	return cu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cu *ConversationUpdate) SetNillableUserID(i *int) *ConversationUpdate {
	if i != nil {
		cu.SetUserID(*i)
	}
	return cu
}

// AddUserID adds i to the "user_id" field.
func (cu *ConversationUpdate) AddUserID(i int) *ConversationUpdate {
	cu.mutation.AddUserID(i)
	return cu
}

// SetAiAgentConversationID sets the "ai_agent_conversation_id" field.
func (cu *ConversationUpdate) SetAiAgentConversationID(s string) *ConversationUpdate {
	cu.mutation.SetAiAgentConversationID(s)
	return cu
}

// SetNillableAiAgentConversationID sets the "ai_agent_conversation_id" field if the given value is not nil.
func (cu *ConversationUpdate) SetNillableAiAgentConversationID(s *string) *ConversationUpdate {
	if s != nil {
		cu.SetAiAgentConversationID(*s)
	}
	return cu
}

// SetName sets the "name" field.
func (cu *ConversationUpdate) SetName(s string) *ConversationUpdate {
	cu.mutation.SetName(s)
	return cu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cu *ConversationUpdate) SetNillableName(s *string) *ConversationUpdate {
	if s != nil {
		cu.SetName(*s)
	}
	return cu
}

// AddContentIDs adds the "contents" edge to the ConversationContent entity by IDs.
func (cu *ConversationUpdate) AddContentIDs(ids ...int) *ConversationUpdate {
	cu.mutation.AddContentIDs(ids...)
	return cu
}

// AddContents adds the "contents" edges to the ConversationContent entity.
func (cu *ConversationUpdate) AddContents(c ...*ConversationContent) *ConversationUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cu.AddContentIDs(ids...)
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (cu *ConversationUpdate) SetAiAgent(a *AiAgent) *ConversationUpdate {
	return cu.SetAiAgentID(a.ID)
}

// Mutation returns the ConversationMutation object of the builder.
func (cu *ConversationUpdate) Mutation() *ConversationMutation {
	return cu.mutation
}

// ClearContents clears all "contents" edges to the ConversationContent entity.
func (cu *ConversationUpdate) ClearContents() *ConversationUpdate {
	cu.mutation.ClearContents()
	return cu
}

// RemoveContentIDs removes the "contents" edge to ConversationContent entities by IDs.
func (cu *ConversationUpdate) RemoveContentIDs(ids ...int) *ConversationUpdate {
	cu.mutation.RemoveContentIDs(ids...)
	return cu
}

// RemoveContents removes "contents" edges to ConversationContent entities.
func (cu *ConversationUpdate) RemoveContents(c ...*ConversationContent) *ConversationUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cu.RemoveContentIDs(ids...)
}

// ClearAiAgent clears the "ai_agent" edge to the AiAgent entity.
func (cu *ConversationUpdate) ClearAiAgent() *ConversationUpdate {
	cu.mutation.ClearAiAgent()
	return cu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (cu *ConversationUpdate) Save(ctx context.Context) (int, error) {
	cu.defaults()
	return withHooks(ctx, cu.sqlSave, cu.mutation, cu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cu *ConversationUpdate) SaveX(ctx context.Context) int {
	affected, err := cu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (cu *ConversationUpdate) Exec(ctx context.Context) error {
	_, err := cu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cu *ConversationUpdate) ExecX(ctx context.Context) {
	if err := cu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cu *ConversationUpdate) defaults() {
	if _, ok := cu.mutation.UpdatedAt(); !ok {
		v := conversation.UpdateDefaultUpdatedAt()
		cu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cu *ConversationUpdate) check() error {
	if cu.mutation.AiAgentCleared() && len(cu.mutation.AiAgentIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Conversation.ai_agent"`)
	}
	return nil
}

func (cu *ConversationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := cu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(conversation.Table, conversation.Columns, sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt))
	if ps := cu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cu.mutation.UpdatedAt(); ok {
		_spec.SetField(conversation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cu.mutation.UserID(); ok {
		_spec.SetField(conversation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := cu.mutation.AddedUserID(); ok {
		_spec.AddField(conversation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := cu.mutation.AiAgentConversationID(); ok {
		_spec.SetField(conversation.FieldAiAgentConversationID, field.TypeString, value)
	}
	if value, ok := cu.mutation.Name(); ok {
		_spec.SetField(conversation.FieldName, field.TypeString, value)
	}
	if cu.mutation.ContentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cu.mutation.RemovedContentsIDs(); len(nodes) > 0 && !cu.mutation.ContentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cu.mutation.ContentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cu.mutation.AiAgentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversation.AiAgentTable,
			Columns: []string{conversation.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cu.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversation.AiAgentTable,
			Columns: []string{conversation.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, cu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{conversation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	cu.mutation.done = true
	return n, nil
}

// ConversationUpdateOne is the builder for updating a single Conversation entity.
type ConversationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ConversationMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (cuo *ConversationUpdateOne) SetUpdatedAt(t time.Time) *ConversationUpdateOne {
	cuo.mutation.SetUpdatedAt(t)
	return cuo
}

// SetAiAgentID sets the "ai_agent_id" field.
func (cuo *ConversationUpdateOne) SetAiAgentID(i int) *ConversationUpdateOne {
	cuo.mutation.SetAiAgentID(i)
	return cuo
}

// SetNillableAiAgentID sets the "ai_agent_id" field if the given value is not nil.
func (cuo *ConversationUpdateOne) SetNillableAiAgentID(i *int) *ConversationUpdateOne {
	if i != nil {
		cuo.SetAiAgentID(*i)
	}
	return cuo
}

// SetUserID sets the "user_id" field.
func (cuo *ConversationUpdateOne) SetUserID(i int) *ConversationUpdateOne {
	cuo.mutation.ResetUserID()
	cuo.mutation.SetUserID(i)
	return cuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (cuo *ConversationUpdateOne) SetNillableUserID(i *int) *ConversationUpdateOne {
	if i != nil {
		cuo.SetUserID(*i)
	}
	return cuo
}

// AddUserID adds i to the "user_id" field.
func (cuo *ConversationUpdateOne) AddUserID(i int) *ConversationUpdateOne {
	cuo.mutation.AddUserID(i)
	return cuo
}

// SetAiAgentConversationID sets the "ai_agent_conversation_id" field.
func (cuo *ConversationUpdateOne) SetAiAgentConversationID(s string) *ConversationUpdateOne {
	cuo.mutation.SetAiAgentConversationID(s)
	return cuo
}

// SetNillableAiAgentConversationID sets the "ai_agent_conversation_id" field if the given value is not nil.
func (cuo *ConversationUpdateOne) SetNillableAiAgentConversationID(s *string) *ConversationUpdateOne {
	if s != nil {
		cuo.SetAiAgentConversationID(*s)
	}
	return cuo
}

// SetName sets the "name" field.
func (cuo *ConversationUpdateOne) SetName(s string) *ConversationUpdateOne {
	cuo.mutation.SetName(s)
	return cuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (cuo *ConversationUpdateOne) SetNillableName(s *string) *ConversationUpdateOne {
	if s != nil {
		cuo.SetName(*s)
	}
	return cuo
}

// AddContentIDs adds the "contents" edge to the ConversationContent entity by IDs.
func (cuo *ConversationUpdateOne) AddContentIDs(ids ...int) *ConversationUpdateOne {
	cuo.mutation.AddContentIDs(ids...)
	return cuo
}

// AddContents adds the "contents" edges to the ConversationContent entity.
func (cuo *ConversationUpdateOne) AddContents(c ...*ConversationContent) *ConversationUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cuo.AddContentIDs(ids...)
}

// SetAiAgent sets the "ai_agent" edge to the AiAgent entity.
func (cuo *ConversationUpdateOne) SetAiAgent(a *AiAgent) *ConversationUpdateOne {
	return cuo.SetAiAgentID(a.ID)
}

// Mutation returns the ConversationMutation object of the builder.
func (cuo *ConversationUpdateOne) Mutation() *ConversationMutation {
	return cuo.mutation
}

// ClearContents clears all "contents" edges to the ConversationContent entity.
func (cuo *ConversationUpdateOne) ClearContents() *ConversationUpdateOne {
	cuo.mutation.ClearContents()
	return cuo
}

// RemoveContentIDs removes the "contents" edge to ConversationContent entities by IDs.
func (cuo *ConversationUpdateOne) RemoveContentIDs(ids ...int) *ConversationUpdateOne {
	cuo.mutation.RemoveContentIDs(ids...)
	return cuo
}

// RemoveContents removes "contents" edges to ConversationContent entities.
func (cuo *ConversationUpdateOne) RemoveContents(c ...*ConversationContent) *ConversationUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return cuo.RemoveContentIDs(ids...)
}

// ClearAiAgent clears the "ai_agent" edge to the AiAgent entity.
func (cuo *ConversationUpdateOne) ClearAiAgent() *ConversationUpdateOne {
	cuo.mutation.ClearAiAgent()
	return cuo
}

// Where appends a list predicates to the ConversationUpdate builder.
func (cuo *ConversationUpdateOne) Where(ps ...predicate.Conversation) *ConversationUpdateOne {
	cuo.mutation.Where(ps...)
	return cuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (cuo *ConversationUpdateOne) Select(field string, fields ...string) *ConversationUpdateOne {
	cuo.fields = append([]string{field}, fields...)
	return cuo
}

// Save executes the query and returns the updated Conversation entity.
func (cuo *ConversationUpdateOne) Save(ctx context.Context) (*Conversation, error) {
	cuo.defaults()
	return withHooks(ctx, cuo.sqlSave, cuo.mutation, cuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (cuo *ConversationUpdateOne) SaveX(ctx context.Context) *Conversation {
	node, err := cuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (cuo *ConversationUpdateOne) Exec(ctx context.Context) error {
	_, err := cuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (cuo *ConversationUpdateOne) ExecX(ctx context.Context) {
	if err := cuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (cuo *ConversationUpdateOne) defaults() {
	if _, ok := cuo.mutation.UpdatedAt(); !ok {
		v := conversation.UpdateDefaultUpdatedAt()
		cuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (cuo *ConversationUpdateOne) check() error {
	if cuo.mutation.AiAgentCleared() && len(cuo.mutation.AiAgentIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "Conversation.ai_agent"`)
	}
	return nil
}

func (cuo *ConversationUpdateOne) sqlSave(ctx context.Context) (_node *Conversation, err error) {
	if err := cuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(conversation.Table, conversation.Columns, sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt))
	id, ok := cuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Conversation.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := cuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, conversation.FieldID)
		for _, f := range fields {
			if !conversation.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != conversation.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := cuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := cuo.mutation.UpdatedAt(); ok {
		_spec.SetField(conversation.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := cuo.mutation.UserID(); ok {
		_spec.SetField(conversation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := cuo.mutation.AddedUserID(); ok {
		_spec.AddField(conversation.FieldUserID, field.TypeInt, value)
	}
	if value, ok := cuo.mutation.AiAgentConversationID(); ok {
		_spec.SetField(conversation.FieldAiAgentConversationID, field.TypeString, value)
	}
	if value, ok := cuo.mutation.Name(); ok {
		_spec.SetField(conversation.FieldName, field.TypeString, value)
	}
	if cuo.mutation.ContentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cuo.mutation.RemovedContentsIDs(); len(nodes) > 0 && !cuo.mutation.ContentsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cuo.mutation.ContentsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   conversation.ContentsTable,
			Columns: []string{conversation.ContentsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if cuo.mutation.AiAgentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversation.AiAgentTable,
			Columns: []string{conversation.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := cuo.mutation.AiAgentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversation.AiAgentTable,
			Columns: []string{conversation.AiAgentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Conversation{config: cuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, cuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{conversation.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	cuo.mutation.done = true
	return _node, nil
}
