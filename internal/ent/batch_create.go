// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// BatchCreate is the builder for creating a Batch entity.
type BatchCreate struct {
	config
	mutation *BatchMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (bc *BatchCreate) SetCreatedAt(t time.Time) *BatchCreate {
	bc.mutation.SetCreatedAt(t)
	return bc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (bc *BatchCreate) SetNillableCreatedAt(t *time.Time) *BatchCreate {
	if t != nil {
		bc.SetCreatedAt(*t)
	}
	return bc
}

// SetUpdatedAt sets the "updated_at" field.
func (bc *BatchCreate) SetUpdatedAt(t time.Time) *BatchCreate {
	bc.mutation.SetUpdatedAt(t)
	return bc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (bc *BatchCreate) SetNillableUpdatedAt(t *time.Time) *BatchCreate {
	if t != nil {
		bc.SetUpdatedAt(*t)
	}
	return bc
}

// SetName sets the "name" field.
func (bc *BatchCreate) SetName(s string) *BatchCreate {
	bc.mutation.SetName(s)
	return bc
}

// SetNum sets the "num" field.
func (bc *BatchCreate) SetNum(i int64) *BatchCreate {
	bc.mutation.SetNum(i)
	return bc
}

// SetExpiredAt sets the "expired_at" field.
func (bc *BatchCreate) SetExpiredAt(t time.Time) *BatchCreate {
	bc.mutation.SetExpiredAt(t)
	return bc
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (bc *BatchCreate) SetNillableExpiredAt(t *time.Time) *BatchCreate {
	if t != nil {
		bc.SetExpiredAt(*t)
	}
	return bc
}

// AddCodeIDs adds the "codes" edge to the TestCode entity by IDs.
func (bc *BatchCreate) AddCodeIDs(ids ...int) *BatchCreate {
	bc.mutation.AddCodeIDs(ids...)
	return bc
}

// AddCodes adds the "codes" edges to the TestCode entity.
func (bc *BatchCreate) AddCodes(t ...*TestCode) *BatchCreate {
	ids := make([]int, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return bc.AddCodeIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (bc *BatchCreate) AddUserIDs(ids ...int) *BatchCreate {
	bc.mutation.AddUserIDs(ids...)
	return bc
}

// AddUsers adds the "users" edges to the User entity.
func (bc *BatchCreate) AddUsers(u ...*User) *BatchCreate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return bc.AddUserIDs(ids...)
}

// Mutation returns the BatchMutation object of the builder.
func (bc *BatchCreate) Mutation() *BatchMutation {
	return bc.mutation
}

// Save creates the Batch in the database.
func (bc *BatchCreate) Save(ctx context.Context) (*Batch, error) {
	bc.defaults()
	return withHooks(ctx, bc.sqlSave, bc.mutation, bc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (bc *BatchCreate) SaveX(ctx context.Context) *Batch {
	v, err := bc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bc *BatchCreate) Exec(ctx context.Context) error {
	_, err := bc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bc *BatchCreate) ExecX(ctx context.Context) {
	if err := bc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bc *BatchCreate) defaults() {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		v := batch.DefaultCreatedAt()
		bc.mutation.SetCreatedAt(v)
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		v := batch.DefaultUpdatedAt()
		bc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (bc *BatchCreate) check() error {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Batch.created_at"`)}
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Batch.updated_at"`)}
	}
	if _, ok := bc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Batch.name"`)}
	}
	if _, ok := bc.mutation.Num(); !ok {
		return &ValidationError{Name: "num", err: errors.New(`ent: missing required field "Batch.num"`)}
	}
	return nil
}

func (bc *BatchCreate) sqlSave(ctx context.Context) (*Batch, error) {
	if err := bc.check(); err != nil {
		return nil, err
	}
	_node, _spec := bc.createSpec()
	if err := sqlgraph.CreateNode(ctx, bc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	bc.mutation.id = &_node.ID
	bc.mutation.done = true
	return _node, nil
}

func (bc *BatchCreate) createSpec() (*Batch, *sqlgraph.CreateSpec) {
	var (
		_node = &Batch{config: bc.config}
		_spec = sqlgraph.NewCreateSpec(batch.Table, sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt))
	)
	if value, ok := bc.mutation.CreatedAt(); ok {
		_spec.SetField(batch.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := bc.mutation.UpdatedAt(); ok {
		_spec.SetField(batch.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := bc.mutation.Name(); ok {
		_spec.SetField(batch.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := bc.mutation.Num(); ok {
		_spec.SetField(batch.FieldNum, field.TypeInt64, value)
		_node.Num = value
	}
	if value, ok := bc.mutation.ExpiredAt(); ok {
		_spec.SetField(batch.FieldExpiredAt, field.TypeTime, value)
		_node.ExpiredAt = value
	}
	if nodes := bc.mutation.CodesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.CodesTable,
			Columns: []string{batch.CodesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := bc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   batch.UsersTable,
			Columns: []string{batch.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// BatchCreateBulk is the builder for creating many Batch entities in bulk.
type BatchCreateBulk struct {
	config
	err      error
	builders []*BatchCreate
}

// Save creates the Batch entities in the database.
func (bcb *BatchCreateBulk) Save(ctx context.Context) ([]*Batch, error) {
	if bcb.err != nil {
		return nil, bcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(bcb.builders))
	nodes := make([]*Batch, len(bcb.builders))
	mutators := make([]Mutator, len(bcb.builders))
	for i := range bcb.builders {
		func(i int, root context.Context) {
			builder := bcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*BatchMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, bcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, bcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, bcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (bcb *BatchCreateBulk) SaveX(ctx context.Context) []*Batch {
	v, err := bcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bcb *BatchCreateBulk) Exec(ctx context.Context) error {
	_, err := bcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bcb *BatchCreateBulk) ExecX(ctx context.Context) {
	if err := bcb.Exec(ctx); err != nil {
		panic(err)
	}
}
