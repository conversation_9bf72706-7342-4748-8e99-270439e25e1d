// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// InternalMessageCreate is the builder for creating a InternalMessage entity.
type InternalMessageCreate struct {
	config
	mutation *InternalMessageMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (imc *InternalMessageCreate) SetCreatedAt(t time.Time) *InternalMessageCreate {
	imc.mutation.SetCreatedAt(t)
	return imc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (imc *InternalMessageCreate) SetNillableCreatedAt(t *time.Time) *InternalMessageCreate {
	if t != nil {
		imc.SetCreatedAt(*t)
	}
	return imc
}

// SetUpdatedAt sets the "updated_at" field.
func (imc *InternalMessageCreate) SetUpdatedAt(t time.Time) *InternalMessageCreate {
	imc.mutation.SetUpdatedAt(t)
	return imc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (imc *InternalMessageCreate) SetNillableUpdatedAt(t *time.Time) *InternalMessageCreate {
	if t != nil {
		imc.SetUpdatedAt(*t)
	}
	return imc
}

// SetTitle sets the "title" field.
func (imc *InternalMessageCreate) SetTitle(s string) *InternalMessageCreate {
	imc.mutation.SetTitle(s)
	return imc
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (imc *InternalMessageCreate) SetNillableTitle(s *string) *InternalMessageCreate {
	if s != nil {
		imc.SetTitle(*s)
	}
	return imc
}

// SetContent sets the "content" field.
func (imc *InternalMessageCreate) SetContent(s string) *InternalMessageCreate {
	imc.mutation.SetContent(s)
	return imc
}

// AddUserIDs adds the "users" edge to the UserInternalMessage entity by IDs.
func (imc *InternalMessageCreate) AddUserIDs(ids ...int) *InternalMessageCreate {
	imc.mutation.AddUserIDs(ids...)
	return imc
}

// AddUsers adds the "users" edges to the UserInternalMessage entity.
func (imc *InternalMessageCreate) AddUsers(u ...*UserInternalMessage) *InternalMessageCreate {
	ids := make([]int, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return imc.AddUserIDs(ids...)
}

// Mutation returns the InternalMessageMutation object of the builder.
func (imc *InternalMessageCreate) Mutation() *InternalMessageMutation {
	return imc.mutation
}

// Save creates the InternalMessage in the database.
func (imc *InternalMessageCreate) Save(ctx context.Context) (*InternalMessage, error) {
	imc.defaults()
	return withHooks(ctx, imc.sqlSave, imc.mutation, imc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (imc *InternalMessageCreate) SaveX(ctx context.Context) *InternalMessage {
	v, err := imc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (imc *InternalMessageCreate) Exec(ctx context.Context) error {
	_, err := imc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (imc *InternalMessageCreate) ExecX(ctx context.Context) {
	if err := imc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (imc *InternalMessageCreate) defaults() {
	if _, ok := imc.mutation.CreatedAt(); !ok {
		v := internalmessage.DefaultCreatedAt()
		imc.mutation.SetCreatedAt(v)
	}
	if _, ok := imc.mutation.UpdatedAt(); !ok {
		v := internalmessage.DefaultUpdatedAt()
		imc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (imc *InternalMessageCreate) check() error {
	if _, ok := imc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "InternalMessage.created_at"`)}
	}
	if _, ok := imc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "InternalMessage.updated_at"`)}
	}
	if _, ok := imc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "InternalMessage.content"`)}
	}
	return nil
}

func (imc *InternalMessageCreate) sqlSave(ctx context.Context) (*InternalMessage, error) {
	if err := imc.check(); err != nil {
		return nil, err
	}
	_node, _spec := imc.createSpec()
	if err := sqlgraph.CreateNode(ctx, imc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	imc.mutation.id = &_node.ID
	imc.mutation.done = true
	return _node, nil
}

func (imc *InternalMessageCreate) createSpec() (*InternalMessage, *sqlgraph.CreateSpec) {
	var (
		_node = &InternalMessage{config: imc.config}
		_spec = sqlgraph.NewCreateSpec(internalmessage.Table, sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt))
	)
	if value, ok := imc.mutation.CreatedAt(); ok {
		_spec.SetField(internalmessage.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := imc.mutation.UpdatedAt(); ok {
		_spec.SetField(internalmessage.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := imc.mutation.Title(); ok {
		_spec.SetField(internalmessage.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := imc.mutation.Content(); ok {
		_spec.SetField(internalmessage.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if nodes := imc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   internalmessage.UsersTable,
			Columns: []string{internalmessage.UsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// InternalMessageCreateBulk is the builder for creating many InternalMessage entities in bulk.
type InternalMessageCreateBulk struct {
	config
	err      error
	builders []*InternalMessageCreate
}

// Save creates the InternalMessage entities in the database.
func (imcb *InternalMessageCreateBulk) Save(ctx context.Context) ([]*InternalMessage, error) {
	if imcb.err != nil {
		return nil, imcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(imcb.builders))
	nodes := make([]*InternalMessage, len(imcb.builders))
	mutators := make([]Mutator, len(imcb.builders))
	for i := range imcb.builders {
		func(i int, root context.Context) {
			builder := imcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*InternalMessageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, imcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, imcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, imcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (imcb *InternalMessageCreateBulk) SaveX(ctx context.Context) []*InternalMessage {
	v, err := imcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (imcb *InternalMessageCreateBulk) Exec(ctx context.Context) error {
	_, err := imcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (imcb *InternalMessageCreateBulk) ExecX(ctx context.Context) {
	if err := imcb.Exec(ctx); err != nil {
		panic(err)
	}
}
