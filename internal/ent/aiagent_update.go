// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/schema"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/dialect/sql/sqljson"
	"entgo.io/ent/schema/field"
)

// AiAgentUpdate is the builder for updating AiAgent entities.
type AiAgentUpdate struct {
	config
	hooks    []Hook
	mutation *AiAgentMutation
}

// Where appends a list predicates to the AiAgentUpdate builder.
func (aau *AiAgentUpdate) Where(ps ...predicate.AiAgent) *AiAgentUpdate {
	aau.mutation.Where(ps...)
	return aau
}

// SetUpdatedAt sets the "updated_at" field.
func (aau *AiAgentUpdate) SetUpdatedAt(t time.Time) *AiAgentUpdate {
	aau.mutation.SetUpdatedAt(t)
	return aau
}

// SetName sets the "name" field.
func (aau *AiAgentUpdate) SetName(s string) *AiAgentUpdate {
	aau.mutation.SetName(s)
	return aau
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableName(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetName(*s)
	}
	return aau
}

// SetIcon sets the "icon" field.
func (aau *AiAgentUpdate) SetIcon(s string) *AiAgentUpdate {
	aau.mutation.SetIcon(s)
	return aau
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableIcon(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetIcon(*s)
	}
	return aau
}

// ClearIcon clears the value of the "icon" field.
func (aau *AiAgentUpdate) ClearIcon() *AiAgentUpdate {
	aau.mutation.ClearIcon()
	return aau
}

// SetDescription sets the "description" field.
func (aau *AiAgentUpdate) SetDescription(s string) *AiAgentUpdate {
	aau.mutation.SetDescription(s)
	return aau
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableDescription(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetDescription(*s)
	}
	return aau
}

// ClearDescription clears the value of the "description" field.
func (aau *AiAgentUpdate) ClearDescription() *AiAgentUpdate {
	aau.mutation.ClearDescription()
	return aau
}

// SetTarget sets the "target" field.
func (aau *AiAgentUpdate) SetTarget(s string) *AiAgentUpdate {
	aau.mutation.SetTarget(s)
	return aau
}

// SetNillableTarget sets the "target" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableTarget(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetTarget(*s)
	}
	return aau
}

// SetGuide sets the "guide" field.
func (aau *AiAgentUpdate) SetGuide(s string) *AiAgentUpdate {
	aau.mutation.SetGuide(s)
	return aau
}

// SetNillableGuide sets the "guide" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableGuide(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetGuide(*s)
	}
	return aau
}

// ClearGuide clears the value of the "guide" field.
func (aau *AiAgentUpdate) ClearGuide() *AiAgentUpdate {
	aau.mutation.ClearGuide()
	return aau
}

// SetSecret sets the "secret" field.
func (aau *AiAgentUpdate) SetSecret(s string) *AiAgentUpdate {
	aau.mutation.SetSecret(s)
	return aau
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableSecret(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetSecret(*s)
	}
	return aau
}

// SetMethod sets the "method" field.
func (aau *AiAgentUpdate) SetMethod(s string) *AiAgentUpdate {
	aau.mutation.SetMethod(s)
	return aau
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableMethod(s *string) *AiAgentUpdate {
	if s != nil {
		aau.SetMethod(*s)
	}
	return aau
}

// SetInputs sets the "inputs" field.
func (aau *AiAgentUpdate) SetInputs(sap []schema.AiAgentParam) *AiAgentUpdate {
	aau.mutation.SetInputs(sap)
	return aau
}

// AppendInputs appends sap to the "inputs" field.
func (aau *AiAgentUpdate) AppendInputs(sap []schema.AiAgentParam) *AiAgentUpdate {
	aau.mutation.AppendInputs(sap)
	return aau
}

// ClearInputs clears the value of the "inputs" field.
func (aau *AiAgentUpdate) ClearInputs() *AiAgentUpdate {
	aau.mutation.ClearInputs()
	return aau
}

// SetStatus sets the "status" field.
func (aau *AiAgentUpdate) SetStatus(i int8) *AiAgentUpdate {
	aau.mutation.ResetStatus()
	aau.mutation.SetStatus(i)
	return aau
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (aau *AiAgentUpdate) SetNillableStatus(i *int8) *AiAgentUpdate {
	if i != nil {
		aau.SetStatus(*i)
	}
	return aau
}

// AddStatus adds i to the "status" field.
func (aau *AiAgentUpdate) AddStatus(i int8) *AiAgentUpdate {
	aau.mutation.AddStatus(i)
	return aau
}

// AddConversationIDs adds the "conversations" edge to the Conversation entity by IDs.
func (aau *AiAgentUpdate) AddConversationIDs(ids ...int) *AiAgentUpdate {
	aau.mutation.AddConversationIDs(ids...)
	return aau
}

// AddConversations adds the "conversations" edges to the Conversation entity.
func (aau *AiAgentUpdate) AddConversations(c ...*Conversation) *AiAgentUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return aau.AddConversationIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aau *AiAgentUpdate) Mutation() *AiAgentMutation {
	return aau.mutation
}

// ClearConversations clears all "conversations" edges to the Conversation entity.
func (aau *AiAgentUpdate) ClearConversations() *AiAgentUpdate {
	aau.mutation.ClearConversations()
	return aau
}

// RemoveConversationIDs removes the "conversations" edge to Conversation entities by IDs.
func (aau *AiAgentUpdate) RemoveConversationIDs(ids ...int) *AiAgentUpdate {
	aau.mutation.RemoveConversationIDs(ids...)
	return aau
}

// RemoveConversations removes "conversations" edges to Conversation entities.
func (aau *AiAgentUpdate) RemoveConversations(c ...*Conversation) *AiAgentUpdate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return aau.RemoveConversationIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (aau *AiAgentUpdate) Save(ctx context.Context) (int, error) {
	aau.defaults()
	return withHooks(ctx, aau.sqlSave, aau.mutation, aau.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aau *AiAgentUpdate) SaveX(ctx context.Context) int {
	affected, err := aau.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (aau *AiAgentUpdate) Exec(ctx context.Context) error {
	_, err := aau.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aau *AiAgentUpdate) ExecX(ctx context.Context) {
	if err := aau.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aau *AiAgentUpdate) defaults() {
	if _, ok := aau.mutation.UpdatedAt(); !ok {
		v := aiagent.UpdateDefaultUpdatedAt()
		aau.mutation.SetUpdatedAt(v)
	}
}

func (aau *AiAgentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagent.Table, aiagent.Columns, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt))
	if ps := aau.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aau.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aau.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
	}
	if value, ok := aau.mutation.Icon(); ok {
		_spec.SetField(aiagent.FieldIcon, field.TypeString, value)
	}
	if aau.mutation.IconCleared() {
		_spec.ClearField(aiagent.FieldIcon, field.TypeString)
	}
	if value, ok := aau.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
	}
	if aau.mutation.DescriptionCleared() {
		_spec.ClearField(aiagent.FieldDescription, field.TypeString)
	}
	if value, ok := aau.mutation.Target(); ok {
		_spec.SetField(aiagent.FieldTarget, field.TypeString, value)
	}
	if value, ok := aau.mutation.Guide(); ok {
		_spec.SetField(aiagent.FieldGuide, field.TypeString, value)
	}
	if aau.mutation.GuideCleared() {
		_spec.ClearField(aiagent.FieldGuide, field.TypeString)
	}
	if value, ok := aau.mutation.Secret(); ok {
		_spec.SetField(aiagent.FieldSecret, field.TypeString, value)
	}
	if value, ok := aau.mutation.Method(); ok {
		_spec.SetField(aiagent.FieldMethod, field.TypeString, value)
	}
	if value, ok := aau.mutation.Inputs(); ok {
		_spec.SetField(aiagent.FieldInputs, field.TypeJSON, value)
	}
	if value, ok := aau.mutation.AppendedInputs(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, aiagent.FieldInputs, value)
		})
	}
	if aau.mutation.InputsCleared() {
		_spec.ClearField(aiagent.FieldInputs, field.TypeJSON)
	}
	if value, ok := aau.mutation.Status(); ok {
		_spec.SetField(aiagent.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := aau.mutation.AddedStatus(); ok {
		_spec.AddField(aiagent.FieldStatus, field.TypeInt8, value)
	}
	if aau.mutation.ConversationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aau.mutation.RemovedConversationsIDs(); len(nodes) > 0 && !aau.mutation.ConversationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aau.mutation.ConversationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, aau.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	aau.mutation.done = true
	return n, nil
}

// AiAgentUpdateOne is the builder for updating a single AiAgent entity.
type AiAgentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *AiAgentMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (aauo *AiAgentUpdateOne) SetUpdatedAt(t time.Time) *AiAgentUpdateOne {
	aauo.mutation.SetUpdatedAt(t)
	return aauo
}

// SetName sets the "name" field.
func (aauo *AiAgentUpdateOne) SetName(s string) *AiAgentUpdateOne {
	aauo.mutation.SetName(s)
	return aauo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableName(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetName(*s)
	}
	return aauo
}

// SetIcon sets the "icon" field.
func (aauo *AiAgentUpdateOne) SetIcon(s string) *AiAgentUpdateOne {
	aauo.mutation.SetIcon(s)
	return aauo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableIcon(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetIcon(*s)
	}
	return aauo
}

// ClearIcon clears the value of the "icon" field.
func (aauo *AiAgentUpdateOne) ClearIcon() *AiAgentUpdateOne {
	aauo.mutation.ClearIcon()
	return aauo
}

// SetDescription sets the "description" field.
func (aauo *AiAgentUpdateOne) SetDescription(s string) *AiAgentUpdateOne {
	aauo.mutation.SetDescription(s)
	return aauo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableDescription(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetDescription(*s)
	}
	return aauo
}

// ClearDescription clears the value of the "description" field.
func (aauo *AiAgentUpdateOne) ClearDescription() *AiAgentUpdateOne {
	aauo.mutation.ClearDescription()
	return aauo
}

// SetTarget sets the "target" field.
func (aauo *AiAgentUpdateOne) SetTarget(s string) *AiAgentUpdateOne {
	aauo.mutation.SetTarget(s)
	return aauo
}

// SetNillableTarget sets the "target" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableTarget(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetTarget(*s)
	}
	return aauo
}

// SetGuide sets the "guide" field.
func (aauo *AiAgentUpdateOne) SetGuide(s string) *AiAgentUpdateOne {
	aauo.mutation.SetGuide(s)
	return aauo
}

// SetNillableGuide sets the "guide" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableGuide(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetGuide(*s)
	}
	return aauo
}

// ClearGuide clears the value of the "guide" field.
func (aauo *AiAgentUpdateOne) ClearGuide() *AiAgentUpdateOne {
	aauo.mutation.ClearGuide()
	return aauo
}

// SetSecret sets the "secret" field.
func (aauo *AiAgentUpdateOne) SetSecret(s string) *AiAgentUpdateOne {
	aauo.mutation.SetSecret(s)
	return aauo
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableSecret(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetSecret(*s)
	}
	return aauo
}

// SetMethod sets the "method" field.
func (aauo *AiAgentUpdateOne) SetMethod(s string) *AiAgentUpdateOne {
	aauo.mutation.SetMethod(s)
	return aauo
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableMethod(s *string) *AiAgentUpdateOne {
	if s != nil {
		aauo.SetMethod(*s)
	}
	return aauo
}

// SetInputs sets the "inputs" field.
func (aauo *AiAgentUpdateOne) SetInputs(sap []schema.AiAgentParam) *AiAgentUpdateOne {
	aauo.mutation.SetInputs(sap)
	return aauo
}

// AppendInputs appends sap to the "inputs" field.
func (aauo *AiAgentUpdateOne) AppendInputs(sap []schema.AiAgentParam) *AiAgentUpdateOne {
	aauo.mutation.AppendInputs(sap)
	return aauo
}

// ClearInputs clears the value of the "inputs" field.
func (aauo *AiAgentUpdateOne) ClearInputs() *AiAgentUpdateOne {
	aauo.mutation.ClearInputs()
	return aauo
}

// SetStatus sets the "status" field.
func (aauo *AiAgentUpdateOne) SetStatus(i int8) *AiAgentUpdateOne {
	aauo.mutation.ResetStatus()
	aauo.mutation.SetStatus(i)
	return aauo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (aauo *AiAgentUpdateOne) SetNillableStatus(i *int8) *AiAgentUpdateOne {
	if i != nil {
		aauo.SetStatus(*i)
	}
	return aauo
}

// AddStatus adds i to the "status" field.
func (aauo *AiAgentUpdateOne) AddStatus(i int8) *AiAgentUpdateOne {
	aauo.mutation.AddStatus(i)
	return aauo
}

// AddConversationIDs adds the "conversations" edge to the Conversation entity by IDs.
func (aauo *AiAgentUpdateOne) AddConversationIDs(ids ...int) *AiAgentUpdateOne {
	aauo.mutation.AddConversationIDs(ids...)
	return aauo
}

// AddConversations adds the "conversations" edges to the Conversation entity.
func (aauo *AiAgentUpdateOne) AddConversations(c ...*Conversation) *AiAgentUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return aauo.AddConversationIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aauo *AiAgentUpdateOne) Mutation() *AiAgentMutation {
	return aauo.mutation
}

// ClearConversations clears all "conversations" edges to the Conversation entity.
func (aauo *AiAgentUpdateOne) ClearConversations() *AiAgentUpdateOne {
	aauo.mutation.ClearConversations()
	return aauo
}

// RemoveConversationIDs removes the "conversations" edge to Conversation entities by IDs.
func (aauo *AiAgentUpdateOne) RemoveConversationIDs(ids ...int) *AiAgentUpdateOne {
	aauo.mutation.RemoveConversationIDs(ids...)
	return aauo
}

// RemoveConversations removes "conversations" edges to Conversation entities.
func (aauo *AiAgentUpdateOne) RemoveConversations(c ...*Conversation) *AiAgentUpdateOne {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return aauo.RemoveConversationIDs(ids...)
}

// Where appends a list predicates to the AiAgentUpdate builder.
func (aauo *AiAgentUpdateOne) Where(ps ...predicate.AiAgent) *AiAgentUpdateOne {
	aauo.mutation.Where(ps...)
	return aauo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (aauo *AiAgentUpdateOne) Select(field string, fields ...string) *AiAgentUpdateOne {
	aauo.fields = append([]string{field}, fields...)
	return aauo
}

// Save executes the query and returns the updated AiAgent entity.
func (aauo *AiAgentUpdateOne) Save(ctx context.Context) (*AiAgent, error) {
	aauo.defaults()
	return withHooks(ctx, aauo.sqlSave, aauo.mutation, aauo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (aauo *AiAgentUpdateOne) SaveX(ctx context.Context) *AiAgent {
	node, err := aauo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (aauo *AiAgentUpdateOne) Exec(ctx context.Context) error {
	_, err := aauo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aauo *AiAgentUpdateOne) ExecX(ctx context.Context) {
	if err := aauo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aauo *AiAgentUpdateOne) defaults() {
	if _, ok := aauo.mutation.UpdatedAt(); !ok {
		v := aiagent.UpdateDefaultUpdatedAt()
		aauo.mutation.SetUpdatedAt(v)
	}
}

func (aauo *AiAgentUpdateOne) sqlSave(ctx context.Context) (_node *AiAgent, err error) {
	_spec := sqlgraph.NewUpdateSpec(aiagent.Table, aiagent.Columns, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt))
	id, ok := aauo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "AiAgent.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := aauo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, aiagent.FieldID)
		for _, f := range fields {
			if !aiagent.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != aiagent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := aauo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := aauo.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := aauo.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Icon(); ok {
		_spec.SetField(aiagent.FieldIcon, field.TypeString, value)
	}
	if aauo.mutation.IconCleared() {
		_spec.ClearField(aiagent.FieldIcon, field.TypeString)
	}
	if value, ok := aauo.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
	}
	if aauo.mutation.DescriptionCleared() {
		_spec.ClearField(aiagent.FieldDescription, field.TypeString)
	}
	if value, ok := aauo.mutation.Target(); ok {
		_spec.SetField(aiagent.FieldTarget, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Guide(); ok {
		_spec.SetField(aiagent.FieldGuide, field.TypeString, value)
	}
	if aauo.mutation.GuideCleared() {
		_spec.ClearField(aiagent.FieldGuide, field.TypeString)
	}
	if value, ok := aauo.mutation.Secret(); ok {
		_spec.SetField(aiagent.FieldSecret, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Method(); ok {
		_spec.SetField(aiagent.FieldMethod, field.TypeString, value)
	}
	if value, ok := aauo.mutation.Inputs(); ok {
		_spec.SetField(aiagent.FieldInputs, field.TypeJSON, value)
	}
	if value, ok := aauo.mutation.AppendedInputs(); ok {
		_spec.AddModifier(func(u *sql.UpdateBuilder) {
			sqljson.Append(u, aiagent.FieldInputs, value)
		})
	}
	if aauo.mutation.InputsCleared() {
		_spec.ClearField(aiagent.FieldInputs, field.TypeJSON)
	}
	if value, ok := aauo.mutation.Status(); ok {
		_spec.SetField(aiagent.FieldStatus, field.TypeInt8, value)
	}
	if value, ok := aauo.mutation.AddedStatus(); ok {
		_spec.AddField(aiagent.FieldStatus, field.TypeInt8, value)
	}
	if aauo.mutation.ConversationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aauo.mutation.RemovedConversationsIDs(); len(nodes) > 0 && !aauo.mutation.ConversationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := aauo.mutation.ConversationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &AiAgent{config: aauo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, aauo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{aiagent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	aauo.mutation.done = true
	return _node, nil
}
