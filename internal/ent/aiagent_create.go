// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/schema"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AiAgentCreate is the builder for creating a AiAgent entity.
type AiAgentCreate struct {
	config
	mutation *AiAgentMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (aac *AiAgentCreate) SetCreatedAt(t time.Time) *AiAgentCreate {
	aac.mutation.SetCreatedAt(t)
	return aac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableCreatedAt(t *time.Time) *AiAgentCreate {
	if t != nil {
		aac.SetCreatedAt(*t)
	}
	return aac
}

// SetUpdatedAt sets the "updated_at" field.
func (aac *AiAgentCreate) SetUpdatedAt(t time.Time) *AiAgentCreate {
	aac.mutation.SetUpdatedAt(t)
	return aac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableUpdatedAt(t *time.Time) *AiAgentCreate {
	if t != nil {
		aac.SetUpdatedAt(*t)
	}
	return aac
}

// SetName sets the "name" field.
func (aac *AiAgentCreate) SetName(s string) *AiAgentCreate {
	aac.mutation.SetName(s)
	return aac
}

// SetIcon sets the "icon" field.
func (aac *AiAgentCreate) SetIcon(s string) *AiAgentCreate {
	aac.mutation.SetIcon(s)
	return aac
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableIcon(s *string) *AiAgentCreate {
	if s != nil {
		aac.SetIcon(*s)
	}
	return aac
}

// SetDescription sets the "description" field.
func (aac *AiAgentCreate) SetDescription(s string) *AiAgentCreate {
	aac.mutation.SetDescription(s)
	return aac
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableDescription(s *string) *AiAgentCreate {
	if s != nil {
		aac.SetDescription(*s)
	}
	return aac
}

// SetTarget sets the "target" field.
func (aac *AiAgentCreate) SetTarget(s string) *AiAgentCreate {
	aac.mutation.SetTarget(s)
	return aac
}

// SetGuide sets the "guide" field.
func (aac *AiAgentCreate) SetGuide(s string) *AiAgentCreate {
	aac.mutation.SetGuide(s)
	return aac
}

// SetNillableGuide sets the "guide" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableGuide(s *string) *AiAgentCreate {
	if s != nil {
		aac.SetGuide(*s)
	}
	return aac
}

// SetSecret sets the "secret" field.
func (aac *AiAgentCreate) SetSecret(s string) *AiAgentCreate {
	aac.mutation.SetSecret(s)
	return aac
}

// SetMethod sets the "method" field.
func (aac *AiAgentCreate) SetMethod(s string) *AiAgentCreate {
	aac.mutation.SetMethod(s)
	return aac
}

// SetInputs sets the "inputs" field.
func (aac *AiAgentCreate) SetInputs(sap []schema.AiAgentParam) *AiAgentCreate {
	aac.mutation.SetInputs(sap)
	return aac
}

// SetStatus sets the "status" field.
func (aac *AiAgentCreate) SetStatus(i int8) *AiAgentCreate {
	aac.mutation.SetStatus(i)
	return aac
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (aac *AiAgentCreate) SetNillableStatus(i *int8) *AiAgentCreate {
	if i != nil {
		aac.SetStatus(*i)
	}
	return aac
}

// AddConversationIDs adds the "conversations" edge to the Conversation entity by IDs.
func (aac *AiAgentCreate) AddConversationIDs(ids ...int) *AiAgentCreate {
	aac.mutation.AddConversationIDs(ids...)
	return aac
}

// AddConversations adds the "conversations" edges to the Conversation entity.
func (aac *AiAgentCreate) AddConversations(c ...*Conversation) *AiAgentCreate {
	ids := make([]int, len(c))
	for i := range c {
		ids[i] = c[i].ID
	}
	return aac.AddConversationIDs(ids...)
}

// Mutation returns the AiAgentMutation object of the builder.
func (aac *AiAgentCreate) Mutation() *AiAgentMutation {
	return aac.mutation
}

// Save creates the AiAgent in the database.
func (aac *AiAgentCreate) Save(ctx context.Context) (*AiAgent, error) {
	aac.defaults()
	return withHooks(ctx, aac.sqlSave, aac.mutation, aac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (aac *AiAgentCreate) SaveX(ctx context.Context) *AiAgent {
	v, err := aac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aac *AiAgentCreate) Exec(ctx context.Context) error {
	_, err := aac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aac *AiAgentCreate) ExecX(ctx context.Context) {
	if err := aac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (aac *AiAgentCreate) defaults() {
	if _, ok := aac.mutation.CreatedAt(); !ok {
		v := aiagent.DefaultCreatedAt()
		aac.mutation.SetCreatedAt(v)
	}
	if _, ok := aac.mutation.UpdatedAt(); !ok {
		v := aiagent.DefaultUpdatedAt()
		aac.mutation.SetUpdatedAt(v)
	}
	if _, ok := aac.mutation.Status(); !ok {
		v := aiagent.DefaultStatus
		aac.mutation.SetStatus(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (aac *AiAgentCreate) check() error {
	if _, ok := aac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "AiAgent.created_at"`)}
	}
	if _, ok := aac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "AiAgent.updated_at"`)}
	}
	if _, ok := aac.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "AiAgent.name"`)}
	}
	if _, ok := aac.mutation.Target(); !ok {
		return &ValidationError{Name: "target", err: errors.New(`ent: missing required field "AiAgent.target"`)}
	}
	if _, ok := aac.mutation.Secret(); !ok {
		return &ValidationError{Name: "secret", err: errors.New(`ent: missing required field "AiAgent.secret"`)}
	}
	if _, ok := aac.mutation.Method(); !ok {
		return &ValidationError{Name: "method", err: errors.New(`ent: missing required field "AiAgent.method"`)}
	}
	if _, ok := aac.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "AiAgent.status"`)}
	}
	return nil
}

func (aac *AiAgentCreate) sqlSave(ctx context.Context) (*AiAgent, error) {
	if err := aac.check(); err != nil {
		return nil, err
	}
	_node, _spec := aac.createSpec()
	if err := sqlgraph.CreateNode(ctx, aac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	aac.mutation.id = &_node.ID
	aac.mutation.done = true
	return _node, nil
}

func (aac *AiAgentCreate) createSpec() (*AiAgent, *sqlgraph.CreateSpec) {
	var (
		_node = &AiAgent{config: aac.config}
		_spec = sqlgraph.NewCreateSpec(aiagent.Table, sqlgraph.NewFieldSpec(aiagent.FieldID, field.TypeInt))
	)
	if value, ok := aac.mutation.CreatedAt(); ok {
		_spec.SetField(aiagent.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := aac.mutation.UpdatedAt(); ok {
		_spec.SetField(aiagent.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := aac.mutation.Name(); ok {
		_spec.SetField(aiagent.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := aac.mutation.Icon(); ok {
		_spec.SetField(aiagent.FieldIcon, field.TypeString, value)
		_node.Icon = value
	}
	if value, ok := aac.mutation.Description(); ok {
		_spec.SetField(aiagent.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := aac.mutation.Target(); ok {
		_spec.SetField(aiagent.FieldTarget, field.TypeString, value)
		_node.Target = value
	}
	if value, ok := aac.mutation.Guide(); ok {
		_spec.SetField(aiagent.FieldGuide, field.TypeString, value)
		_node.Guide = value
	}
	if value, ok := aac.mutation.Secret(); ok {
		_spec.SetField(aiagent.FieldSecret, field.TypeString, value)
		_node.Secret = value
	}
	if value, ok := aac.mutation.Method(); ok {
		_spec.SetField(aiagent.FieldMethod, field.TypeString, value)
		_node.Method = value
	}
	if value, ok := aac.mutation.Inputs(); ok {
		_spec.SetField(aiagent.FieldInputs, field.TypeJSON, value)
		_node.Inputs = value
	}
	if value, ok := aac.mutation.Status(); ok {
		_spec.SetField(aiagent.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if nodes := aac.mutation.ConversationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   aiagent.ConversationsTable,
			Columns: []string{aiagent.ConversationsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// AiAgentCreateBulk is the builder for creating many AiAgent entities in bulk.
type AiAgentCreateBulk struct {
	config
	err      error
	builders []*AiAgentCreate
}

// Save creates the AiAgent entities in the database.
func (aacb *AiAgentCreateBulk) Save(ctx context.Context) ([]*AiAgent, error) {
	if aacb.err != nil {
		return nil, aacb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(aacb.builders))
	nodes := make([]*AiAgent, len(aacb.builders))
	mutators := make([]Mutator, len(aacb.builders))
	for i := range aacb.builders {
		func(i int, root context.Context) {
			builder := aacb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*AiAgentMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, aacb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, aacb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, aacb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (aacb *AiAgentCreateBulk) SaveX(ctx context.Context) []*AiAgent {
	v, err := aacb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (aacb *AiAgentCreateBulk) Exec(ctx context.Context) error {
	_, err := aacb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (aacb *AiAgentCreateBulk) ExecX(ctx context.Context) {
	if err := aacb.Exec(ctx); err != nil {
		panic(err)
	}
}
