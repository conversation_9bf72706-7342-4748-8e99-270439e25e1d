// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserInternalMessageCreate is the builder for creating a UserInternalMessage entity.
type UserInternalMessageCreate struct {
	config
	mutation *UserInternalMessageMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (uimc *UserInternalMessageCreate) SetCreatedAt(t time.Time) *UserInternalMessageCreate {
	uimc.mutation.SetCreatedAt(t)
	return uimc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uimc *UserInternalMessageCreate) SetNillableCreatedAt(t *time.Time) *UserInternalMessageCreate {
	if t != nil {
		uimc.SetCreatedAt(*t)
	}
	return uimc
}

// SetUpdatedAt sets the "updated_at" field.
func (uimc *UserInternalMessageCreate) SetUpdatedAt(t time.Time) *UserInternalMessageCreate {
	uimc.mutation.SetUpdatedAt(t)
	return uimc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uimc *UserInternalMessageCreate) SetNillableUpdatedAt(t *time.Time) *UserInternalMessageCreate {
	if t != nil {
		uimc.SetUpdatedAt(*t)
	}
	return uimc
}

// SetInternalMessageID sets the "internal_message_id" field.
func (uimc *UserInternalMessageCreate) SetInternalMessageID(i int) *UserInternalMessageCreate {
	uimc.mutation.SetInternalMessageID(i)
	return uimc
}

// SetUserID sets the "user_id" field.
func (uimc *UserInternalMessageCreate) SetUserID(i int) *UserInternalMessageCreate {
	uimc.mutation.SetUserID(i)
	return uimc
}

// SetStatus sets the "status" field.
func (uimc *UserInternalMessageCreate) SetStatus(i int8) *UserInternalMessageCreate {
	uimc.mutation.SetStatus(i)
	return uimc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uimc *UserInternalMessageCreate) SetNillableStatus(i *int8) *UserInternalMessageCreate {
	if i != nil {
		uimc.SetStatus(*i)
	}
	return uimc
}

// SetMessageID sets the "message" edge to the InternalMessage entity by ID.
func (uimc *UserInternalMessageCreate) SetMessageID(id int) *UserInternalMessageCreate {
	uimc.mutation.SetMessageID(id)
	return uimc
}

// SetMessage sets the "message" edge to the InternalMessage entity.
func (uimc *UserInternalMessageCreate) SetMessage(i *InternalMessage) *UserInternalMessageCreate {
	return uimc.SetMessageID(i.ID)
}

// SetUser sets the "user" edge to the User entity.
func (uimc *UserInternalMessageCreate) SetUser(u *User) *UserInternalMessageCreate {
	return uimc.SetUserID(u.ID)
}

// Mutation returns the UserInternalMessageMutation object of the builder.
func (uimc *UserInternalMessageCreate) Mutation() *UserInternalMessageMutation {
	return uimc.mutation
}

// Save creates the UserInternalMessage in the database.
func (uimc *UserInternalMessageCreate) Save(ctx context.Context) (*UserInternalMessage, error) {
	uimc.defaults()
	return withHooks(ctx, uimc.sqlSave, uimc.mutation, uimc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uimc *UserInternalMessageCreate) SaveX(ctx context.Context) *UserInternalMessage {
	v, err := uimc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uimc *UserInternalMessageCreate) Exec(ctx context.Context) error {
	_, err := uimc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uimc *UserInternalMessageCreate) ExecX(ctx context.Context) {
	if err := uimc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uimc *UserInternalMessageCreate) defaults() {
	if _, ok := uimc.mutation.CreatedAt(); !ok {
		v := userinternalmessage.DefaultCreatedAt()
		uimc.mutation.SetCreatedAt(v)
	}
	if _, ok := uimc.mutation.UpdatedAt(); !ok {
		v := userinternalmessage.DefaultUpdatedAt()
		uimc.mutation.SetUpdatedAt(v)
	}
	if _, ok := uimc.mutation.Status(); !ok {
		v := userinternalmessage.DefaultStatus
		uimc.mutation.SetStatus(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (uimc *UserInternalMessageCreate) check() error {
	if _, ok := uimc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "UserInternalMessage.created_at"`)}
	}
	if _, ok := uimc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "UserInternalMessage.updated_at"`)}
	}
	if _, ok := uimc.mutation.InternalMessageID(); !ok {
		return &ValidationError{Name: "internal_message_id", err: errors.New(`ent: missing required field "UserInternalMessage.internal_message_id"`)}
	}
	if _, ok := uimc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "UserInternalMessage.user_id"`)}
	}
	if _, ok := uimc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "UserInternalMessage.status"`)}
	}
	if len(uimc.mutation.MessageIDs()) == 0 {
		return &ValidationError{Name: "message", err: errors.New(`ent: missing required edge "UserInternalMessage.message"`)}
	}
	if len(uimc.mutation.UserIDs()) == 0 {
		return &ValidationError{Name: "user", err: errors.New(`ent: missing required edge "UserInternalMessage.user"`)}
	}
	return nil
}

func (uimc *UserInternalMessageCreate) sqlSave(ctx context.Context) (*UserInternalMessage, error) {
	if err := uimc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uimc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uimc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	uimc.mutation.id = &_node.ID
	uimc.mutation.done = true
	return _node, nil
}

func (uimc *UserInternalMessageCreate) createSpec() (*UserInternalMessage, *sqlgraph.CreateSpec) {
	var (
		_node = &UserInternalMessage{config: uimc.config}
		_spec = sqlgraph.NewCreateSpec(userinternalmessage.Table, sqlgraph.NewFieldSpec(userinternalmessage.FieldID, field.TypeInt))
	)
	if value, ok := uimc.mutation.CreatedAt(); ok {
		_spec.SetField(userinternalmessage.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uimc.mutation.UpdatedAt(); ok {
		_spec.SetField(userinternalmessage.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uimc.mutation.Status(); ok {
		_spec.SetField(userinternalmessage.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if nodes := uimc.mutation.MessageIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.MessageTable,
			Columns: []string{userinternalmessage.MessageColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(internalmessage.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.InternalMessageID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uimc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   userinternalmessage.UserTable,
			Columns: []string{userinternalmessage.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// UserInternalMessageCreateBulk is the builder for creating many UserInternalMessage entities in bulk.
type UserInternalMessageCreateBulk struct {
	config
	err      error
	builders []*UserInternalMessageCreate
}

// Save creates the UserInternalMessage entities in the database.
func (uimcb *UserInternalMessageCreateBulk) Save(ctx context.Context) ([]*UserInternalMessage, error) {
	if uimcb.err != nil {
		return nil, uimcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(uimcb.builders))
	nodes := make([]*UserInternalMessage, len(uimcb.builders))
	mutators := make([]Mutator, len(uimcb.builders))
	for i := range uimcb.builders {
		func(i int, root context.Context) {
			builder := uimcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserInternalMessageMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, uimcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, uimcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, uimcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (uimcb *UserInternalMessageCreateBulk) SaveX(ctx context.Context) []*UserInternalMessage {
	v, err := uimcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uimcb *UserInternalMessageCreateBulk) Exec(ctx context.Context) error {
	_, err := uimcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uimcb *UserInternalMessageCreateBulk) ExecX(ctx context.Context) {
	if err := uimcb.Exec(ctx); err != nil {
		panic(err)
	}
}
