// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/adminuser"
	"bole-ai/internal/ent/aiagent"
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/project"
	"bole-ai/internal/ent/schema"
	"bole-ai/internal/ent/screenplay"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"time"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	adminuserMixin := schema.AdminUser{}.Mixin()
	adminuserMixinFields0 := adminuserMixin[0].Fields()
	_ = adminuserMixinFields0
	adminuserFields := schema.AdminUser{}.Fields()
	_ = adminuserFields
	// adminuserDescCreatedAt is the schema descriptor for created_at field.
	adminuserDescCreatedAt := adminuserMixinFields0[0].Descriptor()
	// adminuser.DefaultCreatedAt holds the default value on creation for the created_at field.
	adminuser.DefaultCreatedAt = adminuserDescCreatedAt.Default.(func() time.Time)
	// adminuserDescUpdatedAt is the schema descriptor for updated_at field.
	adminuserDescUpdatedAt := adminuserMixinFields0[1].Descriptor()
	// adminuser.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	adminuser.DefaultUpdatedAt = adminuserDescUpdatedAt.Default.(func() time.Time)
	// adminuser.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	adminuser.UpdateDefaultUpdatedAt = adminuserDescUpdatedAt.UpdateDefault.(func() time.Time)
	aiagentMixin := schema.AiAgent{}.Mixin()
	aiagentMixinFields0 := aiagentMixin[0].Fields()
	_ = aiagentMixinFields0
	aiagentFields := schema.AiAgent{}.Fields()
	_ = aiagentFields
	// aiagentDescCreatedAt is the schema descriptor for created_at field.
	aiagentDescCreatedAt := aiagentMixinFields0[0].Descriptor()
	// aiagent.DefaultCreatedAt holds the default value on creation for the created_at field.
	aiagent.DefaultCreatedAt = aiagentDescCreatedAt.Default.(func() time.Time)
	// aiagentDescUpdatedAt is the schema descriptor for updated_at field.
	aiagentDescUpdatedAt := aiagentMixinFields0[1].Descriptor()
	// aiagent.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	aiagent.DefaultUpdatedAt = aiagentDescUpdatedAt.Default.(func() time.Time)
	// aiagent.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	aiagent.UpdateDefaultUpdatedAt = aiagentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// aiagentDescStatus is the schema descriptor for status field.
	aiagentDescStatus := aiagentFields[8].Descriptor()
	// aiagent.DefaultStatus holds the default value on creation for the status field.
	aiagent.DefaultStatus = aiagentDescStatus.Default.(int8)
	batchMixin := schema.Batch{}.Mixin()
	batchMixinFields0 := batchMixin[0].Fields()
	_ = batchMixinFields0
	batchFields := schema.Batch{}.Fields()
	_ = batchFields
	// batchDescCreatedAt is the schema descriptor for created_at field.
	batchDescCreatedAt := batchMixinFields0[0].Descriptor()
	// batch.DefaultCreatedAt holds the default value on creation for the created_at field.
	batch.DefaultCreatedAt = batchDescCreatedAt.Default.(func() time.Time)
	// batchDescUpdatedAt is the schema descriptor for updated_at field.
	batchDescUpdatedAt := batchMixinFields0[1].Descriptor()
	// batch.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	batch.DefaultUpdatedAt = batchDescUpdatedAt.Default.(func() time.Time)
	// batch.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	batch.UpdateDefaultUpdatedAt = batchDescUpdatedAt.UpdateDefault.(func() time.Time)
	conversationMixin := schema.Conversation{}.Mixin()
	conversationMixinFields0 := conversationMixin[0].Fields()
	_ = conversationMixinFields0
	conversationFields := schema.Conversation{}.Fields()
	_ = conversationFields
	// conversationDescCreatedAt is the schema descriptor for created_at field.
	conversationDescCreatedAt := conversationMixinFields0[0].Descriptor()
	// conversation.DefaultCreatedAt holds the default value on creation for the created_at field.
	conversation.DefaultCreatedAt = conversationDescCreatedAt.Default.(func() time.Time)
	// conversationDescUpdatedAt is the schema descriptor for updated_at field.
	conversationDescUpdatedAt := conversationMixinFields0[1].Descriptor()
	// conversation.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	conversation.DefaultUpdatedAt = conversationDescUpdatedAt.Default.(func() time.Time)
	// conversation.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	conversation.UpdateDefaultUpdatedAt = conversationDescUpdatedAt.UpdateDefault.(func() time.Time)
	conversationcontentMixin := schema.ConversationContent{}.Mixin()
	conversationcontentMixinFields0 := conversationcontentMixin[0].Fields()
	_ = conversationcontentMixinFields0
	conversationcontentFields := schema.ConversationContent{}.Fields()
	_ = conversationcontentFields
	// conversationcontentDescCreatedAt is the schema descriptor for created_at field.
	conversationcontentDescCreatedAt := conversationcontentMixinFields0[0].Descriptor()
	// conversationcontent.DefaultCreatedAt holds the default value on creation for the created_at field.
	conversationcontent.DefaultCreatedAt = conversationcontentDescCreatedAt.Default.(func() time.Time)
	// conversationcontentDescUpdatedAt is the schema descriptor for updated_at field.
	conversationcontentDescUpdatedAt := conversationcontentMixinFields0[1].Descriptor()
	// conversationcontent.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	conversationcontent.DefaultUpdatedAt = conversationcontentDescUpdatedAt.Default.(func() time.Time)
	// conversationcontent.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	conversationcontent.UpdateDefaultUpdatedAt = conversationcontentDescUpdatedAt.UpdateDefault.(func() time.Time)
	// conversationcontentDescType is the schema descriptor for type field.
	conversationcontentDescType := conversationcontentFields[2].Descriptor()
	// conversationcontent.DefaultType holds the default value on creation for the type field.
	conversationcontent.DefaultType = conversationcontentDescType.Default.(int8)
	frequentaskquestionMixin := schema.FrequentAskQuestion{}.Mixin()
	frequentaskquestionMixinFields0 := frequentaskquestionMixin[0].Fields()
	_ = frequentaskquestionMixinFields0
	frequentaskquestionFields := schema.FrequentAskQuestion{}.Fields()
	_ = frequentaskquestionFields
	// frequentaskquestionDescCreatedAt is the schema descriptor for created_at field.
	frequentaskquestionDescCreatedAt := frequentaskquestionMixinFields0[0].Descriptor()
	// frequentaskquestion.DefaultCreatedAt holds the default value on creation for the created_at field.
	frequentaskquestion.DefaultCreatedAt = frequentaskquestionDescCreatedAt.Default.(func() time.Time)
	// frequentaskquestionDescUpdatedAt is the schema descriptor for updated_at field.
	frequentaskquestionDescUpdatedAt := frequentaskquestionMixinFields0[1].Descriptor()
	// frequentaskquestion.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	frequentaskquestion.DefaultUpdatedAt = frequentaskquestionDescUpdatedAt.Default.(func() time.Time)
	// frequentaskquestion.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	frequentaskquestion.UpdateDefaultUpdatedAt = frequentaskquestionDescUpdatedAt.UpdateDefault.(func() time.Time)
	internalmessageMixin := schema.InternalMessage{}.Mixin()
	internalmessageMixinFields0 := internalmessageMixin[0].Fields()
	_ = internalmessageMixinFields0
	internalmessageFields := schema.InternalMessage{}.Fields()
	_ = internalmessageFields
	// internalmessageDescCreatedAt is the schema descriptor for created_at field.
	internalmessageDescCreatedAt := internalmessageMixinFields0[0].Descriptor()
	// internalmessage.DefaultCreatedAt holds the default value on creation for the created_at field.
	internalmessage.DefaultCreatedAt = internalmessageDescCreatedAt.Default.(func() time.Time)
	// internalmessageDescUpdatedAt is the schema descriptor for updated_at field.
	internalmessageDescUpdatedAt := internalmessageMixinFields0[1].Descriptor()
	// internalmessage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	internalmessage.DefaultUpdatedAt = internalmessageDescUpdatedAt.Default.(func() time.Time)
	// internalmessage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	internalmessage.UpdateDefaultUpdatedAt = internalmessageDescUpdatedAt.UpdateDefault.(func() time.Time)
	projectMixin := schema.Project{}.Mixin()
	projectMixinFields0 := projectMixin[0].Fields()
	_ = projectMixinFields0
	projectFields := schema.Project{}.Fields()
	_ = projectFields
	// projectDescCreatedAt is the schema descriptor for created_at field.
	projectDescCreatedAt := projectMixinFields0[0].Descriptor()
	// project.DefaultCreatedAt holds the default value on creation for the created_at field.
	project.DefaultCreatedAt = projectDescCreatedAt.Default.(func() time.Time)
	// projectDescUpdatedAt is the schema descriptor for updated_at field.
	projectDescUpdatedAt := projectMixinFields0[1].Descriptor()
	// project.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	project.DefaultUpdatedAt = projectDescUpdatedAt.Default.(func() time.Time)
	// project.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	project.UpdateDefaultUpdatedAt = projectDescUpdatedAt.UpdateDefault.(func() time.Time)
	screenplayMixin := schema.Screenplay{}.Mixin()
	screenplayMixinFields0 := screenplayMixin[0].Fields()
	_ = screenplayMixinFields0
	screenplayFields := schema.Screenplay{}.Fields()
	_ = screenplayFields
	// screenplayDescCreatedAt is the schema descriptor for created_at field.
	screenplayDescCreatedAt := screenplayMixinFields0[0].Descriptor()
	// screenplay.DefaultCreatedAt holds the default value on creation for the created_at field.
	screenplay.DefaultCreatedAt = screenplayDescCreatedAt.Default.(func() time.Time)
	// screenplayDescUpdatedAt is the schema descriptor for updated_at field.
	screenplayDescUpdatedAt := screenplayMixinFields0[1].Descriptor()
	// screenplay.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	screenplay.DefaultUpdatedAt = screenplayDescUpdatedAt.Default.(func() time.Time)
	// screenplay.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	screenplay.UpdateDefaultUpdatedAt = screenplayDescUpdatedAt.UpdateDefault.(func() time.Time)
	// screenplayDescType is the schema descriptor for type field.
	screenplayDescType := screenplayFields[4].Descriptor()
	// screenplay.DefaultType holds the default value on creation for the type field.
	screenplay.DefaultType = screenplayDescType.Default.(int8)
	// screenplayDescStatus is the schema descriptor for status field.
	screenplayDescStatus := screenplayFields[5].Descriptor()
	// screenplay.DefaultStatus holds the default value on creation for the status field.
	screenplay.DefaultStatus = screenplayDescStatus.Default.(int8)
	testcodeMixin := schema.TestCode{}.Mixin()
	testcodeMixinFields0 := testcodeMixin[0].Fields()
	_ = testcodeMixinFields0
	testcodeFields := schema.TestCode{}.Fields()
	_ = testcodeFields
	// testcodeDescCreatedAt is the schema descriptor for created_at field.
	testcodeDescCreatedAt := testcodeMixinFields0[0].Descriptor()
	// testcode.DefaultCreatedAt holds the default value on creation for the created_at field.
	testcode.DefaultCreatedAt = testcodeDescCreatedAt.Default.(func() time.Time)
	// testcodeDescUpdatedAt is the schema descriptor for updated_at field.
	testcodeDescUpdatedAt := testcodeMixinFields0[1].Descriptor()
	// testcode.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	testcode.DefaultUpdatedAt = testcodeDescUpdatedAt.Default.(func() time.Time)
	// testcode.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	testcode.UpdateDefaultUpdatedAt = testcodeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// testcodeDescStatus is the schema descriptor for status field.
	testcodeDescStatus := testcodeFields[3].Descriptor()
	// testcode.DefaultStatus holds the default value on creation for the status field.
	testcode.DefaultStatus = testcodeDescStatus.Default.(int8)
	userMixin := schema.User{}.Mixin()
	userMixinFields0 := userMixin[0].Fields()
	_ = userMixinFields0
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userMixinFields0[0].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userMixinFields0[1].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userDescStatus is the schema descriptor for status field.
	userDescStatus := userFields[5].Descriptor()
	// user.DefaultStatus holds the default value on creation for the status field.
	user.DefaultStatus = userDescStatus.Default.(int8)
	userinternalmessageMixin := schema.UserInternalMessage{}.Mixin()
	userinternalmessageMixinFields0 := userinternalmessageMixin[0].Fields()
	_ = userinternalmessageMixinFields0
	userinternalmessageFields := schema.UserInternalMessage{}.Fields()
	_ = userinternalmessageFields
	// userinternalmessageDescCreatedAt is the schema descriptor for created_at field.
	userinternalmessageDescCreatedAt := userinternalmessageMixinFields0[0].Descriptor()
	// userinternalmessage.DefaultCreatedAt holds the default value on creation for the created_at field.
	userinternalmessage.DefaultCreatedAt = userinternalmessageDescCreatedAt.Default.(func() time.Time)
	// userinternalmessageDescUpdatedAt is the schema descriptor for updated_at field.
	userinternalmessageDescUpdatedAt := userinternalmessageMixinFields0[1].Descriptor()
	// userinternalmessage.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	userinternalmessage.DefaultUpdatedAt = userinternalmessageDescUpdatedAt.Default.(func() time.Time)
	// userinternalmessage.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	userinternalmessage.UpdateDefaultUpdatedAt = userinternalmessageDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userinternalmessageDescStatus is the schema descriptor for status field.
	userinternalmessageDescStatus := userinternalmessageFields[2].Descriptor()
	// userinternalmessage.DefaultStatus holds the default value on creation for the status field.
	userinternalmessage.DefaultStatus = userinternalmessageDescStatus.Default.(int8)
}
