// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/screenplay"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ScreenplayCreate is the builder for creating a Screenplay entity.
type ScreenplayCreate struct {
	config
	mutation *ScreenplayMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (sc *ScreenplayCreate) SetCreatedAt(t time.Time) *ScreenplayCreate {
	sc.mutation.SetCreatedAt(t)
	return sc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (sc *ScreenplayCreate) SetNillableCreatedAt(t *time.Time) *ScreenplayCreate {
	if t != nil {
		sc.SetCreatedAt(*t)
	}
	return sc
}

// SetUpdatedAt sets the "updated_at" field.
func (sc *ScreenplayCreate) SetUpdatedAt(t time.Time) *ScreenplayCreate {
	sc.mutation.SetUpdatedAt(t)
	return sc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (sc *ScreenplayCreate) SetNillableUpdatedAt(t *time.Time) *ScreenplayCreate {
	if t != nil {
		sc.SetUpdatedAt(*t)
	}
	return sc
}

// SetUserID sets the "user_id" field.
func (sc *ScreenplayCreate) SetUserID(i int64) *ScreenplayCreate {
	sc.mutation.SetUserID(i)
	return sc
}

// SetProjectID sets the "project_id" field.
func (sc *ScreenplayCreate) SetProjectID(i int64) *ScreenplayCreate {
	sc.mutation.SetProjectID(i)
	return sc
}

// SetTitle sets the "title" field.
func (sc *ScreenplayCreate) SetTitle(s string) *ScreenplayCreate {
	sc.mutation.SetTitle(s)
	return sc
}

// SetContent sets the "content" field.
func (sc *ScreenplayCreate) SetContent(s string) *ScreenplayCreate {
	sc.mutation.SetContent(s)
	return sc
}

// SetType sets the "type" field.
func (sc *ScreenplayCreate) SetType(i int8) *ScreenplayCreate {
	sc.mutation.SetType(i)
	return sc
}

// SetNillableType sets the "type" field if the given value is not nil.
func (sc *ScreenplayCreate) SetNillableType(i *int8) *ScreenplayCreate {
	if i != nil {
		sc.SetType(*i)
	}
	return sc
}

// SetStatus sets the "status" field.
func (sc *ScreenplayCreate) SetStatus(i int8) *ScreenplayCreate {
	sc.mutation.SetStatus(i)
	return sc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (sc *ScreenplayCreate) SetNillableStatus(i *int8) *ScreenplayCreate {
	if i != nil {
		sc.SetStatus(*i)
	}
	return sc
}

// Mutation returns the ScreenplayMutation object of the builder.
func (sc *ScreenplayCreate) Mutation() *ScreenplayMutation {
	return sc.mutation
}

// Save creates the Screenplay in the database.
func (sc *ScreenplayCreate) Save(ctx context.Context) (*Screenplay, error) {
	sc.defaults()
	return withHooks(ctx, sc.sqlSave, sc.mutation, sc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (sc *ScreenplayCreate) SaveX(ctx context.Context) *Screenplay {
	v, err := sc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (sc *ScreenplayCreate) Exec(ctx context.Context) error {
	_, err := sc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (sc *ScreenplayCreate) ExecX(ctx context.Context) {
	if err := sc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (sc *ScreenplayCreate) defaults() {
	if _, ok := sc.mutation.CreatedAt(); !ok {
		v := screenplay.DefaultCreatedAt()
		sc.mutation.SetCreatedAt(v)
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		v := screenplay.DefaultUpdatedAt()
		sc.mutation.SetUpdatedAt(v)
	}
	if _, ok := sc.mutation.GetType(); !ok {
		v := screenplay.DefaultType
		sc.mutation.SetType(v)
	}
	if _, ok := sc.mutation.Status(); !ok {
		v := screenplay.DefaultStatus
		sc.mutation.SetStatus(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (sc *ScreenplayCreate) check() error {
	if _, ok := sc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Screenplay.created_at"`)}
	}
	if _, ok := sc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Screenplay.updated_at"`)}
	}
	if _, ok := sc.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "Screenplay.user_id"`)}
	}
	if _, ok := sc.mutation.ProjectID(); !ok {
		return &ValidationError{Name: "project_id", err: errors.New(`ent: missing required field "Screenplay.project_id"`)}
	}
	if _, ok := sc.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "Screenplay.title"`)}
	}
	if _, ok := sc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "Screenplay.content"`)}
	}
	if _, ok := sc.mutation.GetType(); !ok {
		return &ValidationError{Name: "type", err: errors.New(`ent: missing required field "Screenplay.type"`)}
	}
	if _, ok := sc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "Screenplay.status"`)}
	}
	return nil
}

func (sc *ScreenplayCreate) sqlSave(ctx context.Context) (*Screenplay, error) {
	if err := sc.check(); err != nil {
		return nil, err
	}
	_node, _spec := sc.createSpec()
	if err := sqlgraph.CreateNode(ctx, sc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	sc.mutation.id = &_node.ID
	sc.mutation.done = true
	return _node, nil
}

func (sc *ScreenplayCreate) createSpec() (*Screenplay, *sqlgraph.CreateSpec) {
	var (
		_node = &Screenplay{config: sc.config}
		_spec = sqlgraph.NewCreateSpec(screenplay.Table, sqlgraph.NewFieldSpec(screenplay.FieldID, field.TypeInt))
	)
	if value, ok := sc.mutation.CreatedAt(); ok {
		_spec.SetField(screenplay.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := sc.mutation.UpdatedAt(); ok {
		_spec.SetField(screenplay.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := sc.mutation.UserID(); ok {
		_spec.SetField(screenplay.FieldUserID, field.TypeInt64, value)
		_node.UserID = value
	}
	if value, ok := sc.mutation.ProjectID(); ok {
		_spec.SetField(screenplay.FieldProjectID, field.TypeInt64, value)
		_node.ProjectID = value
	}
	if value, ok := sc.mutation.Title(); ok {
		_spec.SetField(screenplay.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := sc.mutation.Content(); ok {
		_spec.SetField(screenplay.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	if value, ok := sc.mutation.GetType(); ok {
		_spec.SetField(screenplay.FieldType, field.TypeInt8, value)
		_node.Type = value
	}
	if value, ok := sc.mutation.Status(); ok {
		_spec.SetField(screenplay.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	return _node, _spec
}

// ScreenplayCreateBulk is the builder for creating many Screenplay entities in bulk.
type ScreenplayCreateBulk struct {
	config
	err      error
	builders []*ScreenplayCreate
}

// Save creates the Screenplay entities in the database.
func (scb *ScreenplayCreateBulk) Save(ctx context.Context) ([]*Screenplay, error) {
	if scb.err != nil {
		return nil, scb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(scb.builders))
	nodes := make([]*Screenplay, len(scb.builders))
	mutators := make([]Mutator, len(scb.builders))
	for i := range scb.builders {
		func(i int, root context.Context) {
			builder := scb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ScreenplayMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, scb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, scb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, scb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (scb *ScreenplayCreateBulk) SaveX(ctx context.Context) []*Screenplay {
	v, err := scb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (scb *ScreenplayCreateBulk) Exec(ctx context.Context) error {
	_, err := scb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (scb *ScreenplayCreateBulk) ExecX(ctx context.Context) {
	if err := scb.Exec(ctx); err != nil {
		panic(err)
	}
}
