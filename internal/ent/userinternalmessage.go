// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/internalmessage"
	"bole-ai/internal/ent/user"
	"bole-ai/internal/ent/userinternalmessage"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// 站内信
type UserInternalMessage struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 站内信id
	InternalMessageID int `json:"internal_message_id,omitempty"`
	// 用户id
	UserID int `json:"user_id,omitempty"`
	// 状态:1未读；2 已读
	Status int8 `json:"status,omitempty"`
	// <PERSON>s holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UserInternalMessageQuery when eager-loading is set.
	Edges        UserInternalMessageEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UserInternalMessageEdges holds the relations/edges for other nodes in the graph.
type UserInternalMessageEdges struct {
	// Message holds the value of the message edge.
	Message *InternalMessage `json:"message,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// MessageOrErr returns the Message value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UserInternalMessageEdges) MessageOrErr() (*InternalMessage, error) {
	if e.Message != nil {
		return e.Message, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: internalmessage.Label}
	}
	return nil, &NotLoadedError{edge: "message"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UserInternalMessageEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*UserInternalMessage) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case userinternalmessage.FieldID, userinternalmessage.FieldInternalMessageID, userinternalmessage.FieldUserID, userinternalmessage.FieldStatus:
			values[i] = new(sql.NullInt64)
		case userinternalmessage.FieldCreatedAt, userinternalmessage.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the UserInternalMessage fields.
func (uim *UserInternalMessage) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case userinternalmessage.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			uim.ID = int(value.Int64)
		case userinternalmessage.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				uim.CreatedAt = value.Time
			}
		case userinternalmessage.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				uim.UpdatedAt = value.Time
			}
		case userinternalmessage.FieldInternalMessageID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field internal_message_id", values[i])
			} else if value.Valid {
				uim.InternalMessageID = int(value.Int64)
			}
		case userinternalmessage.FieldUserID:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				uim.UserID = int(value.Int64)
			}
		case userinternalmessage.FieldStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				uim.Status = int8(value.Int64)
			}
		default:
			uim.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the UserInternalMessage.
// This includes values selected through modifiers, order, etc.
func (uim *UserInternalMessage) Value(name string) (ent.Value, error) {
	return uim.selectValues.Get(name)
}

// QueryMessage queries the "message" edge of the UserInternalMessage entity.
func (uim *UserInternalMessage) QueryMessage() *InternalMessageQuery {
	return NewUserInternalMessageClient(uim.config).QueryMessage(uim)
}

// QueryUser queries the "user" edge of the UserInternalMessage entity.
func (uim *UserInternalMessage) QueryUser() *UserQuery {
	return NewUserInternalMessageClient(uim.config).QueryUser(uim)
}

// Update returns a builder for updating this UserInternalMessage.
// Note that you need to call UserInternalMessage.Unwrap() before calling this method if this UserInternalMessage
// was returned from a transaction, and the transaction was committed or rolled back.
func (uim *UserInternalMessage) Update() *UserInternalMessageUpdateOne {
	return NewUserInternalMessageClient(uim.config).UpdateOne(uim)
}

// Unwrap unwraps the UserInternalMessage entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (uim *UserInternalMessage) Unwrap() *UserInternalMessage {
	_tx, ok := uim.config.driver.(*txDriver)
	if !ok {
		panic("ent: UserInternalMessage is not a transactional entity")
	}
	uim.config.driver = _tx.drv
	return uim
}

// String implements the fmt.Stringer.
func (uim *UserInternalMessage) String() string {
	var builder strings.Builder
	builder.WriteString("UserInternalMessage(")
	builder.WriteString(fmt.Sprintf("id=%v, ", uim.ID))
	builder.WriteString("created_at=")
	builder.WriteString(uim.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(uim.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("internal_message_id=")
	builder.WriteString(fmt.Sprintf("%v", uim.InternalMessageID))
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(fmt.Sprintf("%v", uim.UserID))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", uim.Status))
	builder.WriteByte(')')
	return builder.String()
}

// UserInternalMessages is a parsable slice of UserInternalMessage.
type UserInternalMessages []*UserInternalMessage
