// Code generated by ent, DO NOT EDIT.

package testcode

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldUpdatedAt, v))
}

// BatchID applies equality check predicate on the "batch_id" field. It's identical to BatchIDEQ.
func BatchID(v int) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldBatchID, v))
}

// Code applies equality check predicate on the "code" field. It's identical to CodeEQ.
func Code(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldCode, v))
}

// Device applies equality check predicate on the "device" field. It's identical to DeviceEQ.
func Device(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldDevice, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldStatus, v))
}

// ExpiredAt applies equality check predicate on the "expired_at" field. It's identical to ExpiredAtEQ.
func ExpiredAt(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldExpiredAt, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldUpdatedAt, v))
}

// BatchIDEQ applies the EQ predicate on the "batch_id" field.
func BatchIDEQ(v int) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldBatchID, v))
}

// BatchIDNEQ applies the NEQ predicate on the "batch_id" field.
func BatchIDNEQ(v int) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldBatchID, v))
}

// BatchIDIn applies the In predicate on the "batch_id" field.
func BatchIDIn(vs ...int) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldBatchID, vs...))
}

// BatchIDNotIn applies the NotIn predicate on the "batch_id" field.
func BatchIDNotIn(vs ...int) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldBatchID, vs...))
}

// CodeEQ applies the EQ predicate on the "code" field.
func CodeEQ(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldCode, v))
}

// CodeNEQ applies the NEQ predicate on the "code" field.
func CodeNEQ(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldCode, v))
}

// CodeIn applies the In predicate on the "code" field.
func CodeIn(vs ...string) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldCode, vs...))
}

// CodeNotIn applies the NotIn predicate on the "code" field.
func CodeNotIn(vs ...string) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldCode, vs...))
}

// CodeGT applies the GT predicate on the "code" field.
func CodeGT(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldCode, v))
}

// CodeGTE applies the GTE predicate on the "code" field.
func CodeGTE(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldCode, v))
}

// CodeLT applies the LT predicate on the "code" field.
func CodeLT(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldCode, v))
}

// CodeLTE applies the LTE predicate on the "code" field.
func CodeLTE(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldCode, v))
}

// CodeContains applies the Contains predicate on the "code" field.
func CodeContains(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldContains(FieldCode, v))
}

// CodeHasPrefix applies the HasPrefix predicate on the "code" field.
func CodeHasPrefix(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldHasPrefix(FieldCode, v))
}

// CodeHasSuffix applies the HasSuffix predicate on the "code" field.
func CodeHasSuffix(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldHasSuffix(FieldCode, v))
}

// CodeEqualFold applies the EqualFold predicate on the "code" field.
func CodeEqualFold(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEqualFold(FieldCode, v))
}

// CodeContainsFold applies the ContainsFold predicate on the "code" field.
func CodeContainsFold(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldContainsFold(FieldCode, v))
}

// DeviceEQ applies the EQ predicate on the "device" field.
func DeviceEQ(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldDevice, v))
}

// DeviceNEQ applies the NEQ predicate on the "device" field.
func DeviceNEQ(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldDevice, v))
}

// DeviceIn applies the In predicate on the "device" field.
func DeviceIn(vs ...string) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldDevice, vs...))
}

// DeviceNotIn applies the NotIn predicate on the "device" field.
func DeviceNotIn(vs ...string) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldDevice, vs...))
}

// DeviceGT applies the GT predicate on the "device" field.
func DeviceGT(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldDevice, v))
}

// DeviceGTE applies the GTE predicate on the "device" field.
func DeviceGTE(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldDevice, v))
}

// DeviceLT applies the LT predicate on the "device" field.
func DeviceLT(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldDevice, v))
}

// DeviceLTE applies the LTE predicate on the "device" field.
func DeviceLTE(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldDevice, v))
}

// DeviceContains applies the Contains predicate on the "device" field.
func DeviceContains(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldContains(FieldDevice, v))
}

// DeviceHasPrefix applies the HasPrefix predicate on the "device" field.
func DeviceHasPrefix(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldHasPrefix(FieldDevice, v))
}

// DeviceHasSuffix applies the HasSuffix predicate on the "device" field.
func DeviceHasSuffix(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldHasSuffix(FieldDevice, v))
}

// DeviceIsNil applies the IsNil predicate on the "device" field.
func DeviceIsNil() predicate.TestCode {
	return predicate.TestCode(sql.FieldIsNull(FieldDevice))
}

// DeviceNotNil applies the NotNil predicate on the "device" field.
func DeviceNotNil() predicate.TestCode {
	return predicate.TestCode(sql.FieldNotNull(FieldDevice))
}

// DeviceEqualFold applies the EqualFold predicate on the "device" field.
func DeviceEqualFold(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldEqualFold(FieldDevice, v))
}

// DeviceContainsFold applies the ContainsFold predicate on the "device" field.
func DeviceContainsFold(v string) predicate.TestCode {
	return predicate.TestCode(sql.FieldContainsFold(FieldDevice, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldStatus, v))
}

// StatusIn applies the In predicate on the "status" field.
func StatusIn(vs ...int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldStatus, vs...))
}

// StatusNotIn applies the NotIn predicate on the "status" field.
func StatusNotIn(vs ...int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldStatus, vs...))
}

// StatusGT applies the GT predicate on the "status" field.
func StatusGT(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldStatus, v))
}

// StatusGTE applies the GTE predicate on the "status" field.
func StatusGTE(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldStatus, v))
}

// StatusLT applies the LT predicate on the "status" field.
func StatusLT(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldStatus, v))
}

// StatusLTE applies the LTE predicate on the "status" field.
func StatusLTE(v int8) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldStatus, v))
}

// ExpiredAtEQ applies the EQ predicate on the "expired_at" field.
func ExpiredAtEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldEQ(FieldExpiredAt, v))
}

// ExpiredAtNEQ applies the NEQ predicate on the "expired_at" field.
func ExpiredAtNEQ(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNEQ(FieldExpiredAt, v))
}

// ExpiredAtIn applies the In predicate on the "expired_at" field.
func ExpiredAtIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldIn(FieldExpiredAt, vs...))
}

// ExpiredAtNotIn applies the NotIn predicate on the "expired_at" field.
func ExpiredAtNotIn(vs ...time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldNotIn(FieldExpiredAt, vs...))
}

// ExpiredAtGT applies the GT predicate on the "expired_at" field.
func ExpiredAtGT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGT(FieldExpiredAt, v))
}

// ExpiredAtGTE applies the GTE predicate on the "expired_at" field.
func ExpiredAtGTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldGTE(FieldExpiredAt, v))
}

// ExpiredAtLT applies the LT predicate on the "expired_at" field.
func ExpiredAtLT(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLT(FieldExpiredAt, v))
}

// ExpiredAtLTE applies the LTE predicate on the "expired_at" field.
func ExpiredAtLTE(v time.Time) predicate.TestCode {
	return predicate.TestCode(sql.FieldLTE(FieldExpiredAt, v))
}

// ExpiredAtIsNil applies the IsNil predicate on the "expired_at" field.
func ExpiredAtIsNil() predicate.TestCode {
	return predicate.TestCode(sql.FieldIsNull(FieldExpiredAt))
}

// ExpiredAtNotNil applies the NotNil predicate on the "expired_at" field.
func ExpiredAtNotNil() predicate.TestCode {
	return predicate.TestCode(sql.FieldNotNull(FieldExpiredAt))
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.TestCode {
	return predicate.TestCode(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.TestCode {
	return predicate.TestCode(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasBatch applies the HasEdge predicate on the "batch" edge.
func HasBatch() predicate.TestCode {
	return predicate.TestCode(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, BatchTable, BatchColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasBatchWith applies the HasEdge predicate on the "batch" edge with a given conditions (other predicates).
func HasBatchWith(preds ...predicate.Batch) predicate.TestCode {
	return predicate.TestCode(func(s *sql.Selector) {
		step := newBatchStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.TestCode) predicate.TestCode {
	return predicate.TestCode(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.TestCode) predicate.TestCode {
	return predicate.TestCode(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.TestCode) predicate.TestCode {
	return predicate.TestCode(sql.NotPredicates(p))
}
