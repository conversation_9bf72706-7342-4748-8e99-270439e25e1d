// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/frequentaskquestion"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrequentAskQuestionCreate is the builder for creating a FrequentAskQuestion entity.
type FrequentAskQuestionCreate struct {
	config
	mutation *FrequentAskQuestionMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (faqc *FrequentAskQuestionCreate) SetCreatedAt(t time.Time) *FrequentAskQuestionCreate {
	faqc.mutation.SetCreatedAt(t)
	return faqc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (faqc *FrequentAskQuestionCreate) SetNillableCreatedAt(t *time.Time) *FrequentAskQuestionCreate {
	if t != nil {
		faqc.SetCreatedAt(*t)
	}
	return faqc
}

// SetUpdatedAt sets the "updated_at" field.
func (faqc *FrequentAskQuestionCreate) SetUpdatedAt(t time.Time) *FrequentAskQuestionCreate {
	faqc.mutation.SetUpdatedAt(t)
	return faqc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (faqc *FrequentAskQuestionCreate) SetNillableUpdatedAt(t *time.Time) *FrequentAskQuestionCreate {
	if t != nil {
		faqc.SetUpdatedAt(*t)
	}
	return faqc
}

// SetContent sets the "content" field.
func (faqc *FrequentAskQuestionCreate) SetContent(s string) *FrequentAskQuestionCreate {
	faqc.mutation.SetContent(s)
	return faqc
}

// Mutation returns the FrequentAskQuestionMutation object of the builder.
func (faqc *FrequentAskQuestionCreate) Mutation() *FrequentAskQuestionMutation {
	return faqc.mutation
}

// Save creates the FrequentAskQuestion in the database.
func (faqc *FrequentAskQuestionCreate) Save(ctx context.Context) (*FrequentAskQuestion, error) {
	faqc.defaults()
	return withHooks(ctx, faqc.sqlSave, faqc.mutation, faqc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (faqc *FrequentAskQuestionCreate) SaveX(ctx context.Context) *FrequentAskQuestion {
	v, err := faqc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (faqc *FrequentAskQuestionCreate) Exec(ctx context.Context) error {
	_, err := faqc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (faqc *FrequentAskQuestionCreate) ExecX(ctx context.Context) {
	if err := faqc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (faqc *FrequentAskQuestionCreate) defaults() {
	if _, ok := faqc.mutation.CreatedAt(); !ok {
		v := frequentaskquestion.DefaultCreatedAt()
		faqc.mutation.SetCreatedAt(v)
	}
	if _, ok := faqc.mutation.UpdatedAt(); !ok {
		v := frequentaskquestion.DefaultUpdatedAt()
		faqc.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (faqc *FrequentAskQuestionCreate) check() error {
	if _, ok := faqc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "FrequentAskQuestion.created_at"`)}
	}
	if _, ok := faqc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "FrequentAskQuestion.updated_at"`)}
	}
	if _, ok := faqc.mutation.Content(); !ok {
		return &ValidationError{Name: "content", err: errors.New(`ent: missing required field "FrequentAskQuestion.content"`)}
	}
	return nil
}

func (faqc *FrequentAskQuestionCreate) sqlSave(ctx context.Context) (*FrequentAskQuestion, error) {
	if err := faqc.check(); err != nil {
		return nil, err
	}
	_node, _spec := faqc.createSpec()
	if err := sqlgraph.CreateNode(ctx, faqc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	faqc.mutation.id = &_node.ID
	faqc.mutation.done = true
	return _node, nil
}

func (faqc *FrequentAskQuestionCreate) createSpec() (*FrequentAskQuestion, *sqlgraph.CreateSpec) {
	var (
		_node = &FrequentAskQuestion{config: faqc.config}
		_spec = sqlgraph.NewCreateSpec(frequentaskquestion.Table, sqlgraph.NewFieldSpec(frequentaskquestion.FieldID, field.TypeInt))
	)
	if value, ok := faqc.mutation.CreatedAt(); ok {
		_spec.SetField(frequentaskquestion.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := faqc.mutation.UpdatedAt(); ok {
		_spec.SetField(frequentaskquestion.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := faqc.mutation.Content(); ok {
		_spec.SetField(frequentaskquestion.FieldContent, field.TypeString, value)
		_node.Content = value
	}
	return _node, _spec
}

// FrequentAskQuestionCreateBulk is the builder for creating many FrequentAskQuestion entities in bulk.
type FrequentAskQuestionCreateBulk struct {
	config
	err      error
	builders []*FrequentAskQuestionCreate
}

// Save creates the FrequentAskQuestion entities in the database.
func (faqcb *FrequentAskQuestionCreateBulk) Save(ctx context.Context) ([]*FrequentAskQuestion, error) {
	if faqcb.err != nil {
		return nil, faqcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(faqcb.builders))
	nodes := make([]*FrequentAskQuestion, len(faqcb.builders))
	mutators := make([]Mutator, len(faqcb.builders))
	for i := range faqcb.builders {
		func(i int, root context.Context) {
			builder := faqcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FrequentAskQuestionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, faqcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, faqcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, faqcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (faqcb *FrequentAskQuestionCreateBulk) SaveX(ctx context.Context) []*FrequentAskQuestion {
	v, err := faqcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (faqcb *FrequentAskQuestionCreateBulk) Exec(ctx context.Context) error {
	_, err := faqcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (faqcb *FrequentAskQuestionCreateBulk) ExecX(ctx context.Context) {
	if err := faqcb.Exec(ctx); err != nil {
		panic(err)
	}
}
