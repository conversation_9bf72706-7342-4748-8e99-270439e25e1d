// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/frequentaskquestion"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// FrequentAskQuestion is the model entity for the FrequentAskQuestion schema.
type FrequentAskQuestion struct {
	config `json:"-"`
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// CreatedAt holds the value of the "created_at" field.
	CreatedAt time.Time `json:"created_at,omitempty"`
	// UpdatedAt holds the value of the "updated_at" field.
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// 内容
	Content      string `json:"content,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*FrequentAskQuestion) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case frequentaskquestion.FieldID:
			values[i] = new(sql.NullInt64)
		case frequentaskquestion.FieldContent:
			values[i] = new(sql.NullString)
		case frequentaskquestion.FieldCreatedAt, frequentaskquestion.FieldUpdatedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the FrequentAskQuestion fields.
func (faq *FrequentAskQuestion) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case frequentaskquestion.FieldID:
			value, ok := values[i].(*sql.NullInt64)
			if !ok {
				return fmt.Errorf("unexpected type %T for field id", value)
			}
			faq.ID = int(value.Int64)
		case frequentaskquestion.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				faq.CreatedAt = value.Time
			}
		case frequentaskquestion.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				faq.UpdatedAt = value.Time
			}
		case frequentaskquestion.FieldContent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field content", values[i])
			} else if value.Valid {
				faq.Content = value.String
			}
		default:
			faq.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the FrequentAskQuestion.
// This includes values selected through modifiers, order, etc.
func (faq *FrequentAskQuestion) Value(name string) (ent.Value, error) {
	return faq.selectValues.Get(name)
}

// Update returns a builder for updating this FrequentAskQuestion.
// Note that you need to call FrequentAskQuestion.Unwrap() before calling this method if this FrequentAskQuestion
// was returned from a transaction, and the transaction was committed or rolled back.
func (faq *FrequentAskQuestion) Update() *FrequentAskQuestionUpdateOne {
	return NewFrequentAskQuestionClient(faq.config).UpdateOne(faq)
}

// Unwrap unwraps the FrequentAskQuestion entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (faq *FrequentAskQuestion) Unwrap() *FrequentAskQuestion {
	_tx, ok := faq.config.driver.(*txDriver)
	if !ok {
		panic("ent: FrequentAskQuestion is not a transactional entity")
	}
	faq.config.driver = _tx.drv
	return faq
}

// String implements the fmt.Stringer.
func (faq *FrequentAskQuestion) String() string {
	var builder strings.Builder
	builder.WriteString("FrequentAskQuestion(")
	builder.WriteString(fmt.Sprintf("id=%v, ", faq.ID))
	builder.WriteString("created_at=")
	builder.WriteString(faq.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(faq.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("content=")
	builder.WriteString(faq.Content)
	builder.WriteByte(')')
	return builder.String()
}

// FrequentAskQuestions is a parsable slice of FrequentAskQuestion.
type FrequentAskQuestions []*FrequentAskQuestion
