// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/adminuser"
	"bole-ai/internal/ent/predicate"
	"context"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// AdminUserDelete is the builder for deleting a AdminUser entity.
type AdminUserDelete struct {
	config
	hooks    []Hook
	mutation *AdminUserMutation
}

// Where appends a list predicates to the AdminUserDelete builder.
func (aud *AdminUserDelete) Where(ps ...predicate.AdminUser) *AdminUserDelete {
	aud.mutation.Where(ps...)
	return aud
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (aud *AdminUserDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, aud.sqlExec, aud.mutation, aud.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (aud *AdminUserDelete) ExecX(ctx context.Context) int {
	n, err := aud.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (aud *AdminUserDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(adminuser.Table, sqlgraph.NewFieldSpec(adminuser.FieldID, field.TypeInt))
	if ps := aud.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, aud.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	aud.mutation.done = true
	return affected, err
}

// AdminUserDeleteOne is the builder for deleting a single AdminUser entity.
type AdminUserDeleteOne struct {
	aud *AdminUserDelete
}

// Where appends a list predicates to the AdminUserDelete builder.
func (audo *AdminUserDeleteOne) Where(ps ...predicate.AdminUser) *AdminUserDeleteOne {
	audo.aud.mutation.Where(ps...)
	return audo
}

// Exec executes the deletion query.
func (audo *AdminUserDeleteOne) Exec(ctx context.Context) error {
	n, err := audo.aud.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{adminuser.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (audo *AdminUserDeleteOne) ExecX(ctx context.Context) {
	if err := audo.Exec(ctx); err != nil {
		panic(err)
	}
}
