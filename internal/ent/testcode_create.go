// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCodeCreate is the builder for creating a TestCode entity.
type TestCodeCreate struct {
	config
	mutation *TestCodeMutation
	hooks    []Hook
}

// SetCreatedAt sets the "created_at" field.
func (tcc *TestCodeCreate) SetCreatedAt(t time.Time) *TestCodeCreate {
	tcc.mutation.SetCreatedAt(t)
	return tcc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableCreatedAt(t *time.Time) *TestCodeCreate {
	if t != nil {
		tcc.SetCreatedAt(*t)
	}
	return tcc
}

// SetUpdatedAt sets the "updated_at" field.
func (tcc *TestCodeCreate) SetUpdatedAt(t time.Time) *TestCodeCreate {
	tcc.mutation.SetUpdatedAt(t)
	return tcc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableUpdatedAt(t *time.Time) *TestCodeCreate {
	if t != nil {
		tcc.SetUpdatedAt(*t)
	}
	return tcc
}

// SetBatchID sets the "batch_id" field.
func (tcc *TestCodeCreate) SetBatchID(i int) *TestCodeCreate {
	tcc.mutation.SetBatchID(i)
	return tcc
}

// SetCode sets the "code" field.
func (tcc *TestCodeCreate) SetCode(s string) *TestCodeCreate {
	tcc.mutation.SetCode(s)
	return tcc
}

// SetDevice sets the "device" field.
func (tcc *TestCodeCreate) SetDevice(s string) *TestCodeCreate {
	tcc.mutation.SetDevice(s)
	return tcc
}

// SetNillableDevice sets the "device" field if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableDevice(s *string) *TestCodeCreate {
	if s != nil {
		tcc.SetDevice(*s)
	}
	return tcc
}

// SetStatus sets the "status" field.
func (tcc *TestCodeCreate) SetStatus(i int8) *TestCodeCreate {
	tcc.mutation.SetStatus(i)
	return tcc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableStatus(i *int8) *TestCodeCreate {
	if i != nil {
		tcc.SetStatus(*i)
	}
	return tcc
}

// SetExpiredAt sets the "expired_at" field.
func (tcc *TestCodeCreate) SetExpiredAt(t time.Time) *TestCodeCreate {
	tcc.mutation.SetExpiredAt(t)
	return tcc
}

// SetNillableExpiredAt sets the "expired_at" field if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableExpiredAt(t *time.Time) *TestCodeCreate {
	if t != nil {
		tcc.SetExpiredAt(*t)
	}
	return tcc
}

// SetUserID sets the "user" edge to the User entity by ID.
func (tcc *TestCodeCreate) SetUserID(id int) *TestCodeCreate {
	tcc.mutation.SetUserID(id)
	return tcc
}

// SetNillableUserID sets the "user" edge to the User entity by ID if the given value is not nil.
func (tcc *TestCodeCreate) SetNillableUserID(id *int) *TestCodeCreate {
	if id != nil {
		tcc = tcc.SetUserID(*id)
	}
	return tcc
}

// SetUser sets the "user" edge to the User entity.
func (tcc *TestCodeCreate) SetUser(u *User) *TestCodeCreate {
	return tcc.SetUserID(u.ID)
}

// SetBatch sets the "batch" edge to the Batch entity.
func (tcc *TestCodeCreate) SetBatch(b *Batch) *TestCodeCreate {
	return tcc.SetBatchID(b.ID)
}

// Mutation returns the TestCodeMutation object of the builder.
func (tcc *TestCodeCreate) Mutation() *TestCodeMutation {
	return tcc.mutation
}

// Save creates the TestCode in the database.
func (tcc *TestCodeCreate) Save(ctx context.Context) (*TestCode, error) {
	tcc.defaults()
	return withHooks(ctx, tcc.sqlSave, tcc.mutation, tcc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tcc *TestCodeCreate) SaveX(ctx context.Context) *TestCode {
	v, err := tcc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcc *TestCodeCreate) Exec(ctx context.Context) error {
	_, err := tcc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcc *TestCodeCreate) ExecX(ctx context.Context) {
	if err := tcc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tcc *TestCodeCreate) defaults() {
	if _, ok := tcc.mutation.CreatedAt(); !ok {
		v := testcode.DefaultCreatedAt()
		tcc.mutation.SetCreatedAt(v)
	}
	if _, ok := tcc.mutation.UpdatedAt(); !ok {
		v := testcode.DefaultUpdatedAt()
		tcc.mutation.SetUpdatedAt(v)
	}
	if _, ok := tcc.mutation.Status(); !ok {
		v := testcode.DefaultStatus
		tcc.mutation.SetStatus(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tcc *TestCodeCreate) check() error {
	if _, ok := tcc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "TestCode.created_at"`)}
	}
	if _, ok := tcc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "TestCode.updated_at"`)}
	}
	if _, ok := tcc.mutation.BatchID(); !ok {
		return &ValidationError{Name: "batch_id", err: errors.New(`ent: missing required field "TestCode.batch_id"`)}
	}
	if _, ok := tcc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "TestCode.code"`)}
	}
	if _, ok := tcc.mutation.Status(); !ok {
		return &ValidationError{Name: "status", err: errors.New(`ent: missing required field "TestCode.status"`)}
	}
	if len(tcc.mutation.BatchIDs()) == 0 {
		return &ValidationError{Name: "batch", err: errors.New(`ent: missing required edge "TestCode.batch"`)}
	}
	return nil
}

func (tcc *TestCodeCreate) sqlSave(ctx context.Context) (*TestCode, error) {
	if err := tcc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tcc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tcc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	id := _spec.ID.Value.(int64)
	_node.ID = int(id)
	tcc.mutation.id = &_node.ID
	tcc.mutation.done = true
	return _node, nil
}

func (tcc *TestCodeCreate) createSpec() (*TestCode, *sqlgraph.CreateSpec) {
	var (
		_node = &TestCode{config: tcc.config}
		_spec = sqlgraph.NewCreateSpec(testcode.Table, sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt))
	)
	if value, ok := tcc.mutation.CreatedAt(); ok {
		_spec.SetField(testcode.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tcc.mutation.UpdatedAt(); ok {
		_spec.SetField(testcode.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tcc.mutation.Code(); ok {
		_spec.SetField(testcode.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := tcc.mutation.Device(); ok {
		_spec.SetField(testcode.FieldDevice, field.TypeString, value)
		_node.Device = value
	}
	if value, ok := tcc.mutation.Status(); ok {
		_spec.SetField(testcode.FieldStatus, field.TypeInt8, value)
		_node.Status = value
	}
	if value, ok := tcc.mutation.ExpiredAt(); ok {
		_spec.SetField(testcode.FieldExpiredAt, field.TypeTime, value)
		_node.ExpiredAt = value
	}
	if nodes := tcc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2O,
			Inverse: false,
			Table:   testcode.UserTable,
			Columns: []string{testcode.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tcc.mutation.BatchIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   testcode.BatchTable,
			Columns: []string{testcode.BatchColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(batch.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.BatchID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// TestCodeCreateBulk is the builder for creating many TestCode entities in bulk.
type TestCodeCreateBulk struct {
	config
	err      error
	builders []*TestCodeCreate
}

// Save creates the TestCode entities in the database.
func (tccb *TestCodeCreateBulk) Save(ctx context.Context) ([]*TestCode, error) {
	if tccb.err != nil {
		return nil, tccb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tccb.builders))
	nodes := make([]*TestCode, len(tccb.builders))
	mutators := make([]Mutator, len(tccb.builders))
	for i := range tccb.builders {
		func(i int, root context.Context) {
			builder := tccb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TestCodeMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tccb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tccb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				if specs[i].ID.Value != nil {
					id := specs[i].ID.Value.(int64)
					nodes[i].ID = int(id)
				}
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tccb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tccb *TestCodeCreateBulk) SaveX(ctx context.Context) []*TestCode {
	v, err := tccb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tccb *TestCodeCreateBulk) Exec(ctx context.Context) error {
	_, err := tccb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tccb *TestCodeCreateBulk) ExecX(ctx context.Context) {
	if err := tccb.Exec(ctx); err != nil {
		panic(err)
	}
}
