// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/batch"
	"bole-ai/internal/ent/predicate"
	"bole-ai/internal/ent/testcode"
	"bole-ai/internal/ent/user"
	"context"
	"database/sql/driver"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TestCodeQuery is the builder for querying TestCode entities.
type TestCodeQuery struct {
	config
	ctx        *QueryContext
	order      []testcode.OrderOption
	inters     []Interceptor
	predicates []predicate.TestCode
	withUser   *UserQuery
	withBatch  *BatchQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the TestCodeQuery builder.
func (tcq *TestCodeQuery) Where(ps ...predicate.TestCode) *TestCodeQuery {
	tcq.predicates = append(tcq.predicates, ps...)
	return tcq
}

// Limit the number of records to be returned by this query.
func (tcq *TestCodeQuery) Limit(limit int) *TestCodeQuery {
	tcq.ctx.Limit = &limit
	return tcq
}

// Offset to start from.
func (tcq *TestCodeQuery) Offset(offset int) *TestCodeQuery {
	tcq.ctx.Offset = &offset
	return tcq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (tcq *TestCodeQuery) Unique(unique bool) *TestCodeQuery {
	tcq.ctx.Unique = &unique
	return tcq
}

// Order specifies how the records should be ordered.
func (tcq *TestCodeQuery) Order(o ...testcode.OrderOption) *TestCodeQuery {
	tcq.order = append(tcq.order, o...)
	return tcq
}

// QueryUser chains the current query on the "user" edge.
func (tcq *TestCodeQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: tcq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := tcq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := tcq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(testcode.Table, testcode.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.O2O, false, testcode.UserTable, testcode.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(tcq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryBatch chains the current query on the "batch" edge.
func (tcq *TestCodeQuery) QueryBatch() *BatchQuery {
	query := (&BatchClient{config: tcq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := tcq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := tcq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(testcode.Table, testcode.FieldID, selector),
			sqlgraph.To(batch.Table, batch.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, testcode.BatchTable, testcode.BatchColumn),
		)
		fromU = sqlgraph.SetNeighbors(tcq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first TestCode entity from the query.
// Returns a *NotFoundError when no TestCode was found.
func (tcq *TestCodeQuery) First(ctx context.Context) (*TestCode, error) {
	nodes, err := tcq.Limit(1).All(setContextOp(ctx, tcq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{testcode.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (tcq *TestCodeQuery) FirstX(ctx context.Context) *TestCode {
	node, err := tcq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first TestCode ID from the query.
// Returns a *NotFoundError when no TestCode ID was found.
func (tcq *TestCodeQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = tcq.Limit(1).IDs(setContextOp(ctx, tcq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{testcode.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (tcq *TestCodeQuery) FirstIDX(ctx context.Context) int {
	id, err := tcq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single TestCode entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one TestCode entity is found.
// Returns a *NotFoundError when no TestCode entities are found.
func (tcq *TestCodeQuery) Only(ctx context.Context) (*TestCode, error) {
	nodes, err := tcq.Limit(2).All(setContextOp(ctx, tcq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{testcode.Label}
	default:
		return nil, &NotSingularError{testcode.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (tcq *TestCodeQuery) OnlyX(ctx context.Context) *TestCode {
	node, err := tcq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only TestCode ID in the query.
// Returns a *NotSingularError when more than one TestCode ID is found.
// Returns a *NotFoundError when no entities are found.
func (tcq *TestCodeQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = tcq.Limit(2).IDs(setContextOp(ctx, tcq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{testcode.Label}
	default:
		err = &NotSingularError{testcode.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (tcq *TestCodeQuery) OnlyIDX(ctx context.Context) int {
	id, err := tcq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of TestCodes.
func (tcq *TestCodeQuery) All(ctx context.Context) ([]*TestCode, error) {
	ctx = setContextOp(ctx, tcq.ctx, ent.OpQueryAll)
	if err := tcq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*TestCode, *TestCodeQuery]()
	return withInterceptors[[]*TestCode](ctx, tcq, qr, tcq.inters)
}

// AllX is like All, but panics if an error occurs.
func (tcq *TestCodeQuery) AllX(ctx context.Context) []*TestCode {
	nodes, err := tcq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of TestCode IDs.
func (tcq *TestCodeQuery) IDs(ctx context.Context) (ids []int, err error) {
	if tcq.ctx.Unique == nil && tcq.path != nil {
		tcq.Unique(true)
	}
	ctx = setContextOp(ctx, tcq.ctx, ent.OpQueryIDs)
	if err = tcq.Select(testcode.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (tcq *TestCodeQuery) IDsX(ctx context.Context) []int {
	ids, err := tcq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (tcq *TestCodeQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, tcq.ctx, ent.OpQueryCount)
	if err := tcq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, tcq, querierCount[*TestCodeQuery](), tcq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (tcq *TestCodeQuery) CountX(ctx context.Context) int {
	count, err := tcq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (tcq *TestCodeQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, tcq.ctx, ent.OpQueryExist)
	switch _, err := tcq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (tcq *TestCodeQuery) ExistX(ctx context.Context) bool {
	exist, err := tcq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the TestCodeQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (tcq *TestCodeQuery) Clone() *TestCodeQuery {
	if tcq == nil {
		return nil
	}
	return &TestCodeQuery{
		config:     tcq.config,
		ctx:        tcq.ctx.Clone(),
		order:      append([]testcode.OrderOption{}, tcq.order...),
		inters:     append([]Interceptor{}, tcq.inters...),
		predicates: append([]predicate.TestCode{}, tcq.predicates...),
		withUser:   tcq.withUser.Clone(),
		withBatch:  tcq.withBatch.Clone(),
		// clone intermediate query.
		sql:  tcq.sql.Clone(),
		path: tcq.path,
	}
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (tcq *TestCodeQuery) WithUser(opts ...func(*UserQuery)) *TestCodeQuery {
	query := (&UserClient{config: tcq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	tcq.withUser = query
	return tcq
}

// WithBatch tells the query-builder to eager-load the nodes that are connected to
// the "batch" edge. The optional arguments are used to configure the query builder of the edge.
func (tcq *TestCodeQuery) WithBatch(opts ...func(*BatchQuery)) *TestCodeQuery {
	query := (&BatchClient{config: tcq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	tcq.withBatch = query
	return tcq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.TestCode.Query().
//		GroupBy(testcode.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (tcq *TestCodeQuery) GroupBy(field string, fields ...string) *TestCodeGroupBy {
	tcq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &TestCodeGroupBy{build: tcq}
	grbuild.flds = &tcq.ctx.Fields
	grbuild.label = testcode.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.TestCode.Query().
//		Select(testcode.FieldCreatedAt).
//		Scan(ctx, &v)
func (tcq *TestCodeQuery) Select(fields ...string) *TestCodeSelect {
	tcq.ctx.Fields = append(tcq.ctx.Fields, fields...)
	sbuild := &TestCodeSelect{TestCodeQuery: tcq}
	sbuild.label = testcode.Label
	sbuild.flds, sbuild.scan = &tcq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a TestCodeSelect configured with the given aggregations.
func (tcq *TestCodeQuery) Aggregate(fns ...AggregateFunc) *TestCodeSelect {
	return tcq.Select().Aggregate(fns...)
}

func (tcq *TestCodeQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range tcq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, tcq); err != nil {
				return err
			}
		}
	}
	for _, f := range tcq.ctx.Fields {
		if !testcode.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if tcq.path != nil {
		prev, err := tcq.path(ctx)
		if err != nil {
			return err
		}
		tcq.sql = prev
	}
	return nil
}

func (tcq *TestCodeQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*TestCode, error) {
	var (
		nodes       = []*TestCode{}
		_spec       = tcq.querySpec()
		loadedTypes = [2]bool{
			tcq.withUser != nil,
			tcq.withBatch != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*TestCode).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &TestCode{config: tcq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, tcq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := tcq.withUser; query != nil {
		if err := tcq.loadUser(ctx, query, nodes, nil,
			func(n *TestCode, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	if query := tcq.withBatch; query != nil {
		if err := tcq.loadBatch(ctx, query, nodes, nil,
			func(n *TestCode, e *Batch) { n.Edges.Batch = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (tcq *TestCodeQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*TestCode, init func(*TestCode), assign func(*TestCode, *User)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[int]*TestCode)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(user.FieldTestCodeID)
	}
	query.Where(predicate.User(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(testcode.UserColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.TestCodeID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "test_code_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}
func (tcq *TestCodeQuery) loadBatch(ctx context.Context, query *BatchQuery, nodes []*TestCode, init func(*TestCode), assign func(*TestCode, *Batch)) error {
	ids := make([]int, 0, len(nodes))
	nodeids := make(map[int][]*TestCode)
	for i := range nodes {
		fk := nodes[i].BatchID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(batch.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "batch_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (tcq *TestCodeQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := tcq.querySpec()
	_spec.Node.Columns = tcq.ctx.Fields
	if len(tcq.ctx.Fields) > 0 {
		_spec.Unique = tcq.ctx.Unique != nil && *tcq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, tcq.driver, _spec)
}

func (tcq *TestCodeQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(testcode.Table, testcode.Columns, sqlgraph.NewFieldSpec(testcode.FieldID, field.TypeInt))
	_spec.From = tcq.sql
	if unique := tcq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if tcq.path != nil {
		_spec.Unique = true
	}
	if fields := tcq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, testcode.FieldID)
		for i := range fields {
			if fields[i] != testcode.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if tcq.withBatch != nil {
			_spec.Node.AddColumnOnce(testcode.FieldBatchID)
		}
	}
	if ps := tcq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := tcq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := tcq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := tcq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (tcq *TestCodeQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(tcq.driver.Dialect())
	t1 := builder.Table(testcode.Table)
	columns := tcq.ctx.Fields
	if len(columns) == 0 {
		columns = testcode.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if tcq.sql != nil {
		selector = tcq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if tcq.ctx.Unique != nil && *tcq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range tcq.predicates {
		p(selector)
	}
	for _, p := range tcq.order {
		p(selector)
	}
	if offset := tcq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := tcq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// TestCodeGroupBy is the group-by builder for TestCode entities.
type TestCodeGroupBy struct {
	selector
	build *TestCodeQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (tcgb *TestCodeGroupBy) Aggregate(fns ...AggregateFunc) *TestCodeGroupBy {
	tcgb.fns = append(tcgb.fns, fns...)
	return tcgb
}

// Scan applies the selector query and scans the result into the given value.
func (tcgb *TestCodeGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tcgb.build.ctx, ent.OpQueryGroupBy)
	if err := tcgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TestCodeQuery, *TestCodeGroupBy](ctx, tcgb.build, tcgb, tcgb.build.inters, v)
}

func (tcgb *TestCodeGroupBy) sqlScan(ctx context.Context, root *TestCodeQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(tcgb.fns))
	for _, fn := range tcgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*tcgb.flds)+len(tcgb.fns))
		for _, f := range *tcgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*tcgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tcgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// TestCodeSelect is the builder for selecting fields of TestCode entities.
type TestCodeSelect struct {
	*TestCodeQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (tcs *TestCodeSelect) Aggregate(fns ...AggregateFunc) *TestCodeSelect {
	tcs.fns = append(tcs.fns, fns...)
	return tcs
}

// Scan applies the selector query and scans the result into the given value.
func (tcs *TestCodeSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tcs.ctx, ent.OpQuerySelect)
	if err := tcs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TestCodeQuery, *TestCodeSelect](ctx, tcs.TestCodeQuery, tcs, tcs.inters, v)
}

func (tcs *TestCodeSelect) sqlScan(ctx context.Context, root *TestCodeQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(tcs.fns))
	for _, fn := range tcs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*tcs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tcs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
