// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/frequentaskquestion"
	"bole-ai/internal/ent/predicate"
	"context"
	"fmt"
	"math"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FrequentAskQuestionQuery is the builder for querying FrequentAskQuestion entities.
type FrequentAskQuestionQuery struct {
	config
	ctx        *QueryContext
	order      []frequentaskquestion.OrderOption
	inters     []Interceptor
	predicates []predicate.FrequentAskQuestion
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the FrequentAskQuestionQuery builder.
func (faqq *FrequentAskQuestionQuery) Where(ps ...predicate.FrequentAskQuestion) *FrequentAskQuestionQuery {
	faqq.predicates = append(faqq.predicates, ps...)
	return faqq
}

// Limit the number of records to be returned by this query.
func (faqq *FrequentAskQuestionQuery) Limit(limit int) *FrequentAskQuestionQuery {
	faqq.ctx.Limit = &limit
	return faqq
}

// Offset to start from.
func (faqq *FrequentAskQuestionQuery) Offset(offset int) *FrequentAskQuestionQuery {
	faqq.ctx.Offset = &offset
	return faqq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (faqq *FrequentAskQuestionQuery) Unique(unique bool) *FrequentAskQuestionQuery {
	faqq.ctx.Unique = &unique
	return faqq
}

// Order specifies how the records should be ordered.
func (faqq *FrequentAskQuestionQuery) Order(o ...frequentaskquestion.OrderOption) *FrequentAskQuestionQuery {
	faqq.order = append(faqq.order, o...)
	return faqq
}

// First returns the first FrequentAskQuestion entity from the query.
// Returns a *NotFoundError when no FrequentAskQuestion was found.
func (faqq *FrequentAskQuestionQuery) First(ctx context.Context) (*FrequentAskQuestion, error) {
	nodes, err := faqq.Limit(1).All(setContextOp(ctx, faqq.ctx, ent.OpQueryFirst))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{frequentaskquestion.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) FirstX(ctx context.Context) *FrequentAskQuestion {
	node, err := faqq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first FrequentAskQuestion ID from the query.
// Returns a *NotFoundError when no FrequentAskQuestion ID was found.
func (faqq *FrequentAskQuestionQuery) FirstID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = faqq.Limit(1).IDs(setContextOp(ctx, faqq.ctx, ent.OpQueryFirstID)); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{frequentaskquestion.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) FirstIDX(ctx context.Context) int {
	id, err := faqq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single FrequentAskQuestion entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one FrequentAskQuestion entity is found.
// Returns a *NotFoundError when no FrequentAskQuestion entities are found.
func (faqq *FrequentAskQuestionQuery) Only(ctx context.Context) (*FrequentAskQuestion, error) {
	nodes, err := faqq.Limit(2).All(setContextOp(ctx, faqq.ctx, ent.OpQueryOnly))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{frequentaskquestion.Label}
	default:
		return nil, &NotSingularError{frequentaskquestion.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) OnlyX(ctx context.Context) *FrequentAskQuestion {
	node, err := faqq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only FrequentAskQuestion ID in the query.
// Returns a *NotSingularError when more than one FrequentAskQuestion ID is found.
// Returns a *NotFoundError when no entities are found.
func (faqq *FrequentAskQuestionQuery) OnlyID(ctx context.Context) (id int, err error) {
	var ids []int
	if ids, err = faqq.Limit(2).IDs(setContextOp(ctx, faqq.ctx, ent.OpQueryOnlyID)); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{frequentaskquestion.Label}
	default:
		err = &NotSingularError{frequentaskquestion.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) OnlyIDX(ctx context.Context) int {
	id, err := faqq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of FrequentAskQuestions.
func (faqq *FrequentAskQuestionQuery) All(ctx context.Context) ([]*FrequentAskQuestion, error) {
	ctx = setContextOp(ctx, faqq.ctx, ent.OpQueryAll)
	if err := faqq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*FrequentAskQuestion, *FrequentAskQuestionQuery]()
	return withInterceptors[[]*FrequentAskQuestion](ctx, faqq, qr, faqq.inters)
}

// AllX is like All, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) AllX(ctx context.Context) []*FrequentAskQuestion {
	nodes, err := faqq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of FrequentAskQuestion IDs.
func (faqq *FrequentAskQuestionQuery) IDs(ctx context.Context) (ids []int, err error) {
	if faqq.ctx.Unique == nil && faqq.path != nil {
		faqq.Unique(true)
	}
	ctx = setContextOp(ctx, faqq.ctx, ent.OpQueryIDs)
	if err = faqq.Select(frequentaskquestion.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) IDsX(ctx context.Context) []int {
	ids, err := faqq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (faqq *FrequentAskQuestionQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, faqq.ctx, ent.OpQueryCount)
	if err := faqq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, faqq, querierCount[*FrequentAskQuestionQuery](), faqq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) CountX(ctx context.Context) int {
	count, err := faqq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (faqq *FrequentAskQuestionQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, faqq.ctx, ent.OpQueryExist)
	switch _, err := faqq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (faqq *FrequentAskQuestionQuery) ExistX(ctx context.Context) bool {
	exist, err := faqq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the FrequentAskQuestionQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (faqq *FrequentAskQuestionQuery) Clone() *FrequentAskQuestionQuery {
	if faqq == nil {
		return nil
	}
	return &FrequentAskQuestionQuery{
		config:     faqq.config,
		ctx:        faqq.ctx.Clone(),
		order:      append([]frequentaskquestion.OrderOption{}, faqq.order...),
		inters:     append([]Interceptor{}, faqq.inters...),
		predicates: append([]predicate.FrequentAskQuestion{}, faqq.predicates...),
		// clone intermediate query.
		sql:  faqq.sql.Clone(),
		path: faqq.path,
	}
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.FrequentAskQuestion.Query().
//		GroupBy(frequentaskquestion.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (faqq *FrequentAskQuestionQuery) GroupBy(field string, fields ...string) *FrequentAskQuestionGroupBy {
	faqq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &FrequentAskQuestionGroupBy{build: faqq}
	grbuild.flds = &faqq.ctx.Fields
	grbuild.label = frequentaskquestion.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.FrequentAskQuestion.Query().
//		Select(frequentaskquestion.FieldCreatedAt).
//		Scan(ctx, &v)
func (faqq *FrequentAskQuestionQuery) Select(fields ...string) *FrequentAskQuestionSelect {
	faqq.ctx.Fields = append(faqq.ctx.Fields, fields...)
	sbuild := &FrequentAskQuestionSelect{FrequentAskQuestionQuery: faqq}
	sbuild.label = frequentaskquestion.Label
	sbuild.flds, sbuild.scan = &faqq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a FrequentAskQuestionSelect configured with the given aggregations.
func (faqq *FrequentAskQuestionQuery) Aggregate(fns ...AggregateFunc) *FrequentAskQuestionSelect {
	return faqq.Select().Aggregate(fns...)
}

func (faqq *FrequentAskQuestionQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range faqq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, faqq); err != nil {
				return err
			}
		}
	}
	for _, f := range faqq.ctx.Fields {
		if !frequentaskquestion.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if faqq.path != nil {
		prev, err := faqq.path(ctx)
		if err != nil {
			return err
		}
		faqq.sql = prev
	}
	return nil
}

func (faqq *FrequentAskQuestionQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*FrequentAskQuestion, error) {
	var (
		nodes = []*FrequentAskQuestion{}
		_spec = faqq.querySpec()
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*FrequentAskQuestion).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &FrequentAskQuestion{config: faqq.config}
		nodes = append(nodes, node)
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, faqq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	return nodes, nil
}

func (faqq *FrequentAskQuestionQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := faqq.querySpec()
	_spec.Node.Columns = faqq.ctx.Fields
	if len(faqq.ctx.Fields) > 0 {
		_spec.Unique = faqq.ctx.Unique != nil && *faqq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, faqq.driver, _spec)
}

func (faqq *FrequentAskQuestionQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(frequentaskquestion.Table, frequentaskquestion.Columns, sqlgraph.NewFieldSpec(frequentaskquestion.FieldID, field.TypeInt))
	_spec.From = faqq.sql
	if unique := faqq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if faqq.path != nil {
		_spec.Unique = true
	}
	if fields := faqq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, frequentaskquestion.FieldID)
		for i := range fields {
			if fields[i] != frequentaskquestion.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
	}
	if ps := faqq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := faqq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := faqq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := faqq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (faqq *FrequentAskQuestionQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(faqq.driver.Dialect())
	t1 := builder.Table(frequentaskquestion.Table)
	columns := faqq.ctx.Fields
	if len(columns) == 0 {
		columns = frequentaskquestion.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if faqq.sql != nil {
		selector = faqq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if faqq.ctx.Unique != nil && *faqq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range faqq.predicates {
		p(selector)
	}
	for _, p := range faqq.order {
		p(selector)
	}
	if offset := faqq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := faqq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// FrequentAskQuestionGroupBy is the group-by builder for FrequentAskQuestion entities.
type FrequentAskQuestionGroupBy struct {
	selector
	build *FrequentAskQuestionQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (faqgb *FrequentAskQuestionGroupBy) Aggregate(fns ...AggregateFunc) *FrequentAskQuestionGroupBy {
	faqgb.fns = append(faqgb.fns, fns...)
	return faqgb
}

// Scan applies the selector query and scans the result into the given value.
func (faqgb *FrequentAskQuestionGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, faqgb.build.ctx, ent.OpQueryGroupBy)
	if err := faqgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FrequentAskQuestionQuery, *FrequentAskQuestionGroupBy](ctx, faqgb.build, faqgb, faqgb.build.inters, v)
}

func (faqgb *FrequentAskQuestionGroupBy) sqlScan(ctx context.Context, root *FrequentAskQuestionQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(faqgb.fns))
	for _, fn := range faqgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*faqgb.flds)+len(faqgb.fns))
		for _, f := range *faqgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*faqgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := faqgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// FrequentAskQuestionSelect is the builder for selecting fields of FrequentAskQuestion entities.
type FrequentAskQuestionSelect struct {
	*FrequentAskQuestionQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (faqs *FrequentAskQuestionSelect) Aggregate(fns ...AggregateFunc) *FrequentAskQuestionSelect {
	faqs.fns = append(faqs.fns, fns...)
	return faqs
}

// Scan applies the selector query and scans the result into the given value.
func (faqs *FrequentAskQuestionSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, faqs.ctx, ent.OpQuerySelect)
	if err := faqs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*FrequentAskQuestionQuery, *FrequentAskQuestionSelect](ctx, faqs.FrequentAskQuestionQuery, faqs, faqs.inters, v)
}

func (faqs *FrequentAskQuestionSelect) sqlScan(ctx context.Context, root *FrequentAskQuestionQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(faqs.fns))
	for _, fn := range faqs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*faqs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := faqs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
