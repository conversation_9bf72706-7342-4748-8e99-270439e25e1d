// Code generated by ent, DO NOT EDIT.

package ent

import (
	"bole-ai/internal/ent/conversation"
	"bole-ai/internal/ent/conversationcontent"
	"bole-ai/internal/ent/predicate"
	"context"
	"errors"
	"fmt"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ConversationContentUpdate is the builder for updating ConversationContent entities.
type ConversationContentUpdate struct {
	config
	hooks    []Hook
	mutation *ConversationContentMutation
}

// Where appends a list predicates to the ConversationContentUpdate builder.
func (ccu *ConversationContentUpdate) Where(ps ...predicate.ConversationContent) *ConversationContentUpdate {
	ccu.mutation.Where(ps...)
	return ccu
}

// SetUpdatedAt sets the "updated_at" field.
func (ccu *ConversationContentUpdate) SetUpdatedAt(t time.Time) *ConversationContentUpdate {
	ccu.mutation.SetUpdatedAt(t)
	return ccu
}

// SetConversationID sets the "conversation_id" field.
func (ccu *ConversationContentUpdate) SetConversationID(i int) *ConversationContentUpdate {
	ccu.mutation.SetConversationID(i)
	return ccu
}

// SetNillableConversationID sets the "conversation_id" field if the given value is not nil.
func (ccu *ConversationContentUpdate) SetNillableConversationID(i *int) *ConversationContentUpdate {
	if i != nil {
		ccu.SetConversationID(*i)
	}
	return ccu
}

// SetContent sets the "content" field.
func (ccu *ConversationContentUpdate) SetContent(s string) *ConversationContentUpdate {
	ccu.mutation.SetContent(s)
	return ccu
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (ccu *ConversationContentUpdate) SetNillableContent(s *string) *ConversationContentUpdate {
	if s != nil {
		ccu.SetContent(*s)
	}
	return ccu
}

// SetType sets the "type" field.
func (ccu *ConversationContentUpdate) SetType(i int8) *ConversationContentUpdate {
	ccu.mutation.ResetType()
	ccu.mutation.SetType(i)
	return ccu
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ccu *ConversationContentUpdate) SetNillableType(i *int8) *ConversationContentUpdate {
	if i != nil {
		ccu.SetType(*i)
	}
	return ccu
}

// AddType adds i to the "type" field.
func (ccu *ConversationContentUpdate) AddType(i int8) *ConversationContentUpdate {
	ccu.mutation.AddType(i)
	return ccu
}

// SetConversation sets the "conversation" edge to the Conversation entity.
func (ccu *ConversationContentUpdate) SetConversation(c *Conversation) *ConversationContentUpdate {
	return ccu.SetConversationID(c.ID)
}

// Mutation returns the ConversationContentMutation object of the builder.
func (ccu *ConversationContentUpdate) Mutation() *ConversationContentMutation {
	return ccu.mutation
}

// ClearConversation clears the "conversation" edge to the Conversation entity.
func (ccu *ConversationContentUpdate) ClearConversation() *ConversationContentUpdate {
	ccu.mutation.ClearConversation()
	return ccu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ccu *ConversationContentUpdate) Save(ctx context.Context) (int, error) {
	ccu.defaults()
	return withHooks(ctx, ccu.sqlSave, ccu.mutation, ccu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ccu *ConversationContentUpdate) SaveX(ctx context.Context) int {
	affected, err := ccu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ccu *ConversationContentUpdate) Exec(ctx context.Context) error {
	_, err := ccu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ccu *ConversationContentUpdate) ExecX(ctx context.Context) {
	if err := ccu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ccu *ConversationContentUpdate) defaults() {
	if _, ok := ccu.mutation.UpdatedAt(); !ok {
		v := conversationcontent.UpdateDefaultUpdatedAt()
		ccu.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ccu *ConversationContentUpdate) check() error {
	if ccu.mutation.ConversationCleared() && len(ccu.mutation.ConversationIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "ConversationContent.conversation"`)
	}
	return nil
}

func (ccu *ConversationContentUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ccu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(conversationcontent.Table, conversationcontent.Columns, sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt))
	if ps := ccu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ccu.mutation.UpdatedAt(); ok {
		_spec.SetField(conversationcontent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ccu.mutation.Content(); ok {
		_spec.SetField(conversationcontent.FieldContent, field.TypeString, value)
	}
	if value, ok := ccu.mutation.GetType(); ok {
		_spec.SetField(conversationcontent.FieldType, field.TypeInt8, value)
	}
	if value, ok := ccu.mutation.AddedType(); ok {
		_spec.AddField(conversationcontent.FieldType, field.TypeInt8, value)
	}
	if ccu.mutation.ConversationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversationcontent.ConversationTable,
			Columns: []string{conversationcontent.ConversationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ccu.mutation.ConversationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversationcontent.ConversationTable,
			Columns: []string{conversationcontent.ConversationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ccu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{conversationcontent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ccu.mutation.done = true
	return n, nil
}

// ConversationContentUpdateOne is the builder for updating a single ConversationContent entity.
type ConversationContentUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ConversationContentMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (ccuo *ConversationContentUpdateOne) SetUpdatedAt(t time.Time) *ConversationContentUpdateOne {
	ccuo.mutation.SetUpdatedAt(t)
	return ccuo
}

// SetConversationID sets the "conversation_id" field.
func (ccuo *ConversationContentUpdateOne) SetConversationID(i int) *ConversationContentUpdateOne {
	ccuo.mutation.SetConversationID(i)
	return ccuo
}

// SetNillableConversationID sets the "conversation_id" field if the given value is not nil.
func (ccuo *ConversationContentUpdateOne) SetNillableConversationID(i *int) *ConversationContentUpdateOne {
	if i != nil {
		ccuo.SetConversationID(*i)
	}
	return ccuo
}

// SetContent sets the "content" field.
func (ccuo *ConversationContentUpdateOne) SetContent(s string) *ConversationContentUpdateOne {
	ccuo.mutation.SetContent(s)
	return ccuo
}

// SetNillableContent sets the "content" field if the given value is not nil.
func (ccuo *ConversationContentUpdateOne) SetNillableContent(s *string) *ConversationContentUpdateOne {
	if s != nil {
		ccuo.SetContent(*s)
	}
	return ccuo
}

// SetType sets the "type" field.
func (ccuo *ConversationContentUpdateOne) SetType(i int8) *ConversationContentUpdateOne {
	ccuo.mutation.ResetType()
	ccuo.mutation.SetType(i)
	return ccuo
}

// SetNillableType sets the "type" field if the given value is not nil.
func (ccuo *ConversationContentUpdateOne) SetNillableType(i *int8) *ConversationContentUpdateOne {
	if i != nil {
		ccuo.SetType(*i)
	}
	return ccuo
}

// AddType adds i to the "type" field.
func (ccuo *ConversationContentUpdateOne) AddType(i int8) *ConversationContentUpdateOne {
	ccuo.mutation.AddType(i)
	return ccuo
}

// SetConversation sets the "conversation" edge to the Conversation entity.
func (ccuo *ConversationContentUpdateOne) SetConversation(c *Conversation) *ConversationContentUpdateOne {
	return ccuo.SetConversationID(c.ID)
}

// Mutation returns the ConversationContentMutation object of the builder.
func (ccuo *ConversationContentUpdateOne) Mutation() *ConversationContentMutation {
	return ccuo.mutation
}

// ClearConversation clears the "conversation" edge to the Conversation entity.
func (ccuo *ConversationContentUpdateOne) ClearConversation() *ConversationContentUpdateOne {
	ccuo.mutation.ClearConversation()
	return ccuo
}

// Where appends a list predicates to the ConversationContentUpdate builder.
func (ccuo *ConversationContentUpdateOne) Where(ps ...predicate.ConversationContent) *ConversationContentUpdateOne {
	ccuo.mutation.Where(ps...)
	return ccuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ccuo *ConversationContentUpdateOne) Select(field string, fields ...string) *ConversationContentUpdateOne {
	ccuo.fields = append([]string{field}, fields...)
	return ccuo
}

// Save executes the query and returns the updated ConversationContent entity.
func (ccuo *ConversationContentUpdateOne) Save(ctx context.Context) (*ConversationContent, error) {
	ccuo.defaults()
	return withHooks(ctx, ccuo.sqlSave, ccuo.mutation, ccuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ccuo *ConversationContentUpdateOne) SaveX(ctx context.Context) *ConversationContent {
	node, err := ccuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ccuo *ConversationContentUpdateOne) Exec(ctx context.Context) error {
	_, err := ccuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ccuo *ConversationContentUpdateOne) ExecX(ctx context.Context) {
	if err := ccuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ccuo *ConversationContentUpdateOne) defaults() {
	if _, ok := ccuo.mutation.UpdatedAt(); !ok {
		v := conversationcontent.UpdateDefaultUpdatedAt()
		ccuo.mutation.SetUpdatedAt(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (ccuo *ConversationContentUpdateOne) check() error {
	if ccuo.mutation.ConversationCleared() && len(ccuo.mutation.ConversationIDs()) > 0 {
		return errors.New(`ent: clearing a required unique edge "ConversationContent.conversation"`)
	}
	return nil
}

func (ccuo *ConversationContentUpdateOne) sqlSave(ctx context.Context) (_node *ConversationContent, err error) {
	if err := ccuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(conversationcontent.Table, conversationcontent.Columns, sqlgraph.NewFieldSpec(conversationcontent.FieldID, field.TypeInt))
	id, ok := ccuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "ConversationContent.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ccuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, conversationcontent.FieldID)
		for _, f := range fields {
			if !conversationcontent.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != conversationcontent.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ccuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ccuo.mutation.UpdatedAt(); ok {
		_spec.SetField(conversationcontent.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ccuo.mutation.Content(); ok {
		_spec.SetField(conversationcontent.FieldContent, field.TypeString, value)
	}
	if value, ok := ccuo.mutation.GetType(); ok {
		_spec.SetField(conversationcontent.FieldType, field.TypeInt8, value)
	}
	if value, ok := ccuo.mutation.AddedType(); ok {
		_spec.AddField(conversationcontent.FieldType, field.TypeInt8, value)
	}
	if ccuo.mutation.ConversationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversationcontent.ConversationTable,
			Columns: []string{conversationcontent.ConversationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ccuo.mutation.ConversationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   conversationcontent.ConversationTable,
			Columns: []string{conversationcontent.ConversationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(conversation.FieldID, field.TypeInt),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &ConversationContent{config: ccuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ccuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{conversationcontent.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ccuo.mutation.done = true
	return _node, nil
}
