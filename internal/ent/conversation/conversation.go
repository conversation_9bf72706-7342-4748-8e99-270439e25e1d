// Code generated by ent, DO NOT EDIT.

package conversation

import (
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the conversation type in the database.
	Label = "conversation"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldAiAgentID holds the string denoting the ai_agent_id field in the database.
	FieldAiAgentID = "ai_agent_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldAiAgentConversationID holds the string denoting the ai_agent_conversation_id field in the database.
	FieldAiAgentConversationID = "ai_agent_conversation_id"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// EdgeContents holds the string denoting the contents edge name in mutations.
	EdgeContents = "contents"
	// EdgeAiAgent holds the string denoting the ai_agent edge name in mutations.
	EdgeAiAgent = "ai_agent"
	// Table holds the table name of the conversation in the database.
	Table = "conversations"
	// ContentsTable is the table that holds the contents relation/edge.
	ContentsTable = "conversation_contents"
	// ContentsInverseTable is the table name for the ConversationContent entity.
	// It exists in this package in order to avoid circular dependency with the "conversationcontent" package.
	ContentsInverseTable = "conversation_contents"
	// ContentsColumn is the table column denoting the contents relation/edge.
	ContentsColumn = "conversation_id"
	// AiAgentTable is the table that holds the ai_agent relation/edge.
	AiAgentTable = "conversations"
	// AiAgentInverseTable is the table name for the AiAgent entity.
	// It exists in this package in order to avoid circular dependency with the "aiagent" package.
	AiAgentInverseTable = "ai_agents"
	// AiAgentColumn is the table column denoting the ai_agent relation/edge.
	AiAgentColumn = "ai_agent_id"
)

// Columns holds all SQL columns for conversation fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldAiAgentID,
	FieldUserID,
	FieldAiAgentConversationID,
	FieldName,
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

var (
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
)

// OrderOption defines the ordering options for the Conversation queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByAiAgentID orders the results by the ai_agent_id field.
func ByAiAgentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAiAgentID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByAiAgentConversationID orders the results by the ai_agent_conversation_id field.
func ByAiAgentConversationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAiAgentConversationID, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByContentsCount orders the results by contents count.
func ByContentsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newContentsStep(), opts...)
	}
}

// ByContents orders the results by contents terms.
func ByContents(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newContentsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAiAgentField orders the results by ai_agent field.
func ByAiAgentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAiAgentStep(), sql.OrderByField(field, opts...))
	}
}
func newContentsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ContentsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ContentsTable, ContentsColumn),
	)
}
func newAiAgentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AiAgentInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, AiAgentTable, AiAgentColumn),
	)
}
