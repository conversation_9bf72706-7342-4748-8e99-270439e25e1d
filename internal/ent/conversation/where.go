// Code generated by ent, DO NOT EDIT.

package conversation

import (
	"bole-ai/internal/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id int) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldUpdatedAt, v))
}

// AiAgentID applies equality check predicate on the "ai_agent_id" field. It's identical to AiAgentIDEQ.
func AiAgentID(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldAiAgentID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldUserID, v))
}

// AiAgentConversationID applies equality check predicate on the "ai_agent_conversation_id" field. It's identical to AiAgentConversationIDEQ.
func AiAgentConversationID(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldAiAgentConversationID, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldName, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldUpdatedAt, v))
}

// AiAgentIDEQ applies the EQ predicate on the "ai_agent_id" field.
func AiAgentIDEQ(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldAiAgentID, v))
}

// AiAgentIDNEQ applies the NEQ predicate on the "ai_agent_id" field.
func AiAgentIDNEQ(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldAiAgentID, v))
}

// AiAgentIDIn applies the In predicate on the "ai_agent_id" field.
func AiAgentIDIn(vs ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldAiAgentID, vs...))
}

// AiAgentIDNotIn applies the NotIn predicate on the "ai_agent_id" field.
func AiAgentIDNotIn(vs ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldAiAgentID, vs...))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...int) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v int) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldUserID, v))
}

// AiAgentConversationIDEQ applies the EQ predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDEQ(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDNEQ applies the NEQ predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDNEQ(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDIn applies the In predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDIn(vs ...string) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldAiAgentConversationID, vs...))
}

// AiAgentConversationIDNotIn applies the NotIn predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDNotIn(vs ...string) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldAiAgentConversationID, vs...))
}

// AiAgentConversationIDGT applies the GT predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDGT(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDGTE applies the GTE predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDGTE(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDLT applies the LT predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDLT(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDLTE applies the LTE predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDLTE(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDContains applies the Contains predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDContains(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldContains(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDHasPrefix applies the HasPrefix predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDHasPrefix(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldHasPrefix(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDHasSuffix applies the HasSuffix predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDHasSuffix(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldHasSuffix(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDEqualFold applies the EqualFold predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDEqualFold(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEqualFold(FieldAiAgentConversationID, v))
}

// AiAgentConversationIDContainsFold applies the ContainsFold predicate on the "ai_agent_conversation_id" field.
func AiAgentConversationIDContainsFold(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldContainsFold(FieldAiAgentConversationID, v))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Conversation {
	return predicate.Conversation(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Conversation {
	return predicate.Conversation(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Conversation {
	return predicate.Conversation(sql.FieldContainsFold(FieldName, v))
}

// HasContents applies the HasEdge predicate on the "contents" edge.
func HasContents() predicate.Conversation {
	return predicate.Conversation(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ContentsTable, ContentsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasContentsWith applies the HasEdge predicate on the "contents" edge with a given conditions (other predicates).
func HasContentsWith(preds ...predicate.ConversationContent) predicate.Conversation {
	return predicate.Conversation(func(s *sql.Selector) {
		step := newContentsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAiAgent applies the HasEdge predicate on the "ai_agent" edge.
func HasAiAgent() predicate.Conversation {
	return predicate.Conversation(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, AiAgentTable, AiAgentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAiAgentWith applies the HasEdge predicate on the "ai_agent" edge with a given conditions (other predicates).
func HasAiAgentWith(preds ...predicate.AiAgent) predicate.Conversation {
	return predicate.Conversation(func(s *sql.Selector) {
		step := newAiAgentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Conversation) predicate.Conversation {
	return predicate.Conversation(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Conversation) predicate.Conversation {
	return predicate.Conversation(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Conversation) predicate.Conversation {
	return predicate.Conversation(sql.NotPredicates(p))
}
