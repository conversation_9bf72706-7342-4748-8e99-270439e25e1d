// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// AdminUser is the predicate function for adminuser builders.
type AdminUser func(*sql.Selector)

// AiAgent is the predicate function for aiagent builders.
type AiAgent func(*sql.Selector)

// Batch is the predicate function for batch builders.
type Batch func(*sql.Selector)

// Conversation is the predicate function for conversation builders.
type Conversation func(*sql.Selector)

// ConversationContent is the predicate function for conversationcontent builders.
type ConversationContent func(*sql.Selector)

// FrequentAskQuestion is the predicate function for frequentaskquestion builders.
type FrequentAskQuestion func(*sql.Selector)

// InternalMessage is the predicate function for internalmessage builders.
type InternalMessage func(*sql.Selector)

// Project is the predicate function for project builders.
type Project func(*sql.Selector)

// Screenplay is the predicate function for screenplay builders.
type Screenplay func(*sql.Selector)

// TestCode is the predicate function for testcode builders.
type TestCode func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)

// UserInternalMessage is the predicate function for userinternalmessage builders.
type UserInternalMessage func(*sql.Selector)
