package cache

import (
	"bole-ai/internal/model"
	"context"
	goRedis "github.com/go-redis/redis/v8"
)

func NewRedisClient(conf model.Redis) (*goRedis.Client, error) {
	rc := goRedis.NewClient(&goRedis.Options{
		Addr:        conf.Addr,     // addr
		Username:    conf.Username, // 用户名
		Password:    conf.Password, // 密码
		DB:          conf.DB,       // 连接的database库
		IdleTimeout: 300,           // 默认Idle超时时间
		PoolSize:    50,            // 连接池
	})

	if _, err := rc.Ping(context.Background()).Result(); err != nil {
		return nil, err
	}
	return rc, nil
}
