package model

import (
	"time"
)

type Conf struct {
	Http  Http  `yaml:"http" json:"http"`
	Mysql Mysql `yaml:"mysql" json:"mysql"`
	Redis Redis `yaml:"redis" json:"redis"`
	Chat  Chat  `yaml:"chat" json:"chat"`
	Smtp  Smtp  `yaml:"smtp" json:"smtp"`
}
type Chat struct {
	Planner    ChatKey `yaml:"planner" json:"planner"`       // 策划
	Playwright ChatKey `yaml:"playwright" json:"playwright"` //编剧
	Editor     ChatKey `yaml:"editor" json:"editor"`         //编辑
}

type ChatKey struct {
	Gemini string `yaml:"gemini" json:"gemini"`
	GLM    string `yaml:"glm" json:"glm"`
}

type Mysql struct {
	Host     string `yaml:"host" json:"host"`         // 服务器地址
	Port     string `yaml:"port" json:"port"`         // 端口
	Database string `yaml:"database" json:"database"` // 数据库名
	Username string `yaml:"username" json:"username"` // 数据库用户名
	Password string `yaml:"password" json:"password"` // 数据库密码
}

type Redis struct {
	DB       int    `yaml:"db" json:"db"`
	Addr     string `yaml:"addr" json:"addr"` // ip:port
	Username string `yaml:"username" json:"username"`
	Password string `yaml:"password" json:"password"`
}

type Jwt struct {
	Signature string        `yaml:"signature" json:"signature"` // jwt签名
	Duration  time.Duration `yaml:"duration" json:"duration"`   // 有效期
}

type Http struct {
	Debug   bool   `json:"debug"`
	Addr    string `json:"addr"`
	BaseURL string `json:"base_url" mapstructure:"base_url"`
	Jwt     Jwt    `yaml:"jwt" json:"jwt"`
}

type Smtp struct {
	From     string `yaml:"from" json:"from"`
	Password string `yaml:"password" json:"password"`
	Host     string `yaml:"host" json:"host"`
	Port     int    `yaml:"port" json:"port"`
	Company  string `yaml:"company" json:"company"`
}
