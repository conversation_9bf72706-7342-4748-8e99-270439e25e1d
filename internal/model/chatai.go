package model

type ChatRole int
type ChatG int

const (
	ChatRolePlanner ChatRole = iota + 1
	ChatRolePlaywright
	ChatRoleEditor
)

const (
	ChatGemini ChatG = iota + 1
	ChatGLM
)

type Payload struct {
	Inputs         map[string]any `json:"inputs"`
	Query          string         `json:"query"`
	ResponseMode   string         `json:"response_mode"`
	User           string         `json:"user"`
	ConversationId string         `json:"conversation_id"`
}
