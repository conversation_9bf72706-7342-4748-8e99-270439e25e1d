package model

type AiAgentConversation struct {
	Event          string `json:"event"`
	ConversationID string `json:"conversation_id"`
	MessageID      string `json:"message_id"`
	CreatedAt      int    `json:"created_at"`
	TaskID         string `json:"task_id"`
	WorkflowRunID  string `json:"workflow_run_id"`
	Data           struct {
		ID             string `json:"id"`
		WorkflowID     string `json:"workflow_id"`
		SequenceNumber int    `json:"sequence_number"`
		Status         string `json:"status"`
		Outputs        struct {
			Answer string `json:"answer"`
		} `json:"outputs"`
		Error       interface{} `json:"error"`
		ElapsedTime float64     `json:"elapsed_time"`
		TotalTokens int         `json:"total_tokens"`
		TotalSteps  int         `json:"total_steps"`
		CreatedBy   struct {
			ID   string `json:"id"`
			User string `json:"user"`
		} `json:"created_by"`
		CreatedAt       int           `json:"created_at"`
		FinishedAt      int           `json:"finished_at"`
		ExceptionsCount int           `json:"exceptions_count"`
		Files           []interface{} `json:"files"`
	} `json:"data"`
}
