package model

import (
	"bole-ai/internal/ent"
)

type UserInfo struct {
	*ent.User
	Authorities map[string]interface{} `json:"authorities"`
	Authority   map[string]interface{} `json:"authority"`
}

type User struct {
	Username        string `json:"username"`
	Email           string `json:"email"`
	Password        string `json:"password"`
	ConfirmPassword string `json:"confirm_password"`
	TestCode        string `json:"test_code"`
}
