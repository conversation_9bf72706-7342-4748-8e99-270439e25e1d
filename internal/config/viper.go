/**
 * @Author: 1cool
 * @Author: 1cool.vip
 * @Date: 2023/5/10 22:37
 * @Desc:
 */

package config

import (
	"bole-ai/internal/model"
	"fmt"
	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"log"
)

func NewViperConfig(path string) *model.Conf {
	v := viper.New()
	v.SetConfigType("yaml")
	v.AddConfigPath(path)

	err := v.ReadInConfig()

	if err != nil {
		log.Fatalln(fmt.Errorf("Fatal error config file: %s \n", err))
	}
	var conf model.Conf

	v.WatchConfig()

	v.OnConfigChange(func(e fsnotify.Event) {
		fmt.Println("config file changed:", e.Name)
		if err = v.Unmarshal(&conf); err != nil {
			fmt.Println(err)
		}
	})

	if err = v.Unmarshal(&conf); err != nil {
		fmt.Println(err)
	}

	return &conf
}
